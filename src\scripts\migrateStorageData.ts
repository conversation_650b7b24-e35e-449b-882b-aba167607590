/**
 * Migration script to initialize storage data for existing organizations
 * This script should be run once to set up storage tracking for existing data
 */

import { connectDB } from '@/Utility/db';
import { Organization } from '@/models/organization';
import { Project } from '@/models/Project';
import { Task } from '@/models/Task';
import { StorageUsage } from '@/models/StorageUsage';
import { Subscription } from '@/models/Subscription';
import { v2 as cloudinary } from 'cloudinary';

// Migration constants
const STORAGE_CONSTANTS = {
  FREE_STORAGE_LIMIT: 52428800, // 50MB in bytes
  DEFAULT_FILE_TYPE: 'application/octet-stream',
  BATCH_SIZE: 100, // Process files in batches
} as const;

// Configure Cloudinary
cloudinary.config({
  cloud_name: process.env.CLOUDINARY_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
});

/**
 * Get file size and metadata from Cloudinary
 */
async function getCloudinaryFileInfo(publicId: string): Promise<{
  size: number;
  format: string;
  resourceType: string;
}> {
  try {
    const result = await cloudinary.api.resource(publicId);
    return {
      size: result.bytes || 0,
      format: result.format || 'unknown',
      resourceType: result.resource_type || 'auto',
    };
  } catch (error) {
    console.warn(`Could not get info for file ${publicId}:`, error);
    return {
      size: 0,
      format: 'unknown',
      resourceType: 'auto',
    };
  }
}

/**
 * Extract public_id from Cloudinary URL more reliably
 */
function extractPublicIdFromUrl(url: string): string {
  try {
    // Handle different Cloudinary URL formats
    const urlParts = url.split('/');
    const uploadIndex = urlParts.findIndex(part => part === 'upload');

    if (uploadIndex === -1) {
      // Fallback to simple extraction
      const fileWithExt = urlParts[urlParts.length - 1];
      return fileWithExt.split('.')[0];
    }

    // Skip version and transformation parameters
    let pathAfterUpload = urlParts.slice(uploadIndex + 1);

    // Remove version if present (starts with 'v' followed by numbers)
    if (pathAfterUpload[0] && /^v\d+$/.test(pathAfterUpload[0])) {
      pathAfterUpload = pathAfterUpload.slice(1);
    }

    // Join the remaining path and remove extension
    const fullPath = pathAfterUpload.join('/');
    const lastDotIndex = fullPath.lastIndexOf('.');

    return lastDotIndex > 0 ? fullPath.substring(0, lastDotIndex) : fullPath;
  } catch (error) {
    console.warn(`Error extracting public_id from URL ${url}:`, error);
    // Fallback to simple extraction
    const urlParts = url.split('/');
    const fileWithExt = urlParts[urlParts.length - 1];
    return fileWithExt.split('.')[0];
  }
}

/**
 * Get MIME type from file format
 */
function getMimeTypeFromFormat(format: string, resourceType: string): string {
  const mimeTypes: Record<string, string> = {
    // Images
    jpg: 'image/jpeg',
    jpeg: 'image/jpeg',
    png: 'image/png',
    gif: 'image/gif',
    webp: 'image/webp',
    svg: 'image/svg+xml',
    bmp: 'image/bmp',
    tiff: 'image/tiff',

    // Videos
    mp4: 'video/mp4',
    avi: 'video/avi',
    mov: 'video/quicktime',
    wmv: 'video/x-ms-wmv',
    webm: 'video/webm',

    // Documents
    pdf: 'application/pdf',
    doc: 'application/msword',
    docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    xls: 'application/vnd.ms-excel',
    xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    ppt: 'application/vnd.ms-powerpoint',
    pptx: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    txt: 'text/plain',
    csv: 'text/csv',

    // Audio
    mp3: 'audio/mpeg',
    wav: 'audio/wav',
    ogg: 'audio/ogg',
    flac: 'audio/flac',

    // Archives
    zip: 'application/zip',
    rar: 'application/x-rar-compressed',
    '7z': 'application/x-7z-compressed',
    tar: 'application/x-tar',
    gz: 'application/gzip',
  };

  const mimeType = mimeTypes[format.toLowerCase()];
  if (mimeType) return mimeType;

  // Fallback based on resource type
  switch (resourceType) {
    case 'image':
      return 'image/jpeg';
    case 'video':
      return 'video/mp4';
    case 'audio':
      return 'audio/mpeg';
    default:
      return 'application/octet-stream';
  }
}

/**
 * Validate migration prerequisites
 */
async function validateMigrationPrerequisites(): Promise<{
  isValid: boolean;
  errors: string[];
}> {
  const errors: string[] = [];

  try {
    // Check database connection
    await connectDB();

    // Check if required environment variables are set
    if (
      !process.env.CLOUDINARY_NAME ||
      !process.env.CLOUDINARY_API_KEY ||
      !process.env.CLOUDINARY_API_SECRET
    ) {
      errors.push('Missing required Cloudinary environment variables');
    }

    // Check if models are accessible
    const orgCount = await Organization.countDocuments();
    if (orgCount === 0) {
      errors.push('No organizations found in database');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  } catch (error) {
    errors.push(`Database connection failed: ${error}`);
    return {
      isValid: false,
      errors,
    };
  }
}

/**
 * Migrate project files to storage tracking
 */
async function migrateProjectFiles(organizationId: string): Promise<number> {
  // eslint-disable-next-line no-console
  console.log(`Migrating project files for organization ${organizationId}...`);

  const projects = await Project.find({ organizationId });
  let totalSize = 0;

  for (const project of projects) {
    if (project.files && project.files.length > 0) {
      for (const file of project.files) {
        try {
          // Check if already tracked
          const existingUsage = await StorageUsage.findOne({
            organizationId,
            fileId: file.public_id,
          });

          if (existingUsage) {
            // eslint-disable-next-line no-console
            console.log(`File ${file.public_id} already tracked, skipping...`);
            continue;
          }

          // Get file info from Cloudinary
          const fileInfo = await getCloudinaryFileInfo(file.public_id);

          if (fileInfo.size > 0) {
            const mimeType = getMimeTypeFromFormat(fileInfo.format, fileInfo.resourceType);

            await StorageUsage.create({
              organizationId,
              fileId: file.public_id,
              fileName: file.name,
              fileSize: fileInfo.size,
              fileType: mimeType,
              uploadedBy: project.ownerId,
              resourceType: 'project',
              resourceId: project._id,
              cloudinaryUrl: file.url,
              isDeleted: false,
            });

            totalSize += fileInfo.size;
            // eslint-disable-next-line no-console
            console.log(`Tracked file: ${file.name} (${fileInfo.size} bytes)`);
          }
        } catch (error) {
          // eslint-disable-next-line no-console
          console.error(`Error processing file ${file.name}:`, error);
        }
      }
    }
  }

  return totalSize;
}

/**
 * Migrate task comment attachments to storage tracking
 */
async function migrateTaskFiles(organizationId: string): Promise<number> {
  // eslint-disable-next-line no-console
  console.log(`Migrating task files for organization ${organizationId}...`);

  const tasks = await Task.find({ organizationId });
  let totalSize = 0;

  for (const task of tasks) {
    if (task.comments && task.comments.length > 0) {
      for (const comment of task.comments) {
        if (comment.attachments && comment.attachments.length > 0) {
          for (const attachment of comment.attachments) {
            try {
              // Extract public_id from URL using improved function
              const publicId = extractPublicIdFromUrl(attachment.url);

              // Check if already tracked
              const existingUsage = await StorageUsage.findOne({
                organizationId,
                fileId: publicId,
              });

              if (existingUsage) {
                // eslint-disable-next-line no-console
                console.log(`File ${publicId} already tracked, skipping...`);
                continue;
              }

              // Get file info from Cloudinary
              const fileInfo = await getCloudinaryFileInfo(publicId);

              if (fileInfo.size > 0) {
                const mimeType = getMimeTypeFromFormat(fileInfo.format, fileInfo.resourceType);

                await StorageUsage.create({
                  organizationId,
                  fileId: publicId,
                  fileName: attachment.filename,
                  fileSize: fileInfo.size,
                  fileType: mimeType,
                  uploadedBy: comment.userId,
                  resourceType: 'task',
                  resourceId: task._id,
                  cloudinaryUrl: attachment.url,
                  isDeleted: false,
                });

                totalSize += fileInfo.size;
                // eslint-disable-next-line no-console
                console.log(`Tracked attachment: ${attachment.filename} (${fileInfo.size} bytes)`);
              }
            } catch (error) {
              // eslint-disable-next-line no-console
              console.error(`Error processing attachment ${attachment.filename}:`, error);
            }
          }
        }
      }
    }
  }

  return totalSize;
}

/**
 * Initialize subscription for organization
 */
async function initializeSubscription(organizationId: string): Promise<void> {
  const existingSubscription = await Subscription.findOne({ organizationId });

  if (!existingSubscription) {
    await Subscription.create({
      organizationId,
      plan: 'free',
      status: 'active',
      storageLimit: STORAGE_CONSTANTS.FREE_STORAGE_LIMIT,
      totalStoragePurchased: 0,
      userLimit: 3,
      projectLimit: 10,
      integrationLimit: 2,
      billingCycle: 'lifetime',
      amount: 0,
      currency: 'INR',
      currentPeriodStart: new Date(),
      currentPeriodEnd: null,
    });
    // eslint-disable-next-line no-console
    console.log(`Created subscription for organization ${organizationId}`);
  }
}

/**
 * Update organization storage usage and sync with subscription
 */
async function updateOrganizationStorage(organizationId: string, totalUsed: number): Promise<void> {
  // Update organization
  await Organization.findByIdAndUpdate(organizationId, {
    storageUsed: totalUsed,
    storageLimit: STORAGE_CONSTANTS.FREE_STORAGE_LIMIT,
    lastStorageCalculated: new Date(),
  });

  // Update subscription storage info
  const subscription = await Subscription.findOne({ organizationId });
  if (subscription) {
    await Subscription.findByIdAndUpdate(subscription._id, {
      storageUsed: totalUsed,
    });
  }

  // eslint-disable-next-line no-console
  console.log(`Updated organization storage: ${totalUsed} bytes used`);
}

/**
 * Main migration function with improved error handling and validation
 */
export async function migrateStorageData(): Promise<{
  success: boolean;
  migratedOrganizations: number;
  totalFilesProcessed: number;
  totalStorageMigrated: number;
  errors: string[];
}> {
  const result = {
    success: false,
    migratedOrganizations: 0,
    totalFilesProcessed: 0,
    totalStorageMigrated: 0,
    errors: [] as string[],
  };

  try {
    // Validate prerequisites before starting migration
    // eslint-disable-next-line no-console
    console.log('Validating migration prerequisites...');
    const validation = await validateMigrationPrerequisites();

    if (!validation.isValid) {
      result.errors.push(...validation.errors);
      throw new Error(`Migration prerequisites failed: ${validation.errors.join(', ')}`);
    }

    // eslint-disable-next-line no-console
    console.log('Starting storage data migration...');

    const organizations = await Organization.find({});
    // eslint-disable-next-line no-console
    console.log(`Found ${organizations.length} organizations to migrate`);

    for (const org of organizations) {
      // eslint-disable-next-line no-console
      console.log(`\n--- Migrating organization: ${org.name} (${org._id}) ---`);

      try {
        // Validate organization has required fields
        if (!org._id || !org.name) {
          const error = `Invalid organization data: ${org._id}`;
          result.errors.push(error);
          continue;
        }

        // Initialize subscription
        await initializeSubscription(org._id.toString());

        // Migrate project files
        const projectFileSize = await migrateProjectFiles(org._id.toString());

        // Migrate task files
        const taskFileSize = await migrateTaskFiles(org._id.toString());

        // Calculate total storage used
        const totalUsed = projectFileSize + taskFileSize;

        // Update organization storage info
        await updateOrganizationStorage(org._id.toString(), totalUsed);

        result.migratedOrganizations++;
        result.totalStorageMigrated += totalUsed;

        // eslint-disable-next-line no-console
        console.log(`Migration completed for ${org.name}: ${totalUsed} bytes total`);
      } catch (error) {
        const errorMsg = `Error migrating organization ${org.name}: ${error}`;
        result.errors.push(errorMsg);
        // eslint-disable-next-line no-console
        console.error(errorMsg);
      }
    }

    result.success = result.errors.length === 0;
    // eslint-disable-next-line no-console
    console.log('\nStorage data migration completed!');
    // eslint-disable-next-line no-console
    console.log(
      `Summary: ${result.migratedOrganizations} organizations, ${result.totalStorageMigrated} bytes total`
    );

    return result;
  } catch (error) {
    const errorMsg = `Migration failed: ${error}`;
    result.errors.push(errorMsg);
    // eslint-disable-next-line no-console
    console.error(errorMsg);
    throw error;
  }
}

// Run migration if this file is executed directly
if (require.main === module) {
  migrateStorageData()
    .then(result => {
      // eslint-disable-next-line no-console
      console.log('Migration completed successfully');
      // eslint-disable-next-line no-console
      console.log('Result:', result);
      process.exit(result.success ? 0 : 1);
    })
    .catch(error => {
      // eslint-disable-next-line no-console
      console.error('Migration failed:', error);
      process.exit(1);
    });
}
