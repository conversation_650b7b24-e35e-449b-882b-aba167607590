import { uploadToCloudinary } from '@/Utility/cloudinary';
import { connectDB } from '@/Utility/db';
import { Project } from '@/models/Project';
import { ProjectMembers } from '@/models/ProjectMembers';
import { Task } from '@/models/Task';
import { User } from '@/models/User';
import { NotificationService } from '@/services/Notification.service';

const createProject = async (data: any, id: string, organizationId: string) => {
  try {
    await connectDB();
    const { name, description, status, priority, tasks, files } = data;
    // create task
    const formatedTasks = tasks.map((task: any) => {
      return {
        name: task.name,
        description: task.description,
        assignedTo: task.assignedTo,
        status: task.status,
        priority: task.priority,
        dueDate: task.dueDate,
        startDate: task.startDate,
        estimatedTime: task.estimatedTime,
        loggedTime: task.loggedTime,
        subtasks: task.subtasks,
        comments: task.comments,
        createdBy: id,
        organizationId: organizationId,
      };
    });
    const createdTasks = await Task.insertMany(formatedTasks);
    // upload files to cloudinary with storage tracking
    interface UploadedFile {
      secure_url: string;
      public_id: string;
    }
    const uploadedFiles: UploadedFile[] = [];
    for (const file of files) {
      const uploadResult = await uploadToCloudinary(file.data, 'taskmantra/projects', {
        organizationId,
        checkStorageLimit: true,
        fileInfo: {
          fileName: file.name,
          fileSize: 0,
          fileType: file.type || 'application/octet-stream',
          uploadedBy: id,
          resourceType: 'project',
          resourceId: '',
        },
      });

      if (uploadResult.error) {
        throw new Error(`Failed to upload file ${file.name}: ${uploadResult.error}`);
      }

      uploadedFiles.push({
        secure_url: uploadResult.secure_url,
        public_id: uploadResult.public_id,
      });
    }
    const formatedFiles = uploadedFiles.map((file, index: number) => ({
      url: file.secure_url,
      public_id: file.public_id,
      name: files[index].name,
    }));
    // create project
    const newProject = new Project({
      name,
      description,
      ownerId: id,
      status,
      priority,
      history: [],
      tasks: createdTasks.map((task: any) => task._id),
      files: formatedFiles,
      organizationId: organizationId,
    });
    const res = await newProject.save();
    // create notification for assignedTo (optimized with timeout)
    const notificationPromises = formatedTasks.flatMap((task: any) =>
      task.assignedTo.map(async (userId: string) => {
        try {
          const user = await User.findById(userId);
          if (user) {
            return NotificationService.createNotification({
              userId: user.id,
              title: 'You have been assigned to a task',
              description: `You have been assigned to "${task.name}" by ${user.name}`,
              type: 'task',
              link: `/tasks?id=${task._id}`,
              metadata: { taskId: task._id, assignedBy: user.name },
            });
          }
        } catch (error) {
          console.error('Error creating notification:', error);
          return null;
        }
      })
    );

    // Execute notifications with timeout
    try {
      await Promise.race([
        Promise.allSettled(notificationPromises),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Notification timeout')), 10000)
        ),
      ]);
    } catch (error) {
      console.error('Notification creation timed out:', error);
      // Continue execution even if notifications fail
    }
    await Task.updateMany(
      { _id: { $in: createdTasks.map((task: any) => task._id) } },
      { $set: { projectId: res._id } }
    );
    const additionalMembers =
      tasks.length > 0
        ? formatedTasks.flatMap((task: any) => {
            if (!Array.isArray(task.assignedTo) || task.assignedTo.length === 0) {
              return [];
            }
            // Create a member entry for each assigned user
            return task.assignedTo.map((userId: string) => ({
              userId,
              role: 'Developer',
              invitedAt: new Date(),
            }));
          })
        : [];
    const uniqueMembers = [
      {
        userId: id,
        role: 'Project Admin',
        invitedAt: new Date(),
        acceptedAt: new Date(),
      },
      ...additionalMembers.filter(
        (member, index, self) => index === self.findIndex(m => m.userId === member.userId)
      ),
    ];

    await new ProjectMembers({
      projectId: res._id,
      members: uniqueMembers,
    }).save();
    return res;
  } catch (error: any) {
    return error;
  }
};
const getAllProjects = async (userId: string) => {
  try {
    await connectDB();
    const projectMembers = await ProjectMembers.find({ members: { $elemMatch: { userId } } });
    const projects = await Project.find({
      _id: { $in: projectMembers.map((project: any) => project.projectId) },
    });
    return projects;
  } catch (error: any) {
    return error;
  }
};

const getProjectById = async (projectId: string, _userId: string) => {
  try {
    await connectDB();
    const project = await Project.findById(projectId).populate({
      path: 'tasks',
      populate: {
        path: 'assignedTo',
      },
    });
    return project;
  } catch (error: any) {
    return error;
  }
};

const updateTask = async (taskId: string, data: any) => {
  try {
    await connectDB();
    const originalTask = await Task.findById(taskId);
    if (!originalTask) {
      throw new Error('Task not found');
    }
    const task = await Task.findByIdAndUpdate(taskId, data, { new: true });

    const originalAssignees = originalTask.assignedTo.map(id => id.toString());
    const newAssignees = data.assignedTo || [];

    const addedAssignees = newAssignees.filter(id => !originalAssignees.includes(id.toString()));
    await Promise.all(
      addedAssignees.map(async (userId: string) => {
        const user = await User.findById(userId);
        if (user) {
          await NotificationService.createNotification({
            userId: user.id,
            title: 'You have been assigned to a task',
            description: `You have been assigned to "${data.name || originalTask.name}" by ${user.name}`,
            type: 'task',
            link: `/tasks?id=${task._id}`,
            metadata: { taskId: task._id, assignedBy: user.name },
          });
        }
      })
    );

    // Optimize notification creation with timeout
    const updateNotificationPromises = originalAssignees
      .filter(userId => newAssignees.includes(userId) || addedAssignees.includes(userId))
      .map(async (userId: string) => {
        try {
          const user = await User.findById(userId);
          if (user) {
            return NotificationService.createNotification({
              userId: user.id,
              title: 'Task has been updated',
              description: `The task "${data.name || originalTask.name}" has been updated`,
              type: 'task',
              link: `/tasks?id=${task._id}`,
              metadata: { taskId: task._id, action: 'updated' },
            });
          }
        } catch (error) {
          console.error('Error creating update notification:', error);
          return null;
        }
      });

    // Execute with timeout
    try {
      await Promise.race([
        Promise.allSettled(updateNotificationPromises),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Update notification timeout')), 5000)
        ),
      ]);
    } catch (error) {
      console.error('Update notification creation timed out:', error);
      // Continue execution even if notifications fail
    }

    return task;
  } catch (error: any) {
    return error;
  }
};

export { createProject, getAllProjects, getProjectById, updateTask };
