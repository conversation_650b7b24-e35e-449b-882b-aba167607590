'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  CheckSquare,
  Flag,
  Diamond,
  FileText,
  Paperclip,
  Calendar,
  Users,
  ChevronDown,
  ChevronUp,
  Folder,
  Clock,
  Activity,
  MessageSquare,
  ListOrdered,
  Type,
  Search,
  Grid3X3,
  List,
} from 'lucide-react';
import { Input } from '@/components/ui/input';

interface NodePaletteProps {
  onAddNode: (type: string, position: { x: number; y: number }) => void;
}

const nodeTypes = [
  {
    type: 'task',
    label: 'Task',
    icon: CheckSquare,
    color: 'bg-blue-500',
    description: 'Standard to-do items',
    category: 'Core',
  },
  {
    type: 'milestone',
    label: 'Milestone',
    icon: Flag,
    color: 'bg-green-500',
    description: 'Key project checkpoints',
    category: 'Core',
  },
  {
    type: 'decision',
    label: 'Decision',
    icon: Diamond,
    color: 'bg-yellow-500',
    description: 'Choice points',
    category: 'Core',
  },
  {
    type: 'note',
    label: 'Note',
    icon: FileText,
    color: 'bg-purple-500',
    description: 'Documentation blocks',
    category: 'Content',
  },
  {
    type: 'resource',
    label: 'Resource',
    icon: Paperclip,
    color: 'bg-orange-500',
    description: 'File attachments',
    category: 'Content',
  },
  {
    type: 'timeline',
    label: 'Timeline',
    icon: Calendar,
    color: 'bg-red-500',
    description: 'Date-specific deadlines',
    category: 'Time',
  },
  {
    type: 'team',
    label: 'Team',
    icon: Users,
    color: 'bg-indigo-500',
    description: 'Person assignments',
    category: 'People',
  },
  {
    type: 'project',
    label: 'Project',
    icon: Folder,
    color: 'bg-blue-600',
    description: 'Project container',
    category: 'Structure',
  },
  {
    type: 'deadline',
    label: 'Deadline',
    icon: Clock,
    color: 'bg-red-600',
    description: 'Countdown timers',
    category: 'Time',
  },
  {
    type: 'status',
    label: 'Status',
    icon: Activity,
    color: 'bg-purple-600',
    description: 'Status tracker',
    category: 'Core',
  },
  {
    type: 'annotation',
    label: 'Annotation',
    icon: MessageSquare,
    color: 'bg-teal-500',
    description: 'Rich text comments',
    category: 'Content',
  },
  {
    type: 'process',
    label: 'Process',
    icon: ListOrdered,
    color: 'bg-cyan-500',
    description: 'Sequential steps',
    category: 'Structure',
  },
  {
    type: 'text',
    label: 'Text',
    icon: Type,
    color: 'bg-gray-500',
    description: 'Custom text labels',
    category: 'Content',
  },
];

type ViewMode = 'grid' | 'list';

export function NodePalette({ onAddNode }: NodePaletteProps) {
  const [isExpanded, setIsExpanded] = useState(true);
  const [recentlyAdded, setRecentlyAdded] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState<ViewMode>('list');

  const handleAddNode = (type: string) => {
    // Position nodes in the center of the viewport with some randomization
    const baseX = 200;
    const baseY = 200;
    const randomOffset = 100;

    const position = {
      x: baseX + (Math.random() - 0.5) * randomOffset,
      y: baseY + (Math.random() - 0.5) * randomOffset,
    };

    onAddNode(type, position);

    setRecentlyAdded(type);
    setTimeout(() => setRecentlyAdded(null), 1000);
  };

  const filteredNodeTypes = nodeTypes.filter(
    nodeType =>
      nodeType.label.toLowerCase().includes(searchQuery.toLowerCase()) ||
      nodeType.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      nodeType.category.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const groupedNodeTypes = filteredNodeTypes.reduce(
    (acc, nodeType) => {
      if (!acc[nodeType.category]) {
        acc[nodeType.category] = [];
      }
      acc[nodeType.category].push(nodeType);
      return acc;
    },
    {} as Record<string, typeof nodeTypes>
  );

  return (
    <div className="h-full w-full theme-surface flex flex-col border-0">
      {/* Header */}
      <div className="p-4 pb-3 border-b theme-border flex-shrink-0 theme-surface-elevated">
        <div className="flex items-center justify-between mb-3">
          <h3 className="font-semibold theme-text-primary text-lg">Node Palette</h3>
          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
              className="theme-button-ghost h-8 w-8 p-0"
            >
              {viewMode === 'grid' ? <List className="h-4 w-4" /> : <Grid3X3 className="h-4 w-4" />}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
              className="theme-button-ghost h-8 w-8 p-0"
            >
              {isExpanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
            </Button>
          </div>
        </div>

        {isExpanded && (
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 theme-text-secondary" />
            <Input
              placeholder="Search nodes..."
              value={searchQuery}
              onChange={e => setSearchQuery(e.target.value)}
              className="theme-input pl-10 h-9"
            />
          </div>
        )}
      </div>

      {/* Content */}
      {isExpanded ? (
        <div className="flex-1 overflow-hidden">
          <div className="h-full theme-scrollbar overflow-y-auto p-4 pt-3 theme-surface">
            {viewMode === 'list' ? (
              <div className="space-y-4">
                {Object.entries(groupedNodeTypes).map(([category, types]) => (
                  <div key={category} className="space-y-2">
                    <h4 className="text-sm font-medium theme-text-secondary uppercase tracking-wide">
                      {category}
                    </h4>
                    <div className="space-y-1">
                      {types.map(nodeType => {
                        const Icon = nodeType.icon;
                        return (
                          <Button
                            key={nodeType.type}
                            variant="ghost"
                            className={`w-full justify-start h-auto p-3 theme-hover-surface transition-all duration-200 ${
                              recentlyAdded === nodeType.type
                                ? 'theme-active-primary ring-2 ring-primary/20'
                                : ''
                            }`}
                            onClick={() => handleAddNode(nodeType.type)}
                          >
                            <div className="flex items-center space-x-3 w-full">
                              <div
                                className={`${nodeType.color} p-2 rounded-lg text-white flex-shrink-0 shadow-sm`}
                              >
                                <Icon className="h-4 w-4" />
                              </div>
                              <div className="text-left flex-1 min-w-0">
                                <div className="font-medium text-sm theme-text-primary">
                                  {nodeType.label}
                                </div>
                                <div className="text-xs theme-text-secondary truncate">
                                  {nodeType.description}
                                </div>
                              </div>
                            </div>
                          </Button>
                        );
                      })}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="grid grid-cols-3 gap-3">
                {filteredNodeTypes.map(nodeType => {
                  const Icon = nodeType.icon;
                  return (
                    <Button
                      key={nodeType.type}
                      variant="ghost"
                      className={`h-20 w-full p-2 flex flex-col items-center justify-center space-y-1 theme-hover-surface transition-all duration-200 ${
                        recentlyAdded === nodeType.type
                          ? 'theme-active-primary ring-2 ring-primary/20'
                          : ''
                      }`}
                      onClick={() => handleAddNode(nodeType.type)}
                      title={`${nodeType.label} - ${nodeType.description}`}
                    >
                      <div className={`${nodeType.color} p-2 rounded-lg text-white shadow-sm`}>
                        <Icon className="h-4 w-4" />
                      </div>
                      <span className="text-xs font-medium theme-text-primary text-center leading-tight">
                        {nodeType.label}
                      </span>
                    </Button>
                  );
                })}
              </div>
            )}

            {filteredNodeTypes.length === 0 && (
              <div className="text-center py-8">
                <div className="theme-text-secondary text-sm">
                  No nodes found matching "{searchQuery}"
                </div>
              </div>
            )}
          </div>
        </div>
      ) : (
        <div className="p-4 pt-3 theme-surface">
          <div className="grid grid-cols-4 gap-2">
            {nodeTypes.slice(0, 8).map(nodeType => {
              const Icon = nodeType.icon;
              return (
                <Button
                  key={nodeType.type}
                  variant="ghost"
                  className={`h-12 w-12 p-0 theme-hover-surface transition-all duration-200 ${
                    recentlyAdded === nodeType.type
                      ? 'theme-active-primary ring-2 ring-primary/20'
                      : ''
                  }`}
                  onClick={() => handleAddNode(nodeType.type)}
                  title={`${nodeType.label} - ${nodeType.description}`}
                >
                  <div className={`${nodeType.color} p-2 rounded-md text-white shadow-sm`}>
                    <Icon className="h-4 w-4" />
                  </div>
                </Button>
              );
            })}
          </div>
          {nodeTypes.length > 8 && (
            <div className="mt-2 text-center">
              <span className="text-xs theme-text-secondary">
                +{nodeTypes.length - 8} more nodes
              </span>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
