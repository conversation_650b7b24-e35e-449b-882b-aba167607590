'use client';

interface BeforeInstallPromptEvent extends Event {
  readonly platforms: string[];
  readonly userChoice: Promise<{
    outcome: 'accepted' | 'dismissed';
    platform: string;
  }>;
  prompt(): Promise<void>;
}

interface PWACapabilities {
  hasServiceWorker: boolean;
  hasPushNotifications: boolean;
  hasBackgroundSync: boolean;
  hasPeriodicSync: boolean;
  hasWebShare: boolean;
  hasBeforeInstallPrompt: boolean;
  hasNotificationAPI: boolean;
  hasStorageAPI: boolean;
  isStandalone: boolean;
  isDisplayModeStandalone: boolean;
  hasFileSystemAccess: boolean;
  hasClipboard: boolean;
  hasWakeLock: boolean;
  hasDeviceMotion: boolean;
  hasGeolocation: boolean;
}

interface NetworkInfo {
  type: string;
  effectiveType: string;
  downlink: number;
  rtt: number;
  saveData: boolean;
}

interface CacheInfo {
  name: string;
  size: number;
  entries: number;
  lastModified: Date;
}

interface PWAAnalyticsEvent {
  event: string;
  data: Record<string, any>;
  timestamp: string;
  sessionId: string;
  userId?: string;
}

interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

// PWA Installation Detection Utilities
export const pwaInstallationUtils = {
  /**
   * Check if PWA is currently installed
   */
  isInstalled(): boolean {
    // iOS Safari check
    if ((navigator as any).standalone) {
      return true;
    }

    // Standard display mode check
    if (window.matchMedia('(display-mode: standalone)').matches) {
      return true;
    }

    // Windows/Android check
    if (window.matchMedia('(display-mode: fullscreen)').matches) {
      return true;
    }

    // Check for minimal-ui mode
    if (window.matchMedia('(display-mode: minimal-ui)').matches) {
      return true;
    }

    return false;
  },

  /**
   * Check if install prompt is available
   */
  canInstall(): boolean {
    return localStorage.getItem('pwa_install_prompt_available') === 'true';
  },

  /**
   * Get installation source
   */
  getInstallationSource(): string {
    if ((navigator as any).standalone) {
      return 'ios_home_screen';
    }

    if (window.matchMedia('(display-mode: standalone)').matches) {
      return 'browser_install';
    }

    if (document.referrer.includes('android-app://')) {
      return 'android_intent';
    }

    return 'unknown';
  },

  /**
   * Check if running in browser tab
   */
  isRunningInBrowser(): boolean {
    return !this.isInstalled();
  },

  /**
   * Get display mode
   */
  getDisplayMode(): string {
    if (window.matchMedia('(display-mode: fullscreen)').matches) {
      return 'fullscreen';
    }
    if (window.matchMedia('(display-mode: standalone)').matches) {
      return 'standalone';
    }
    if (window.matchMedia('(display-mode: minimal-ui)').matches) {
      return 'minimal-ui';
    }
    return 'browser';
  },

  /**
   * Store install prompt reference
   */
  storeInstallPrompt(prompt: BeforeInstallPromptEvent): void {
    localStorage.setItem('pwa_install_prompt_available', 'true');
    localStorage.setItem('pwa_install_platforms', JSON.stringify(prompt.platforms));
  },

  /**
   * Clear install prompt reference
   */
  clearInstallPrompt(): void {
    localStorage.removeItem('pwa_install_prompt_available');
    localStorage.removeItem('pwa_install_platforms');
  },
};

// Service Worker Registration Helpers
export const serviceWorkerUtils = {
  /**
   * Register service worker with error handling
   */
  async register(path: string = '/sw.js', options): Promise<ServiceWorkerRegistration | null> {
    if (!('serviceWorker' in navigator)) {
      return null;
    }

    try {
      const registration = await navigator.serviceWorker.register(path, {
        scope: '/',
        ...options,
      });

      console.log('Service Worker registered successfully:', registration.scope);
      return registration;
    } catch (error) {
      console.error('Service Worker registration failed:', error);
      return null;
    }
  },

  /**
   * Get current service worker registration
   */
  async getCurrentRegistration(): Promise<ServiceWorkerRegistration | null> {
    if (!('serviceWorker' in navigator)) {
      return null;
    }

    try {
      const registration = await navigator.serviceWorker.getRegistration();
      return registration ?? null;
    } catch (error) {
      console.error('Failed to get service worker registration:', error);
      return null;
    }
  },

  /**
   * Unregister service worker
   */
  async unregister(): Promise<boolean> {
    try {
      const registration = await this.getCurrentRegistration();
      if (registration) {
        return await registration.unregister();
      }
      return true;
    } catch (error) {
      console.error('Failed to unregister service worker:', error);
      return false;
    }
  },

  /**
   * Check if service worker is active
   */
  isActive(): boolean {
    return navigator.serviceWorker?.controller !== null;
  },

  /**
   * Wait for service worker to be ready
   */
  async waitForReady(): Promise<ServiceWorkerRegistration | null> {
    if (!('serviceWorker' in navigator)) {
      return null;
    }

    try {
      return await navigator.serviceWorker.ready;
    } catch (error) {
      console.error('Service worker ready promise failed:', error);
      return null;
    }
  },

  /**
   * Send message to service worker
   */
  async postMessage(message: any): Promise<void> {
    if (!navigator.serviceWorker?.controller) {
      throw new Error('No active service worker');
    }

    navigator.serviceWorker.controller.postMessage(message);
  },

  /**
   * Listen for service worker messages
   */
  onMessage(callback: (event: MessageEvent) => void): () => void {
    if (!('serviceWorker' in navigator)) {
      return () => {};
    }

    navigator.serviceWorker.addEventListener('message', callback);

    return () => {
      navigator.serviceWorker.removeEventListener('message', callback);
    };
  },
};

// Offline Detection and Network Utilities
export const networkUtils = {
  /**
   * Check if currently online
   */
  isOnline(): boolean {
    return navigator.onLine;
  },

  /**
   * Check if currently offline
   */
  isOffline(): boolean {
    return !navigator.onLine;
  },

  /**
   * Get network information
   */
  getNetworkInfo(): NetworkInfo | null {
    const connection =
      (navigator as any).connection ||
      (navigator as any).mozConnection ||
      (navigator as any).webkitConnection;

    if (!connection) {
      return null;
    }

    return {
      type: connection.type || 'unknown',
      effectiveType: connection.effectiveType || 'unknown',
      downlink: connection.downlink || 0,
      rtt: connection.rtt || 0,
      saveData: connection.saveData || false,
    };
  },

  /**
   * Test network speed
   */
  async testNetworkSpeed(): Promise<{ latency: number; speed: 'slow' | 'medium' | 'fast' }> {
    const startTime = performance.now();

    try {
      const endTime = performance.now();
      const latency = endTime - startTime;

      let speed: 'slow' | 'medium' | 'fast' = 'medium';
      if (latency < 200) {
        speed = 'fast';
      } else if (latency > 1000) {
        speed = 'slow';
      }

      return { latency, speed };
    } catch (error) {
      return { latency: -1, speed: 'slow' };
    }
  },

  /**
   * Monitor network status changes
   */
  onNetworkChange(callback: (isOnline: boolean) => void): () => void {
    const handleOnline = () => callback(true);
    const handleOffline = () => callback(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  },

  /**
   * Check if connection is metered
   */
  isMeteredConnection(): boolean {
    const connection = (navigator as any).connection;
    return connection?.saveData === true;
  },

  /**
   * Get connection quality
   */
  getConnectionQuality(): 'poor' | 'average' | 'good' | 'excellent' {
    const info = this.getNetworkInfo();
    if (!info) return 'average';

    const { effectiveType, rtt, downlink } = info;

    if (effectiveType === '4g' && rtt < 150 && downlink > 10) {
      return 'excellent';
    } else if (effectiveType === '4g' || (rtt < 300 && downlink > 5)) {
      return 'good';
    } else if (effectiveType === '3g' || (rtt < 500 && downlink > 1)) {
      return 'average';
    } else {
      return 'poor';
    }
  },
};

// Cache Management Helper Functions
export const cacheUtils = {
  /**
   * Get all cache names
   */
  async getCacheNames(): Promise<string[]> {
    if (!('caches' in window)) {
      return [];
    }

    try {
      return await caches.keys();
    } catch (error) {
      console.error('Failed to get cache names:', error);
      return [];
    }
  },

  /**
   * Get cache information
   */
  async getCacheInfo(cacheName: string): Promise<CacheInfo | null> {
    if (!('caches' in window)) {
      return null;
    }

    try {
      const cache = await caches.open(cacheName);
      const keys = await cache.keys();

      let totalSize = 0;
      let lastModified = new Date(0);

      for (const request of keys) {
        try {
          const response = await cache.match(request);
          if (response) {
            const blob = await response.blob();
            totalSize += blob.size;

            const dateHeader = response.headers.get('date');
            if (dateHeader) {
              const responseDate = new Date(dateHeader);
              if (responseDate > lastModified) {
                lastModified = responseDate;
              }
            }
          }
        } catch (error) {
          // Skip this entry if there's an error
          continue;
        }
      }

      return {
        name: cacheName,
        size: totalSize,
        entries: keys.length,
        lastModified,
      };
    } catch (error) {
      console.error(`Failed to get cache info for ${cacheName}:`, error);
      return null;
    }
  },

  /**
   * Clear specific cache
   */
  async clearCache(cacheName: string): Promise<boolean> {
    if (!('caches' in window)) {
      return false;
    }

    try {
      return await caches.delete(cacheName);
    } catch (error) {
      console.error(`Failed to clear cache ${cacheName}:`, error);
      return false;
    }
  },

  /**
   * Clear all caches
   */
  async clearAllCaches(): Promise<boolean> {
    if (!('caches' in window)) {
      return false;
    }

    try {
      const cacheNames = await caches.keys();
      const deletePromises = cacheNames.map(name => caches.delete(name));
      const results = await Promise.all(deletePromises);

      return results.every(result => result === true);
    } catch (error) {
      console.error('Failed to clear all caches:', error);
      return false;
    }
  },

  /**
   * Add resources to cache
   */
  async addToCache(cacheName: string, resources: string[]): Promise<boolean> {
    if (!('caches' in window)) {
      return false;
    }

    try {
      const cache = await caches.open(cacheName);
      await cache.addAll(resources);
      return true;
    } catch (error) {
      console.error(`Failed to add resources to cache ${cacheName}:`, error);
      return false;
    }
  },

  /**
   * Check if resource is cached
   */
  async isResourceCached(resource: string, cacheName?: string): Promise<boolean> {
    if (!('caches' in window)) {
      return false;
    }

    try {
      if (cacheName) {
        const cache = await caches.open(cacheName);
        const response = await cache.match(resource);
        return response !== undefined;
      } else {
        const response = await caches.match(resource);
        return response !== undefined;
      }
    } catch (error) {
      console.error('Failed to check if resource is cached:', error);
      return false;
    }
  },

  /**
   * Get total cache storage usage
   */
  async getStorageUsage(): Promise<{ usage: number; quota: number; percentage: number }> {
    if (!('storage' in navigator) || !('estimate' in navigator.storage)) {
      return { usage: 0, quota: 0, percentage: 0 };
    }

    try {
      const estimate = await navigator.storage.estimate();
      const usage = estimate.usage || 0;
      const quota = estimate.quota || 0;
      const percentage = quota > 0 ? (usage / quota) * 100 : 0;

      return { usage, quota, percentage };
    } catch (error) {
      return { usage: 0, quota: 0, percentage: 0 };
    }
  },
};

// Push Notification Utilities
export const pushNotificationUtils = {
  /**
   * Check if push notifications are supported
   */
  isSupported(): boolean {
    return 'serviceWorker' in navigator && 'PushManager' in window && 'Notification' in window;
  },

  /**
   * Get current notification permission
   */
  getPermission() {
    if (!('Notification' in window)) {
      return 'denied';
    }
    return Notification.permission;
  },

  /**
   * Request notification permission
   */
  async requestPermission() {
    if (!('Notification' in window)) {
      return 'denied';
    }

    try {
      return await Notification.requestPermission();
    } catch (error) {
      console.error('Failed to request notification permission:', error);
      return 'denied';
    }
  },

  /**
   * Check if notifications are enabled
   */
  areNotificationsEnabled(): boolean {
    return this.getPermission() === 'granted';
  },

  /**
   * Subscribe to push notifications
   */
  async subscribe(
    registration: ServiceWorkerRegistration,
    publicKey: string
  ): Promise<PushSubscription | null> {
    if (!this.isSupported() || !this.areNotificationsEnabled()) {
      return null;
    }

    try {
      return await registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: this.urlBase64ToUint8Array(publicKey),
      });
    } catch (error) {
      console.error('Failed to subscribe to push notifications:', error);
      return null;
    }
  },

  /**
   * Get current push subscription
   */
  async getSubscription(registration: ServiceWorkerRegistration): Promise<PushSubscription | null> {
    if (!this.isSupported()) {
      return null;
    }

    try {
      return await registration.pushManager.getSubscription();
    } catch (error) {
      console.error('Failed to get push subscription:', error);
      return null;
    }
  },

  /**
   * Unsubscribe from push notifications
   */
  async unsubscribe(subscription: PushSubscription): Promise<boolean> {
    try {
      return await subscription.unsubscribe();
    } catch (error) {
      console.error('Failed to unsubscribe from push notifications:', error);
      return false;
    }
  },

  /**
   * Show local notification
   */
  async showNotification(title: string, options): Promise<boolean> {
    if (!this.areNotificationsEnabled()) {
      return false;
    }

    try {
      const registration = await serviceWorkerUtils.getCurrentRegistration();
      if (registration) {
        await registration.showNotification(title, options);
      } else {
        new Notification(title, options);
      }
      return true;
    } catch (error) {
      console.error('Failed to show notification:', error);
      return false;
    }
  },

  /**
   * Convert VAPID key to Uint8Array
   */
  urlBase64ToUint8Array(base64String: string): Uint8Array {
    const padding = '='.repeat((4 - (base64String.length % 4)) % 4);
    const base64 = (base64String + padding).replace(/-/g, '+').replace(/_/g, '/');

    const rawData = window.atob(base64);
    const outputArray = new Uint8Array(rawData.length);

    for (let i = 0; i < rawData.length; ++i) {
      outputArray[i] = rawData.charCodeAt(i);
    }
    return outputArray;
  },
};

// Background Sync Helper Functions
export const backgroundSyncUtils = {
  /**
   * Check if background sync is supported
   */
  isSupported(): boolean {
    return 'serviceWorker' in navigator && 'SyncManager' in window;
  },

  /**
   * Register background sync
   */
  async register(registration: ServiceWorkerRegistration, tag: string): Promise<boolean> {
    if (!this.isSupported()) {
      return false;
    }

    try {
      await (registration as any).sync.register(tag);
      return true;
    } catch (error) {
      console.error(`Failed to register background sync with tag ${tag}:`, error);
      return false;
    }
  },

  /**
   * Get registered sync tags
   */
  async getTags(registration: ServiceWorkerRegistration): Promise<string[]> {
    if (!this.isSupported()) {
      return [];
    }

    try {
      return await (registration as any).sync.getTags();
    } catch (error) {
      console.error('Failed to get sync tags:', error);
      return [];
    }
  },

  /**
   * Check if periodic sync is supported
   */
  isPeriodicSyncSupported(): boolean {
    return 'serviceWorker' in navigator && 'PeriodicSyncManager' in window;
  },

  /**
   * Register periodic sync
   */
  async registerPeriodicSync(
    registration: ServiceWorkerRegistration,
    tag: string,
    options: { minInterval: number }
  ): Promise<boolean> {
    if (!this.isPeriodicSyncSupported()) {
      return false;
    }

    try {
      const periodicSync = (registration as any).periodicSync;
      await periodicSync.register(tag, options);
      return true;
    } catch (error) {
      console.error(`Failed to register periodic sync with tag ${tag}:`, error);
      return false;
    }
  },

  /**
   * Get periodic sync tags
   */
  async getPeriodicSyncTags(registration: ServiceWorkerRegistration): Promise<string[]> {
    if (!this.isPeriodicSyncSupported()) {
      return [];
    }

    try {
      const periodicSync = (registration as any).periodicSync;
      return await periodicSync.getTags();
    } catch (error) {
      console.error('Failed to get periodic sync tags:', error);
      return [];
    }
  },

  /**
   * Unregister periodic sync
   */
  async unregisterPeriodicSync(
    registration: ServiceWorkerRegistration,
    tag: string
  ): Promise<boolean> {
    if (!this.isPeriodicSyncSupported()) {
      return false;
    }

    try {
      const periodicSync = (registration as any).periodicSync;
      await periodicSync.unregister(tag);
      return true;
    } catch (error) {
      console.error(`Failed to unregister periodic sync with tag ${tag}:`, error);
      return false;
    }
  },
};

// PWA Analytics Tracking Utilities
export const analyticsUtils = {
  /**
   * Generate session ID
   */
  generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  },

  /**
   * Get or create session ID
   */
  getSessionId(): string {
    let sessionId = sessionStorage.getItem('pwa_session_id');
    if (!sessionId) {
      sessionId = this.generateSessionId();
      sessionStorage.setItem('pwa_session_id', sessionId);
    }
    return sessionId;
  },

  /**
   * Create analytics event
   */
  createEvent(event: string, data: Record<string, any> = {}, userId?: string): PWAAnalyticsEvent {
    return {
      event,
      data: {
        ...data,
        url: window.location.href,
        userAgent: navigator.userAgent,
        timestamp: new Date().toISOString(),
        isOnline: navigator.onLine,
        displayMode: pwaInstallationUtils.getDisplayMode(),
      },
      timestamp: new Date().toISOString(),
      sessionId: this.getSessionId(),
      userId,
    };
  },

  /**
   * Track PWA event
   */
  trackEvent(event: string, data: Record<string, any> = {}, userId?: string): void {
    const analyticsEvent = this.createEvent(event, data, userId);

    // Store in localStorage for offline support
    const events = this.getStoredEvents();
    events.push(analyticsEvent);
    this.storeEvents(events);

    // Send if online
    if (navigator.onLine) {
      this.sendEvents([analyticsEvent]);
    }
  },

  /**
   * Get stored events from localStorage
   */
  getStoredEvents(): PWAAnalyticsEvent[] {
    try {
      const stored = localStorage.getItem('pwa_analytics_events');
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('Failed to get stored analytics events:', error);
      return [];
    }
  },

  /**
   * Store events in localStorage
   */
  storeEvents(events: PWAAnalyticsEvent[]): void {
    try {
      // Keep only last 100 events to prevent storage bloat
      const eventsToStore = events.slice(-100);
      localStorage.setItem('pwa_analytics_events', JSON.stringify(eventsToStore));
    } catch (error) {
      console.error('Failed to store analytics events:', error);
    }
  },

  /**
   * Send events to server
   */
  async sendEvents(events: PWAAnalyticsEvent[]): Promise<boolean> {
    if (events.length === 0) {
      return true;
    }

    try {
      const response = await fetch('/api/analytics/pwa', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ events }),
      });

      return response.ok;
    } catch (error) {
      console.error('Failed to send analytics events:', error);
      return false;
    }
  },

  /**
   * Sync all stored events
   */
  async syncStoredEvents(): Promise<boolean> {
    const events = this.getStoredEvents();
    if (events.length === 0) {
      return true;
    }

    const success = await this.sendEvents(events);
    if (success) {
      // Clear stored events after successful sync
      this.storeEvents([]);
    }

    return success;
  },

  /**
   * Track page view
   */
  trackPageView(additionalData?: Record<string, any>): void {
    this.trackEvent('page_view', {
      path: window.location.pathname,
      title: document.title,
      referrer: document.referrer,
      ...additionalData,
    });
  },

  /**
   * Track user interaction
   */
  trackInteraction(element: string, action: string, additionalData?: Record<string, any>): void {
    this.trackEvent('user_interaction', {
      element,
      action,
      ...additionalData,
    });
  },

  /**
   * Track performance metrics
   */
  trackPerformance(): void {
    if ('performance' in window && 'timing' in performance) {
      const timing = performance.timing;
      const metrics = {
        domContentLoaded: timing.domContentLoadedEventEnd - timing.navigationStart,
        pageLoad: timing.loadEventEnd - timing.navigationStart,
        dnsLookup: timing.domainLookupEnd - timing.domainLookupStart,
        serverResponse: timing.responseEnd - timing.requestStart,
      };

      this.trackEvent('performance_metrics', metrics);
    }
  },
};

// Device Capability Detection Functions
export const deviceCapabilityUtils = {
  /**
   * Detect all PWA capabilities
   */
  detectCapabilities(): PWACapabilities {
    return {
      hasServiceWorker: 'serviceWorker' in navigator,
      hasPushNotifications: 'PushManager' in window && 'Notification' in window,
      hasBackgroundSync: 'serviceWorker' in navigator && 'SyncManager' in window,
      hasPeriodicSync: 'serviceWorker' in navigator && 'PeriodicSyncManager' in window,
      hasWebShare: 'share' in navigator,
      hasBeforeInstallPrompt: true, // Cannot be detected before the event
      hasNotificationAPI: 'Notification' in window,
      hasStorageAPI: 'storage' in navigator && 'estimate' in navigator.storage,
      isStandalone: pwaInstallationUtils.isInstalled(),
      isDisplayModeStandalone: pwaInstallationUtils.getDisplayMode() === 'standalone',
      hasFileSystemAccess: 'showOpenFilePicker' in window,
      hasClipboard: 'clipboard' in navigator,
      hasWakeLock: 'wakeLock' in navigator,
      hasDeviceMotion: 'DeviceMotionEvent' in window,
      hasGeolocation: 'geolocation' in navigator,
    };
  },

  /**
   * Check if device supports specific feature
   */
  hasFeature(feature: keyof PWACapabilities): boolean {
    const capabilities = this.detectCapabilities();
    return capabilities[feature];
  },

  /**
   * Get device information
   */
  getDeviceInfo(): Record<string, any> {
    return {
      userAgent: navigator.userAgent,
      platform: navigator.platform,
      language: navigator.language,
      languages: navigator.languages,
      cookieEnabled: navigator.cookieEnabled,
      onLine: navigator.onLine,
      hardwareConcurrency: navigator.hardwareConcurrency,
      maxTouchPoints: navigator.maxTouchPoints,
      vendorSub: navigator.vendorSub,
      productSub: navigator.productSub,
      vendor: navigator.vendor,
      appName: navigator.appName,
      appVersion: navigator.appVersion,
      screenWidth: screen.width,
      screenHeight: screen.height,
      colorDepth: screen.colorDepth,
      pixelDepth: screen.pixelDepth,
      timezoneOffset: new Date().getTimezoneOffset(),
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    };
  },

  /**
   * Check if device is mobile
   */
  isMobile(): boolean {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
      navigator.userAgent
    );
  },

  /**
   * Check if device is iOS
   */
  isIOS(): boolean {
    return /iPad|iPhone|iPod/.test(navigator.userAgent);
  },

  /**
   * Check if device is Android
   */
  isAndroid(): boolean {
    return /Android/i.test(navigator.userAgent);
  },

  /**
   * Check if running on desktop
   */
  isDesktop(): boolean {
    return !this.isMobile();
  },

  /**
   * Get browser information
   */
  getBrowserInfo(): { name: string; version: string } {
    const userAgent = navigator.userAgent;
    let browserName = 'Unknown';
    let browserVersion = 'Unknown';

    if (userAgent.indexOf('Chrome') > -1) {
      browserName = 'Chrome';
      browserVersion = userAgent.match(/Chrome\/(\d+)/)?.[1] || 'Unknown';
    } else if (userAgent.indexOf('Firefox') > -1) {
      browserName = 'Firefox';
      browserVersion = userAgent.match(/Firefox\/(\d+)/)?.[1] || 'Unknown';
    } else if (userAgent.indexOf('Safari') > -1 && userAgent.indexOf('Chrome') === -1) {
      browserName = 'Safari';
      browserVersion = userAgent.match(/Version\/(\d+)/)?.[1] || 'Unknown';
    } else if (userAgent.indexOf('Edge') > -1) {
      browserName = 'Edge';
      browserVersion = userAgent.match(/Edge\/(\d+)/)?.[1] || 'Unknown';
    }

    return { name: browserName, version: browserVersion };
  },
};

// PWA Security and Validation Utilities
export const securityUtils = {
  /**
   * Validate service worker URL
   */
  validateServiceWorkerUrl(url: string): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Check if URL is absolute
    if (!url.startsWith('/') && !url.startsWith('http')) {
      errors.push('Service worker URL must be absolute or root-relative');
    }

    // Check if URL is HTTPS in production
    if (location.protocol === 'https:' && url.startsWith('http:')) {
      errors.push('Service worker must be served over HTTPS in production');
    }

    // Check file extension
    if (!url.endsWith('.js')) {
      warnings.push('Service worker file should have .js extension');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  },

  /**
   * Validate manifest.json
   */
  async validateManifest(manifestUrl: string = '/manifest.json'): Promise<ValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      const response = await fetch(manifestUrl);
      if (!response.ok) {
        errors.push(`Failed to fetch manifest: ${response.status}`);
        return { isValid: false, errors, warnings };
      }

      const manifest = await response.json();

      // Required fields
      if (!manifest.name && !manifest.short_name) {
        errors.push('Manifest must have either name or short_name');
      }

      if (!manifest.icons || !Array.isArray(manifest.icons) || manifest.icons.length === 0) {
        errors.push('Manifest must have at least one icon');
      }

      if (!manifest.start_url) {
        errors.push('Manifest must have start_url');
      }

      if (!manifest.display) {
        errors.push('Manifest must have display mode');
      }

      // Recommended fields
      if (!manifest.background_color) {
        warnings.push('Manifest should have background_color');
      }

      if (!manifest.theme_color) {
        warnings.push('Manifest should have theme_color');
      }

      if (!manifest.description) {
        warnings.push('Manifest should have description');
      }

      // Icon validations
      if (manifest.icons) {
        const hasMaskableIcon = manifest.icons.some(icon => icon.purpose?.includes('maskable'));
        if (!hasMaskableIcon) {
          warnings.push('Manifest should have at least one maskable icon');
        }

        const hasLargeIcon = manifest.icons.some(icon => {
          const size = parseInt(icon.sizes?.split('x')[0] || '0');
          return size >= 512;
        });
        if (!hasLargeIcon) {
          warnings.push('Manifest should have at least one large icon (512x512 or larger)');
        }
      }

      // Screenshots validation
      if (!manifest.screenshots || manifest.screenshots.length === 0) {
        warnings.push('Manifest should include screenshots for better visibility in app stores');
      }

      return {
        isValid: errors.length === 0,
        errors,
        warnings,
      };
    } catch (error) {
      errors.push(`Failed to parse manifest: ${error}`);
      return { isValid: false, errors, warnings };
    }
  },
};
