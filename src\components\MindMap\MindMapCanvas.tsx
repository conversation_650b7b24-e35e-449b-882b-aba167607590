import React, { use<PERSON><PERSON>back, useMemo, useState } from 'react';
import <PERSON>act<PERSON><PERSON>, {
  Panel,
  type Node,
  type Edge,
  type <PERSON>de<PERSON>hange,
  type <PERSON><PERSON>hang<PERSON>,
  ReactFlowProvider,
  Background,
  type Connection,
  useReactFlow,
} from 'reactflow';
import 'reactflow/dist/style.css';

import { nodeTypes } from './nodes';
import { Button } from '@/components/ui/button';
import { Type, Link, AlertCircle, Edit3, Trash2, Copy, HelpCircle } from 'lucide-react';
import { toReactFlowEdge, getDefaultNodeData } from '@/utils/mindMapTransforms';
import { MindMapNode, MindMapConnection } from '@/types/MindMapsTypes';

import { EmptyState } from '@/components/Global/EmptyState';
import { ConfirmDialog } from '../Global/ConfirmDialog';
import { useMindMapEditor } from '@/hooks/useMindMapEditor';
import { useKeyboardShortcuts } from '@/hooks/useKeyboardShortcuts';
import Modal from '../Global/Modal';
import { NodeEditProvider, useNodeEdit } from './contexts/node-edit-context';
import { TaskNodeForm } from './node-forms/task-node-form';
import { MilestoneNodeForm } from './node-forms/milestone-node-form';
import { DecisionNodeForm } from './node-forms/decision-node-form';
import { useMindMapStore } from '@/stores/mindMapStore';

// Consolidated Context Menu Component
interface ContextMenuProps {
  x: number;
  y: number;
  nodeId: string;
  nodeType: string;
  onEdit: () => void;
  onDelete: () => void;
  onDuplicate: () => void;
  onConnect: () => void;
  onClose: () => void;
}

function ContextMenu({
  x,
  y,
  onEdit,
  onDelete,
  onDuplicate,
  onConnect,
  onClose,
}: ContextMenuProps) {
  return (
    <div
      className="fixed z-50 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-xl py-2 min-w-[180px]"
      style={{ left: x, top: y }}
      onMouseLeave={onClose}
    >
      <button
        className="w-full px-4 py-2 text-left text-sm hover:bg-blue-50 dark:hover:bg-blue-900/20 hover:text-blue-600 dark:hover:text-blue-400 flex items-center gap-3 transition-colors duration-150"
        onClick={onEdit}
      >
        <Edit3 className="w-4 h-4" />
        Edit Node
      </button>
      <button
        className="w-full px-4 py-2 text-left text-sm hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center gap-3 transition-colors duration-150"
        onClick={onDuplicate}
      >
        <Copy className="w-4 h-4" />
        Duplicate
      </button>
      <button
        className="w-full px-4 py-2 text-left text-sm hover:bg-green-50 dark:hover:bg-green-900/20 hover:text-green-600 dark:hover:text-green-400 flex items-center gap-3 transition-colors duration-150"
        onClick={onConnect}
      >
        <Link className="w-4 h-4" />
        Connect
      </button>
      <div className="border-t border-gray-200 dark:border-gray-700 my-1" />
      <button
        className="w-full px-4 py-2 text-left text-sm hover:bg-red-50 dark:hover:bg-red-900/20 text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 flex items-center gap-3 transition-colors duration-150"
        onClick={onDelete}
      >
        <Trash2 className="w-4 h-4" />
        Delete Node
      </button>
    </div>
  );
}

// Node Edit Modal Component
function NodeEditModal() {
  const { editingNodeId, editingNodeData, editingNodeType, stopEditing } = useNodeEdit();
  const { updateNode } = useMindMapStore();

  const handleSave = (updatedData: any) => {
    if (editingNodeId) {
      updateNode(editingNodeId, {
        content: {
          text: updatedData.title || updatedData.text || 'New Node',
          ...updatedData,
        },
      });
    }
    stopEditing();
  };

  const renderForm = () => {
    if (!editingNodeData || !editingNodeType) return null;

    switch (editingNodeType) {
      case 'task':
        return <TaskNodeForm data={editingNodeData} onSave={handleSave} onCancel={stopEditing} />;
      case 'milestone':
        return (
          <MilestoneNodeForm data={editingNodeData} onSave={handleSave} onCancel={stopEditing} />
        );
      case 'decision':
        return (
          <DecisionNodeForm data={editingNodeData} onSave={handleSave} onCancel={stopEditing} />
        );
      default:
        return null;
    }
  };

  return (
    <Modal isOpen={!!editingNodeId} onClose={stopEditing} size="lg">
      {renderForm()}
    </Modal>
  );
}

// Consolidated Keyboard Shortcuts Help Component
function KeyboardShortcutsHelp() {
  const [isOpen, setIsOpen] = useState(false);

  const shortcuts = [
    { key: 'Ctrl + Z', action: 'Undo' },
    { key: 'Ctrl + Y', action: 'Redo' },
    { key: 'Delete', action: 'Delete selected' },
    { key: 'Ctrl + D', action: 'Duplicate selected' },
    { key: 'Ctrl + S', action: 'Save' },
    { key: 'Escape', action: 'Cancel connection' },
    { key: 'Double Click', action: 'Edit node' },
    { key: 'Right Click', action: 'Context menu' },
  ];

  return (
    <>
      <Button
        size="sm"
        variant="ghost"
        className="fixed bottom-4 right-4 z-40"
        onClick={() => setIsOpen(true)}
      >
        <HelpCircle className="w-4 h-4" />
      </Button>

      <Modal isOpen={isOpen} onClose={() => setIsOpen(false)}>
        <div className="max-w-md">
          <div>
            <p>Keyboard Shortcuts</p>
            <div>Use these shortcuts to work more efficiently with your mind map.</div>
          </div>
          <div className="space-y-2">
            {shortcuts.map((shortcut, index) => (
              <div key={index} className="flex justify-between items-center py-1">
                <span className="text-sm text-gray-600 dark:text-gray-400">{shortcut.action}</span>
                <kbd className="px-2 py-1 text-xs bg-gray-100 dark:bg-gray-800 rounded border">
                  {shortcut.key}
                </kbd>
              </div>
            ))}
          </div>
        </div>
      </Modal>
    </>
  );
}

class MindMapErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError() {
    return { hasError: true };
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="h-full w-full flex items-center justify-center theme-surface">
          <EmptyState
            type="error"
            title="Something went wrong"
            description="Failed to load the mind map. Please refresh the page."
            icon={<AlertCircle className="w-full h-full" />}
            actions={[
              {
                label: 'Refresh Page',
                onClick: () => window.location.reload(),
                variant: 'outline',
              },
            ]}
            size="md"
            animated={true}
          />
        </div>
      );
    }
    return this.props.children;
  }
}

interface MindMapCanvasInnerProps {
  isEditing?: boolean;
}

function MindMapCanvasInner({ isEditing = true }: MindMapCanvasInnerProps) {
  const reactFlowInstance = useReactFlow();

  // Use the consolidated mind map editor hook
  const {
    mindMap: currentMindMap,
    addNode,
    updateNode,
    deleteNode,
    addConnection,
    deleteConnection,
    selectNode,
    isConnecting,
    connectingFrom,
    startConnecting,
    endConnecting,
    contextMenu,
    confirmDialog,
    isDeleting,
    showContextMenu,
    hideContextMenu,
    hideConfirmDialog,
    handleDeleteNode,
    handleDuplicateNode,
  } = useMindMapEditor();

  // Apply keyboard shortcuts
  useKeyboardShortcuts(isEditing);

  const nodes: Node[] = useMemo(() => {
    if (!currentMindMap?.nodes || !Array.isArray(currentMindMap.nodes)) {
      return [];
    }

    return currentMindMap.nodes.map(node => ({
      id: node.id,
      type: node.type,
      position: node.position,
      data: {
        ...node.content,
        title: node.content?.text || node.content?.title || 'New Node',
        text: node.content?.text || node.content?.title || 'New Node',
        isConnecting,
        isConnectingFrom: connectingFrom === node.id,
      },
      selected: node.selected || false,
      dragging: node.dragging || false,
      // Ensure we have proper dimensions
      width: node.size?.width,
      height: node.size?.height,
    }));
  }, [currentMindMap, isConnecting, connectingFrom]);

  const edges: Edge[] = useMemo(() => {
    if (!currentMindMap?.connections || !Array.isArray(currentMindMap.connections)) {
      return [];
    }
    return currentMindMap.connections.map(toReactFlowEdge);
  }, [currentMindMap?.connections]);

  // OPTIMIZED: onNodesChange function - reduce re-renders during dragging
  const onNodesChange = useCallback(
    (changes: NodeChange[]) => {
      if (!isEditing) return;

      // Batch changes to reduce re-renders
      const batchedChanges = new Map<string, Partial<MindMapNode>>();
      const removeChanges: string[] = [];

      changes.forEach(change => {
        switch (change.type) {
          case 'position':
            // Only update position if it's actually dragging (not just selection)
            if (change.position && change.dragging !== false) {
              const existing = batchedChanges.get(change.id) || {};
              batchedChanges.set(change.id, {
                ...existing,
                position: change.position,
                dragging: change.dragging,
              });
            }
            break;
          case 'select':
            const existing = batchedChanges.get(change.id) || {};
            batchedChanges.set(change.id, {
              ...existing,
              selected: change.selected || false,
            });

            if (change.selected) {
              selectNode(change.id);
            }
            break;
          case 'remove':
            removeChanges.push(change.id);
            break;
          case 'dimensions':
            if (change.dimensions) {
              const existing = batchedChanges.get(change.id) || {};
              batchedChanges.set(change.id, {
                ...existing,
                size: {
                  width: change.dimensions.width,
                  height: change.dimensions.height,
                },
              });
            }
            break;
        }
      });

      // Apply batched changes
      batchedChanges.forEach((updates, nodeId) => {
        updateNode(nodeId, updates);
      });

      // Handle removals
      removeChanges.forEach(id => {
        deleteNode(id);
      });
    },
    [isEditing, updateNode, selectNode, deleteNode]
  );

  const onEdgesChange = useCallback(
    (changes: EdgeChange[]) => {
      if (!isEditing) return;

      changes.forEach(change => {
        if (change.type === 'remove') {
          deleteConnection(change.id);
        }
      });
    },
    [isEditing, deleteConnection]
  );

  const addTextNode = useCallback(() => {
    const viewport = reactFlowInstance.getViewport();
    const nodeId = `node-${Date.now()}`;
    const defaultData = getDefaultNodeData('text');

    const position = {
      x: -viewport.x / viewport.zoom + (Math.random() * 200 - 100),
      y: -viewport.y / viewport.zoom + (Math.random() * 200 - 100),
    };

    const newNode: MindMapNode = {
      id: nodeId,
      type: 'text',
      position,
      size: { width: 200, height: 100 },
      content: {
        text: 'New Text Node',
        ...defaultData,
      },
      style: {
        borderColor: '#cccccc',
        borderWidth: 1,
        borderRadius: 8,
        opacity: 1,
        shadow: false,
      },
      parentId: undefined,
      childIds: [],
      collapsed: false,
      zIndex: 1,
      selected: false,
      dragging: false,
    };

    addNode(newNode);

    setTimeout(() => {
      selectNode(nodeId);
      reactFlowInstance.setCenter(position.x, position.y, { zoom: 1 });
    }, 50);
  }, [addNode, selectNode, reactFlowInstance]);



  const onNodeContextMenu = useCallback(
    (event: React.MouseEvent, node: Node) => {
      if (isEditing) {
        event.preventDefault();
        showContextMenu(event.clientX, event.clientY, node.id, node.type || 'default');
      }
    },
    [isEditing, showContextMenu]
  );

  const onNodeClick = useCallback(
    (_event: React.MouseEvent, node: Node) => {
      if (!isEditing) return;

      if (isConnecting && connectingFrom && connectingFrom !== node.id) {
        const connectionId = `connection-${connectingFrom}-${node.id}-${Date.now()}`;
        const newConnection: MindMapConnection = {
          id: connectionId,
          sourceNodeId: connectingFrom,
          targetNodeId: node.id,
          type: 'curved',
          style: {
            stroke: '#3b82f6',
            strokeWidth: 2,
            animated: false,
          },
        };
        addConnection(newConnection);
        endConnecting();
      }
    },
    [isEditing, isConnecting, connectingFrom, addConnection, endConnecting]
  );

  const onConnect = useCallback(
    (connection: Connection) => {
      if (!isEditing || !connection.source || !connection.target) return;

      const connectionId = `connection-${connection.source}-${connection.target}-${Date.now()}`;
      const newConnection: MindMapConnection = {
        id: connectionId,
        sourceNodeId: connection.source,
        targetNodeId: connection.target,
        type: 'curved',
        style: {
          stroke: '#3b82f6',
          strokeWidth: 2,
          animated: false,
        },
      };
      addConnection(newConnection);
    },
    [isEditing, addConnection]
  );

  return (
    <div className="h-full w-full relative">
      <ReactFlow
        nodes={nodes}
        edges={edges}
        nodeTypes={nodeTypes}
        onNodesChange={onNodesChange} // Always enable onNodesChange for proper functionality
        onEdgesChange={isEditing ? onEdgesChange : undefined}
        onNodeContextMenu={onNodeContextMenu}
        onNodeClick={onNodeClick}
        onConnect={onConnect}
        fitView={false} // Disable fitView to prevent layout issues
        snapToGrid={isEditing}
        snapGrid={[20, 20]}
        defaultViewport={{ x: 0, y: 0, zoom: 1 }}
        connectionLineStyle={{ stroke: '#3b82f6', strokeWidth: 2 }}
        deleteKeyCode={isEditing ? 'Delete' : null}
        minZoom={0.2}
        maxZoom={1.5}
        nodesDraggable={isEditing}
        nodesConnectable={isEditing}
        elementsSelectable={isEditing}
        panOnDrag={true}
        zoomOnScroll={true}
        preventScrolling={true}
        proOptions={{ hideAttribution: true }}
      >
        <Background />

        {/* Optimized Toolbar */}
        {isEditing && (
          <Panel position="top-right">
            <div className="flex gap-2">
              <Button size="sm" variant="outline" onClick={addTextNode}>
                <Type className="h-4 w-4 mr-1" />
                Text
              </Button>
            </div>
          </Panel>
        )}
      </ReactFlow>

      {/* Consolidated Modals and UI */}
      <NodeEditModal />

      {contextMenu && (
        <ContextMenu
          x={contextMenu.x}
          y={contextMenu.y}
          nodeId={contextMenu.nodeId}
          nodeType={contextMenu.nodeType}
          onEdit={() => {
            hideContextMenu();
          }}
          onDelete={() => handleDeleteNode(contextMenu.nodeId)}
          onDuplicate={() => handleDuplicateNode(contextMenu.nodeId)}
          onConnect={() => {
            startConnecting(contextMenu.nodeId);
            hideContextMenu();
          }}
          onClose={hideContextMenu}
        />
      )}

      <ConfirmDialog
        isOpen={confirmDialog.isOpen}
        onClose={hideConfirmDialog}
        title={confirmDialog.title}
        description={confirmDialog.description}
        confirmText="Delete"
        cancelText="Cancel"
        onConfirm={confirmDialog.onConfirm}
        variant="destructive"
        loading={isDeleting}
      />

      {isEditing && <KeyboardShortcutsHelp />}
    </div>
  );
}

export default function MindMapCanvas({ isEditing = true }: { isEditing?: boolean }) {
  return (
    <MindMapErrorBoundary>
      <ReactFlowProvider>
        <NodeEditProvider>
          <MindMapCanvasInner isEditing={isEditing} />
        </NodeEditProvider>
      </ReactFlowProvider>
    </MindMapErrorBoundary>
  );
}
