import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useToast } from '@/hooks/use-toast';
import { MindMapService } from '@/services/MindMap.service';
import { CreateMindMapRequest, MindMap, MindMapFilters } from '@/types/MindMapsTypes';

// Query keys
export const mindMapKeys = {
  all: ['mindMaps'] as const,
  lists: () => [...mindMapKeys.all, 'list'] as const,
  list: (filters: MindMapFilters) => [...mindMapKeys.lists(), filters] as const,
  details: () => [...mindMapKeys.all, 'detail'] as const,
  detail: (id: string) => [...mindMapKeys.details(), id] as const,
};

// Get mind maps hook
export const useMindMaps = (filters?: MindMapFilters) => {
  return useQuery({
    queryKey: mindMapKeys.list(filters || {}),
    queryFn: async () => {
      const response = await MindMapService.getMindMaps(filters);
      // The API returns { mindMaps: [...] }, so we need to extract the mindMaps array
      return response.mindMaps || response;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Get single mind map hook
export const useMindMap = (id: string) => {
  return useQuery({
    queryKey: mindMapKeys.detail(id),
    queryFn: async () => {
      const response = await MindMapService.getMindMap(id);
      // The API returns { mindMap: {...} }, so we need to extract the mindMap object
      return response.mindMap || response;
    },
    enabled: !!id,
  });
};

// Create mind map hook
export const useCreateMindMap = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: (data: CreateMindMapRequest) => MindMapService.createMindMap(data),
    onSuccess: data => {
      queryClient.invalidateQueries({ queryKey: mindMapKeys.lists() });
      toast({
        title: 'Success',
        description: 'Mind map created successfully',
      });
      return data;
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.response?.data?.error || 'Failed to create mind map',
        variant: 'destructive',
      });
    },
  });
};

// Update mind map hook
export const useUpdateMindMap = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<MindMap> }) =>
      MindMapService.updateMindMap(id, data),
    onSuccess: (_data, variables) => {
      queryClient.invalidateQueries({ queryKey: mindMapKeys.lists() });
      queryClient.invalidateQueries({ queryKey: mindMapKeys.detail(variables.id) });
      toast({
        title: 'Success',
        description: 'Mind map updated successfully',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.response?.data?.error || 'Failed to update mind map',
        variant: 'destructive',
      });
    },
  });
};

// Delete mind map hook
export const useDeleteMindMap = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: (id: string) => MindMapService.deleteMindMap(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: mindMapKeys.lists() });
      toast({
        title: 'Success',
        description: 'Mind map deleted successfully',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.response?.data?.error || 'Failed to delete mind map',
        variant: 'destructive',
      });
    },
  });
};

// Duplicate mind map hook
export const useDuplicateMindMap = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: ({ id, title }: { id: string; title: string }) =>
      MindMapService.duplicateMindMap(id, title),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: mindMapKeys.lists() });
      toast({
        title: 'Success',
        description: 'Mind map duplicated successfully',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.response?.data?.error || 'Failed to duplicate mind map',
        variant: 'destructive',
      });
    },
  });
};

// Export mind map hook
export const useExportMindMap = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: ({ id, format }: { id: string; format: string }) =>
      MindMapService.exportMindMap(id, format),
    onSuccess: () => {
      toast({
        title: 'Export Started',
        description: 'Mind map export has been initiated',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Export Failed',
        description: error.response?.data?.error || 'Failed to export mind map',
        variant: 'destructive',
      });
    },
  });
};

// Node operations hooks
export const useAddNode = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: ({ id, node }: { id: string; node: any }) => MindMapService.addNode(id, node),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: mindMapKeys.detail(variables.id) });
      toast({
        title: 'Success',
        description: 'Node added successfully',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.response?.data?.error || 'Failed to add node',
        variant: 'destructive',
      });
    },
  });
};

export const useUpdateNode = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: ({ id, nodeId, updates }: { id: string; nodeId: string; updates: any }) =>
      MindMapService.updateNode(id, nodeId, updates),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: mindMapKeys.detail(variables.id) });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.response?.data?.error || 'Failed to update node',
        variant: 'destructive',
      });
    },
  });
};

export const useDeleteNode = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: ({ id, nodeId }: { id: string; nodeId: string }) =>
      MindMapService.deleteNode(id, nodeId),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: mindMapKeys.detail(variables.id) });
      toast({
        title: 'Success',
        description: 'Node deleted successfully',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.response?.data?.error || 'Failed to delete node',
        variant: 'destructive',
      });
    },
  });
};

// Connection operations hooks
export const useAddConnection = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: ({ id, connection }: { id: string; connection: any }) =>
      MindMapService.addConnection(id, connection),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: mindMapKeys.detail(variables.id) });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.response?.data?.error || 'Failed to add connection',
        variant: 'destructive',
      });
    },
  });
};
