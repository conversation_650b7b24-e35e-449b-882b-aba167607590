import mongoose from 'mongoose';

if (!process.env.MONGODB_URI) {
  throw new Error('Please add MONGODB_URI to .env file');
}

let isConnected = false;

export const connectDB = async () => {
  if (isConnected && mongoose.connection.readyState === 1) {
    return;
  }

  try {
    const options = {
      maxPoolSize: 10,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
      family: 4,
      bufferCommands: false,
    };

    await mongoose.connect(process.env.MONGODB_URI as string, options);

    // Wait for connection to be fully ready
    await new Promise(resolve => {
      if (mongoose.connection.readyState === 1) {
        resolve(true);
      } else {
        mongoose.connection.once('connected', resolve);
      }
    });

    isConnected = true;

    mongoose.connection.on('connected', () => {
      isConnected = true;
    });

    mongoose.connection.on('disconnected', () => {
      isConnected = false;
    });

    mongoose.connection.on('error', () => {
      isConnected = false;
    });
  } catch (error: any) {
    isConnected = false;
    throw error;
  }
};
