import axios, { AxiosInstance, AxiosResponse } from 'axios';

// Types for API requests and responses
export interface GetLogsParams {
    page?: number;
    limit?: number;
    userId?: string;
    organizationId?: string;
    projectId?: string;
    resourceType?: string;
    resourceId?: string;
    search?: string;
    filterUserId?: string;
    actions?: string[];
    resourceTypes?: string[];
    startDate?: Date;
    endDate?: Date;
    priority?: string;
    source?: string;
}

export interface ActivityLog {
    id: string;
    userId: string;
    userName?: string;
    userEmail?: string;
    userImage?: string;
    action: string;
    resourceType: string;
    resourceId?: string;
    resourceName?: string;
    description: string;
    metadata?: Record<string, any>;
    ipAddress?: string;
    userAgent?: string;
    deviceInfo?: {
        browser?: string;
        os?: string;
        isMobile?: boolean;
        isTablet?: boolean;
        isDesktop?: boolean;
    };
    timestamp: string;
    createdAt: string;
    updatedAt: string;
}

export interface PaginationInfo {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
}

export interface User {
    id: string;
    name: string;
    email: string;
}

export interface ActivityLogsResponse {
    success: boolean;
    logs: ActivityLog[];
    pagination: PaginationInfo;
    availableActions: string[];
    availableResourceTypes: string[];
    availableUsers: User[];
}

export interface AnalyticsParams {
    userId?: string;
    organizationId?: string;
    startDate?: Date;
    endDate?: Date;
    groupBy?: 'day' | 'week' | 'month';
}

export interface AnalyticsData {
    totalActions: number;
    actionBreakdown: Record<string, number>;
    resourceBreakdown: Record<string, number>;
    dailyActivity: Array<{ date: string; count: number }>;
    topUsers: Array<{ userId: string; userName: string; count: number }>;
    recentActivity: ActivityLog[];
}

export interface AnalyticsResponse {
    success: boolean;
    analytics: AnalyticsData;
}

export interface CreateLogData {
    action: string;
    resourceType: string;
    resourceId?: string;
    resourceName?: string;
    description: string;
    metadata?: Record<string, any>;
    ipAddress?: string;
    userAgent?: string;
}

export interface CreateLogResponse {
    success: boolean;
    log: {
        id: string;
        timestamp: string;
        action: string;
        resourceType: string;
        description: string;
    };
    message: string;
}

export interface ExportParams {
    format: 'csv' | 'json' | 'xlsx';
    userId?: string;
    organizationId?: string;
    projectId?: string;
    resourceType?: string;
    resourceId?: string;
    search?: string;
    filterUserId?: string;
    actions?: string[];
    resourceTypes?: string[];
    startDate?: Date;
    endDate?: Date;
}

export interface ApiError {
    message: string;
    code?: string;
    details?: Record<string, any>;
}

class ActivityLogApiService {
    private api: AxiosInstance;
    private readonly baseURL = '/api/activity-logs';

    constructor() {
        this.api = axios.create({
            baseURL: this.baseURL,
            timeout: 30000, // 30 seconds timeout
            headers: {
                'Content-Type': 'application/json',
            },
        });

        this.setupInterceptors();
    }

    private setupInterceptors(): void {
        // Request interceptor
        this.api.interceptors.request.use(
            (config) => {
                // Add timestamp to prevent caching issues
                if (config.method === 'get') {
                    config.params = {
                        ...config.params,
                        _t: Date.now(),
                    };
                }
                return config;
            },
            (error) => {
                console.error('Request interceptor error:', error);
                return Promise.reject(error);
            }
        );

        // Response interceptor
        this.api.interceptors.response.use(
            (response: AxiosResponse) => {
                return response;
            },
            async (error) => {
                // Handle network errors
                if (!error.response) {
                    console.error('Network error:', error.message);
                    throw new Error('Network error. Please check your connection and try again.');
                }

                // Handle specific HTTP status codes
                switch (error.response.status) {
                    case 401:
                        console.error('Unauthorized access');
                        throw new Error('You are not authorized to perform this action.');
                    case 403:
                        console.error('Forbidden access');
                        throw new Error('You do not have permission to access this resource.');
                    case 404:
                        console.error('Resource not found');
                        throw new Error('The requested resource was not found.');
                    case 429:
                        console.error('Rate limit exceeded');
                        throw new Error('Too many requests. Please wait a moment and try again.');
                    case 500:
                        console.error('Server error');
                        throw new Error('Server error. Please try again later.');
                    default:
                        console.error('API error:', error.response.data);
                        throw new Error(
                            error.response.data?.message ||
                            error.response.data?.error ||
                            'An unexpected error occurred.'
                        );
                }
            }
        );
    }

    private buildQueryParams(params: Record<string, any>): URLSearchParams {
        const searchParams = new URLSearchParams();

        Object.entries(params).forEach(([key, value]) => {
            if (value !== undefined && value !== null && value !== '') {
                if (Array.isArray(value)) {
                    if (value.length > 0) {
                        searchParams.append(key, value.join(','));
                    }
                } else if (value instanceof Date) {
                    searchParams.append(key, value.toISOString());
                } else {
                    searchParams.append(key, String(value));
                }
            }
        });

        return searchParams;
    }

    private async retryRequest<T>(
        requestFn: () => Promise<T>,
        maxRetries: number = 3,
        baseDelay: number = 1000
    ): Promise<T> {
        let lastError: Error;

        for (let attempt = 0; attempt <= maxRetries; attempt++) {
            try {
                return await requestFn();
            } catch (error) {
                lastError = error as Error;

                // Don't retry on client errors (4xx)
                if (axios.isAxiosError(error) && error.response?.status && error.response.status < 500) {
                    throw error;
                }

                if (attempt < maxRetries) {
                    const delay = baseDelay * Math.pow(2, attempt); // Exponential backoff
                    console.warn(`Request failed, retrying in ${delay}ms... (attempt ${attempt + 1}/${maxRetries})`);
                    await new Promise(resolve => setTimeout(resolve, delay));
                }
            }
        }

        throw lastError!;
    }

    // Get activity logs with filtering and pagination
    async getLogs(params: GetLogsParams = {}): Promise<ActivityLogsResponse> {
        return this.retryRequest(async () => {
            const queryParams = this.buildQueryParams(params);
            const response = await this.api.get<ActivityLogsResponse>(`/?${queryParams}`);
            return response.data;
        });
    }

    // Get analytics data
    async getAnalytics(params: AnalyticsParams = {}): Promise<AnalyticsResponse> {
        return this.retryRequest(async () => {
            const queryParams = this.buildQueryParams(params);
            const response = await this.api.get<AnalyticsResponse>(`/analytics?${queryParams}`);
            return response.data;
        });
    }

    // Create a new activity log
    async createLog(data: CreateLogData): Promise<CreateLogResponse> {
        return this.retryRequest(async () => {
            const response = await this.api.post<CreateLogResponse>('/', data);
            return response.data;
        });
    }

    // Bulk create activity logs
    async createBulkLogs(logs: CreateLogData[]): Promise<{ success: boolean; inserted: number; message: string }> {
        return this.retryRequest(async () => {
            const response = await this.api.post('/bulk', { logs });
            return response.data;
        });
    }

    // Export activity logs
    async exportLogs(params: ExportParams): Promise<Blob> {
        return this.retryRequest(async () => {
            const queryParams = this.buildQueryParams({
                ...params,
                export: 'true',
            });

            const response = await this.api.get(`/?${queryParams}`, {
                responseType: 'blob',
                timeout: 60000, // 60 seconds for export operations
            });

            return response.data;
        });
    }

    // Get available actions
    async getAvailableActions(): Promise<string[]> {
        return this.retryRequest(async () => {
            const response = await this.api.get<{ success: boolean; actions: string[] }>('/actions');
            return response.data.actions;
        });
    }

    // Get available resource types
    async getAvailableResourceTypes(): Promise<string[]> {
        return this.retryRequest(async () => {
            const response = await this.api.get<{ success: boolean; resourceTypes: string[] }>('/resource-types');
            return response.data.resourceTypes;
        });
    }

    // Get available users
    async getAvailableUsers(organizationId?: string): Promise<User[]> {
        return this.retryRequest(async () => {
            const params = organizationId ? { organizationId } : {};
            const queryParams = this.buildQueryParams(params);
            const response = await this.api.get<{ success: boolean; users: User[] }>(`/users?${queryParams}`);
            return response.data.users;
        });
    }

    // Delete a specific log (admin only)
    async deleteLog(logId: string): Promise<{ success: boolean; message: string }> {
        return this.retryRequest(async () => {
            const response = await this.api.delete(`/${logId}`);
            return response.data;
        });
    }

    // Bulk delete logs (admin only)
    async bulkDeleteLogs(params: Partial<GetLogsParams>): Promise<{ success: boolean; deleted: number; message: string }> {
        return this.retryRequest(async () => {
            const queryParams = this.buildQueryParams(params);
            const response = await this.api.delete(`/?${queryParams}`);
            return response.data;
        });
    }
}

// Export singleton instance
export const activityLogApiService = new ActivityLogApiService();
export default activityLogApiService;