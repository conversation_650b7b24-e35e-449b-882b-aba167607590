'use client';

import { memo, useState, useEffect } from 'react';
import { <PERSON><PERSON>, Position, type NodeProps } from 'reactflow';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Clock, AlertTriangle, Edit3 } from 'lucide-react';
import { useNodeEdit } from '../contexts/node-edit-context';
import { useMindMapStore } from '@/stores/mindMapStore';
import { DeadlineNodeForm } from '../node-forms/deadline-node-form';

interface DeadlineNodeData {
  title: string;
  date: string;
  timeRemaining: string;
  urgency: 'low' | 'medium' | 'high' | 'critical';
}

export const DeadlineNode = memo(({ id, data, selected }: NodeProps<DeadlineNodeData>) => {
  const { isEditing, startEditing, stopEditing } = useNodeEdit();
  const { updateNode } = useMindMapStore();
  const [isHovered, setIsHovered] = useState(false);
  const [countdown, setCountdown] = useState('');

  const urgencyColors = {
    low: 'bg-green-100 text-green-800 border-green-200',
    medium: 'bg-yellow-100 text-yellow-800 border-yellow-200',
    high: 'bg-orange-100 text-orange-800 border-orange-200',
    critical: 'bg-red-100 text-red-800 border-red-200',
  };

  const urgencyIndicators = {
    low: 0,
    medium: 1,
    high: 2,
    critical: 3,
  };

  // Update countdown timer
  useEffect(() => {
    const updateCountdown = () => {
      const now = new Date();
      const deadline = new Date(data.date);
      const diff = deadline.getTime() - now.getTime();

      if (diff <= 0) {
        setCountdown('Overdue');
        return;
      }

      const days = Math.floor(diff / (1000 * 60 * 60 * 24));
      const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

      if (days > 0) {
        setCountdown(`${days}d ${hours}h`);
      } else if (hours > 0) {
        setCountdown(`${hours}h ${minutes}m`);
      } else {
        setCountdown(`${minutes}m`);
      }
    };

    updateCountdown();
    const interval = setInterval(updateCountdown, 60000); // Update every minute

    return () => clearInterval(interval);
  }, [data.date]);

  const handleDoubleClick = () => {
    startEditing(id, data);
  };

  const handleSave = (updatedData: Partial<DeadlineNodeData>) => {
    updateNode(id, {
      content: {
        text: updatedData.title || 'New Deadline',
        ...updatedData,
      },
    });
    stopEditing();
  };

  if (isEditing(id)) {
    return <DeadlineNodeForm data={data} onSave={handleSave} onCancel={stopEditing} />;
  }

  return (
    <Card
      className={`min-w-[220px] bg-gradient-to-br from-red-50 to-pink-50 border-red-200 shadow-lg transition-all duration-200 ${
        selected ? 'ring-2 ring-red-500 shadow-xl scale-105' : 'hover:shadow-xl hover:scale-102'
      }`}
      onDoubleClick={handleDoubleClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <Handle
        type="target"
        position={Position.Top}
        className="w-3 h-3 bg-red-500 border-2 border-white"
      />

      <div className="p-4">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-2">
            <Clock className="h-5 w-5 text-red-600" />
            <h3 className="font-semibold text-gray-900 text-sm">{data.title}</h3>
          </div>
          {isHovered && (
            <Edit3
              className="h-3 w-3 text-gray-400 cursor-pointer hover:text-gray-600"
              onClick={() => startEditing(id, data)}
            />
          )}
        </div>

        <div className="flex items-center justify-between mb-2">
          <span className="text-xs text-gray-600">{new Date(data.date).toLocaleDateString()}</span>
          <Badge variant="outline" className={`text-xs ${urgencyColors[data.urgency]}`}>
            {data.urgency}
          </Badge>
        </div>

        <div className="flex items-center justify-between bg-white/70 rounded-md p-2 border border-red-100">
          <div className="flex items-center">
            <AlertTriangle
              className={`h-4 w-4 ${data.urgency === 'critical' ? 'text-red-500' : 'text-yellow-500'}`}
            />
            <span className="ml-2 text-sm font-medium">{countdown}</span>
          </div>
          <div className="flex">
            {Array(urgencyIndicators[data.urgency] + 1)
              .fill(0)
              .map((_, i) => (
                <div
                  key={i}
                  className={`w-1.5 h-6 mx-0.5 rounded-full ${
                    i === 0
                      ? 'bg-green-500'
                      : i === 1
                        ? 'bg-yellow-500'
                        : i === 2
                          ? 'bg-orange-500'
                          : 'bg-red-500'
                  }`}
                />
              ))}
          </div>
        </div>
      </div>

      <Handle
        type="source"
        position={Position.Bottom}
        className="w-3 h-3 bg-red-500 border-2 border-white"
      />
    </Card>
  );
});

DeadlineNode.displayName = 'DeadlineNode';
