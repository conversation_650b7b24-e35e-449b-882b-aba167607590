import axios from 'axios';

export interface SearchQuery {
  query: string;
  type?: string;
  status?: string;
  priority?: string;
  tags?: string;
  project?: string;
  page?: number;
  limit?: number;
}

export interface SearchResult {
  id: string;
  type: 'task' | 'project' | 'note' | 'user';
  title: string;
  description?: string;
  url: string;
  relevanceScore: number;
  metadata: Record<string, any>;
  highlights?: Record<string, string[]>;
}

export interface SearchResponse {
  results: SearchResult[];
  total: number;
  page: number;
  totalPages: number;
  searchTime: number;
}

export class GlobalSearchService {
  static async search(params: SearchQuery): Promise<SearchResponse> {
    const response = await axios.get('/api/search', { params });
    return response.data;
  }
}
