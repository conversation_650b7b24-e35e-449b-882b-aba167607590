import axios from 'axios';
import { ISubscription } from '@/models/Subscription';
import { ReceiptGenerator } from '@/utils/receiptGenerator';
export interface StorageTier {
  id: string;
  name: string;
  storageBytes: number;
  storageFormatted: string;
  priceMonthly: number;
  priceYearly: number;
  isDefault: boolean;
  features?: string[];
  gracePeriod: number; // days
}

export const STORAGE_TIERS: StorageTier[] = [
  {
    id: 'basic',
    name: 'Basic Storage',
    storageBytes: 104857600, // 100MB
    storageFormatted: '100MB',
    priceMonthly: 99,
    priceYearly: 990,
    isDefault: true,
    gracePeriod: 7,
  },
  {
    id: 'standard',
    name: 'Standard Storage',
    storageBytes: 1073741824, // 1GB
    storageFormatted: '1GB',
    priceMonthly: 299,
    priceYearly: 2990,
    isDefault: false,
    gracePeriod: 7,
  },
  {
    id: 'premium',
    name: 'Premium Storage',
    storageBytes: 5368709120, // 5GB
    storageFormatted: '5GB',
    priceMonthly: 999,
    priceYearly: 9990,
    isDefault: false,
    gracePeriod: 14,
  },
  {
    id: 'enterprise',
    name: 'Enterprise Storage',
    storageBytes: 21474836480, // 20GB
    storageFormatted: '20GB',
    priceMonthly: 2999,
    priceYearly: 29990,
    isDefault: false,
    gracePeriod: 30,
  },
];

export interface SubscriptionPlan {
  id: 'free' | 'starter' | 'professional' | 'enterprise';
  name: string;
  description: string;
  price: {
    monthly: number;
    yearly: number;
    yearlyDiscount: number; // percentage
  };
  features: {
    users: number | 'unlimited';
    projects: number | 'unlimited';
    storage: string;
    integrations: number | 'unlimited';
    advancedAnalytics: boolean;
    customIntegrations: boolean;
    prioritySupport: boolean;
    ssoEnabled: boolean;
    apiAccess: boolean;
    customBranding: boolean;
    advancedSecurity: boolean;
  };
  popular?: boolean;
  recommended?: boolean;
}

export const SUBSCRIPTION_PLANS: SubscriptionPlan[] = [
  {
    id: 'free',
    name: 'Free',
    description: 'Perfect for individuals and small teams getting started',
    price: {
      monthly: 0,
      yearly: 0,
      yearlyDiscount: 0,
    },
    features: {
      users: 3,
      projects: 5,
      storage: '50MB',
      integrations: 2,
      advancedAnalytics: false,
      customIntegrations: false,
      prioritySupport: false,
      ssoEnabled: false,
      apiAccess: false,
      customBranding: false,
      advancedSecurity: false,
    },
  },
  {
    id: 'starter',
    name: 'Starter',
    description: 'Great for growing teams and small businesses',
    price: {
      monthly: 299,
      yearly: 2990, // ~17% discount
      yearlyDiscount: 17,
    },
    features: {
      users: 10,
      projects: 25,
      storage: '1GB',
      integrations: 5,
      advancedAnalytics: true,
      customIntegrations: false,
      prioritySupport: false,
      ssoEnabled: false,
      apiAccess: true,
      customBranding: false,
      advancedSecurity: false,
    },
    popular: true,
  },
  {
    id: 'professional',
    name: 'Professional',
    description: 'Perfect for established teams and growing companies',
    price: {
      monthly: 599,
      yearly: 5990, // ~17% discount
      yearlyDiscount: 17,
    },
    features: {
      users: 50,
      projects: 100,
      storage: '5GB',
      integrations: 15,
      advancedAnalytics: true,
      customIntegrations: true,
      prioritySupport: true,
      ssoEnabled: false,
      apiAccess: true,
      customBranding: true,
      advancedSecurity: true,
    },
    recommended: true,
  },
  {
    id: 'enterprise',
    name: 'Enterprise',
    description: 'Advanced features for large organizations',
    price: {
      monthly: 1299,
      yearly: 12990, // ~17% discount
      yearlyDiscount: 17,
    },
    features: {
      users: 'unlimited',
      projects: 'unlimited',
      storage: '20GB',
      integrations: 'unlimited',
      advancedAnalytics: true,
      customIntegrations: true,
      prioritySupport: true,
      ssoEnabled: true,
      apiAccess: true,
      customBranding: true,
      advancedSecurity: true,
    },
  },
];

export interface SubscriptionInfo {
  subscription: ISubscription;
  plan: SubscriptionPlan;
  usage: {
    users: number;
    projects: number;
    storage: {
      used: number;
      limit: number;
      percentage: number;
    };
    integrations: number;
  };
  billing: {
    nextPaymentDate?: Date;
    lastPaymentDate?: Date;
    amount: number;
    currency: string;
    billingCycle: string;
  };
  canUpgrade: boolean;
  canDowngrade: boolean;
}

export interface StorageQuotaInfo {
  basePlanStorage: number;
  additionalPurchasedStorage: number;
  totalAvailableStorage: number;
  usedStorage: number;
  availableStorage: number;
  usagePercentage: number;
  isOverQuota: boolean;
  daysInGracePeriod: number | null;
  gracePeriodEnd: Date | null;
  recommendedTier: StorageTier | null;
  currentTier: StorageTier | null;
  canDowngrade: boolean;
  canUpgrade: boolean;
}

export interface ProratedBillingInfo {
  currentPlanEndDate: Date;
  daysRemaining: number;
  totalDaysInBillingCycle: number;
  currentPlanRefundAmount: number;
  newPlanProRatedAmount: number;
  totalDue: number;
  savingsAmount: number | null;
  effectiveDate: Date;
}

export class SubscriptionService {
  static async getSubscriptionInfo(organizationId: string): Promise<SubscriptionInfo> {
    try {
      const response = await axios.get(`/api/subscription/info?organizationId=${organizationId}`);
      return response.data.data;
    } catch (error) {
      throw new Error('Failed to fetch subscription information');
    }
  }

  static async upgradePlan(
    organizationId: string,
    newPlan: string,
    billingCycle: 'monthly' | 'yearly'
  ): Promise<{ success: boolean; message: string }> {
    try {
      const response = await axios.post('/api/subscription/upgrade', {
        organizationId,
        newPlan,
        billingCycle,
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Failed to upgrade plan');
    }
  }

  static async downgradePlan(
    organizationId: string,
    newPlan: string
  ): Promise<{ success: boolean; message: string }> {
    try {
      const response = await axios.post('/api/subscription/downgrade', {
        organizationId,
        newPlan,
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Failed to downgrade plan');
    }
  }

  static async cancelSubscription(
    organizationId: string,
    reason?: string
  ): Promise<{ success: boolean; message: string }> {
    try {
      const response = await axios.post('/api/subscription/cancel', {
        organizationId,
        reason,
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Failed to cancel subscription');
    }
  }

  static async getBillingHistory(organizationId: string): Promise<any[]> {
    try {
      const response = await axios.get(
        `/api/subscription/billing-history?organizationId=${organizationId}`
      );
      return response.data.data;
    } catch (error) {
      throw new Error('Failed to fetch billing history');
    }
  }

  static async downloadReceipt(transactionId: string): Promise<any> {
    try {
      const response = await axios.get(`/api/subscription/receipt/${transactionId}`);
      return response.data.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Failed to download receipt');
    }
  }

  static async downloadReceiptFile(transactionId: string): Promise<void> {
    try {
      const receiptData = await this.downloadReceipt(transactionId);
      ReceiptGenerator.downloadReceipt(receiptData);
    } catch (error: any) {
      throw new Error(error.message || 'Failed to download receipt file');
    }
  }

  static async printReceipt(transactionId: string): Promise<void> {
    try {
      const receiptData = await this.downloadReceipt(transactionId);
      ReceiptGenerator.printReceipt(receiptData);
    } catch (error: any) {
      throw new Error(error.message || 'Failed to print receipt');
    }
  }

  static getPlanById(planId: string): SubscriptionPlan | undefined {
    return SUBSCRIPTION_PLANS.find(plan => plan.id === planId);
  }

  static calculateYearlyDiscount(plan: SubscriptionPlan): number {
    const monthlyTotal = plan.price.monthly * 12;
    const yearlyPrice = plan.price.yearly;
    return monthlyTotal - yearlyPrice;
  }

  static formatPrice(amount: number): string {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
    }).format(amount);
  }

  static isFeatureAvailable(
    subscription: ISubscription,
    feature: keyof ISubscription['features']
  ): boolean {
    return subscription.features[feature] || false;
  }

  static canUpgradeToplan(currentPlan: string, targetPlan: string): boolean {
    const planOrder = ['free', 'starter', 'professional', 'enterprise'];
    const currentIndex = planOrder.indexOf(currentPlan);
    const targetIndex = planOrder.indexOf(targetPlan);
    return targetIndex > currentIndex;
  }

  static canDowngradeToplan(currentPlan: string, targetPlan: string): boolean {
    const planOrder = ['free', 'starter', 'professional', 'enterprise'];
    const currentIndex = planOrder.indexOf(currentPlan);
    const targetIndex = planOrder.indexOf(targetPlan);
    return targetIndex < currentIndex;
  }

  // New methods for storage tier management

  static getStorageTierById(tierId: string): StorageTier | undefined {
    return STORAGE_TIERS.find(tier => tier.id === tierId);
  }

  static getDefaultStorageTier(): StorageTier {
    return STORAGE_TIERS.find(tier => tier.isDefault) || STORAGE_TIERS[0];
  }

  static async getStorageQuotaInfo(organizationId: string): Promise<StorageQuotaInfo> {
    try {
      const response = await axios.get(
        `/api/subscription/storage-quota?organizationId=${organizationId}`
      );
      return response.data.data;
    } catch (error) {
      throw new Error('Failed to fetch storage quota information');
    }
  }

  static calculateRecommendedTier(storageUsage: number): StorageTier {
    // Find the smallest tier that fits the usage with 20% buffer
    const requiredStorage = storageUsage * 1.2;

    const recommendedTier = STORAGE_TIERS.find(tier => tier.storageBytes >= requiredStorage);
    return recommendedTier || STORAGE_TIERS[STORAGE_TIERS.length - 1]; // Return highest tier if none fits
  }

  static async upgradeStorageTier(
    organizationId: string,
    tierId: string,
    billingCycle: 'monthly' | 'yearly'
  ): Promise<{ success: boolean; message: string }> {
    try {
      const response = await axios.post('/api/subscription/storage-tiers/upgrade', {
        organizationId,
        tierId,
        billingCycle,
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Failed to upgrade storage tier');
    }
  }

  static async calculateProratedBilling(
    organizationId: string,
    targetPlanId: string,
    billingCycle: 'monthly' | 'yearly'
  ): Promise<ProratedBillingInfo> {
    try {
      const response = await axios.post('/api/subscription/calculate-prorated', {
        organizationId,
        targetPlanId,
        billingCycle,
      });
      return response.data.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Failed to calculate prorated billing');
    }
  }

  static async requestQuotaExtension(
    organizationId: string,
    additionalDays: number,
    reason: string
  ): Promise<{ success: boolean; message: string; newGracePeriodEnd: Date }> {
    try {
      const response = await axios.post('/api/storage/quota-enforcement/extension', {
        organizationId,
        additionalDays,
        reason,
      });
      return response.data.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Failed to request quota extension');
    }
  }

  static async calculateStorageOverage(
    organizationId: string
  ): Promise<{ overageBytes: number; overageCost: number; billingDate: Date }> {
    try {
      const response = await axios.get(
        `/api/subscription/storage-overage?organizationId=${organizationId}`
      );
      return response.data.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Failed to calculate storage overage');
    }
  }

  static async processStorageOverageBilling(
    organizationId: string,
    paymentMethod?: string
  ): Promise<{ success: boolean; message: string; transactionId?: string }> {
    try {
      const response = await axios.post('/api/subscription/process-overage', {
        organizationId,
        paymentMethod,
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Failed to process overage billing');
    }
  }

  static async getSubscriptionAnalytics(
    organizationId: string,
    period: 'week' | 'month' | 'year' = 'month'
  ): Promise<any> {
    try {
      const response = await axios.get(
        `/api/subscription/analytics?organizationId=${organizationId}&period=${period}`
      );
      return response.data.data;
    } catch (error) {
      throw new Error('Failed to fetch subscription analytics');
    }
  }

  static async getStorageUsageAnalytics(
    organizationId: string,
    period: 'week' | 'month' | 'year' = 'month'
  ): Promise<any> {
    try {
      const response = await axios.get(
        `/api/storage/analytics?organizationId=${organizationId}&period=${period}`
      );
      return response.data.data;
    } catch (error) {
      throw new Error('Failed to fetch storage usage analytics');
    }
  }

  static async forecastStorageNeeds(
    organizationId: string,
    months: number = 3
  ): Promise<{
    forecastedUsage: { date: Date; projectedBytes: number }[];
    recommendedTier: StorageTier;
    projectedOverageCosts: number;
  }> {
    try {
      const response = await axios.get(
        `/api/storage/forecast?organizationId=${organizationId}&months=${months}`
      );
      return response.data.data;
    } catch (error) {
      throw new Error('Failed to forecast storage needs');
    }
  }

  static async autoRenewSubscription(
    organizationId: string,
    autoRenew: boolean
  ): Promise<{ success: boolean; message: string }> {
    try {
      const response = await axios.post('/api/subscription/auto-renew', {
        organizationId,
        autoRenew,
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Failed to update auto-renewal settings');
    }
  }

  static async scheduleDowngrade(
    organizationId: string,
    targetPlanId: string,
    effectiveDate: Date
  ): Promise<{ success: boolean; message: string; scheduledDate: Date }> {
    try {
      const response = await axios.post('/api/subscription/schedule-downgrade', {
        organizationId,
        targetPlanId,
        effectiveDate,
      });
      return response.data.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Failed to schedule downgrade');
    }
  }

  static storageQuotaThresholds = {
    warning: 75, // percentage
    critical: 90, // percentage
    gracePeriodDefault: 7, // days
  };
}
