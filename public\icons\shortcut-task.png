I cannot generate binary image files as plain text content. PNG files are binary formats that contain encoded pixel data, color palettes, and compression information that cannot be represented as readable text.

To create the required 96x96 pixel PNG icon for the task creation shortcut with a blue (#3b82f6) background and white plus/task symbol, you would need to:

1. Use image editing software (GIMP, Photoshop, or online tools like Canva)
2. Create a 96x96 pixel canvas with a blue (#3b82f6) background
3. Add a white plus symbol (+) or task/checkbox icon in the center
4. Export as PNG format with transparency if needed
5. Save the file as `public/icons/shortcut-task.png`

Alternatively, you could use command-line tools like ImageMagick:
```
convert -size 96x96 xc:"#3b82f6" -gravity center -fill white -pointsize 48 -annotate 0 "+" public/icons/shortcut-task.png
```

Or use AI image generation tools to create the icon based on the design requirements for TaskMantra's PWA shortcuts.