import { Hono } from 'hono';
import { handle } from 'hono/vercel';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../auth/[...nextauth]/options';
import { connectDB } from '@/Utility/db';
import { Task } from '@/models/Task';
import { Project } from '@/models/Project';
import { User } from '@/models/User';
import Note from '@/models/Note';
import { logger } from 'hono/logger';

// Define user data type
interface UserData {
  id: string;
  name: string;
  email: string;
  image: string;
  organizationId?: string;
}

interface CustomVariables {
  user?: UserData;
}

const app = new Hono<{ Variables: CustomVariables }>().basePath('/api/search');

app.use('*', logger());

// Auth middleware
app.use('*', async (c, next) => {
  try {
    const session = await getServerSession(authOptions);
    if (session?.user) {
      const userData: UserData = {
        id: session.user.id || '',
        name: session.user.name || '',
        email: session.user.email || '',
        image: session.user.image || '',
        organizationId: (session.user as any).organizationId || '',
      };
      c.set('user', userData);
    }
  } catch (error: any) {
    throw new Error(error.message);
  }
  await next();
});

app.get('/', async c => {
  const user = c.get('user') as UserData | undefined;
  if (!user) {
    return c.json({ error: 'User not authenticated' }, 401);
  }
  const { q, type, status, priority, tags, project, page = '1', limit = '20' } = c.req.query();
  const startTime = Date.now();
  await connectDB();
  const pageNum = parseInt(page);
  const limitNum = parseInt(limit);
  const skip = (pageNum - 1) * limitNum;
  const sanitizedQuery = sanitizeQuery(q || '');
  const searchTerms = sanitizedQuery.split(/\s+/).filter(Boolean);
  const filters = buildFilters(user.organizationId || '', { status, priority, tags, project });
  const searchCondition = searchTerms.length ? { $text: { $search: sanitizedQuery } } : {};
  const query = { ...filters, ...searchCondition };
  const contentTypes = type ? type.split(',') : ['task', 'project', 'note', 'user'];
  type SearchResult = ReturnType<typeof formatResult>;
  const results: SearchResult[] = [];

  // Search each content type
  if (contentTypes.includes('task')) {
    const tasks = await Task.find(query)
      .populate('assignedTo', 'name')
      .populate('projectId', 'name')
      .skip(skip)
      .limit(limitNum);
    tasks.forEach(task => {
      results.push(formatResult(task, 'task', searchTerms));
    });
  }
  if (contentTypes.includes('project')) {
    const projects = await Project.find(query).skip(skip).limit(limitNum);
    projects.forEach(project => {
      results.push(formatResult(project, 'project', searchTerms));
    });
  }
  if (contentTypes.includes('note')) {
    const notes = await Note.find(query).populate('createdBy', 'name').skip(skip).limit(limitNum);
    notes.forEach(note => {
      results.push(formatResult(note, 'note', searchTerms));
    });
  }
  if (contentTypes.includes('user')) {
    const users = await User.find(query).skip(skip).limit(limitNum);
    users.forEach(user => {
      results.push(formatResult(user, 'user', searchTerms));
    });
  }

  // Sort by relevance
  results.sort((a, b) => b.relevanceScore - a.relevanceScore);

  const total = await getTotalCount(query, contentTypes);

  return c.json({
    results,
    total,
    page: pageNum,
    totalPages: Math.ceil(total / limitNum),
    searchTime: Date.now() - startTime,
  });
});

// Helper functions
function sanitizeQuery(query: string): string {
  return (
    query
      ?.trim()
      .replace(/[<>;"'\\]/g, '')
      .substring(0, 500) || ''
  );
}

function buildFilters(organizationId: string, filters: any) {
  const query: any = { organizationId };

  if (filters.status) query.status = { $in: filters.status.split(',') };
  if (filters.priority) query.priority = { $in: filters.priority.split(',') };
  if (filters.tags) query.tags = { $in: filters.tags.split(',') };
  if (filters.project) query.projectId = filters.project;

  return query;
}

function formatResult(doc: any, type: string, searchTerms: string[]) {
  const relevanceScore = calculateRelevance(doc, searchTerms);
  const highlights = generateHighlights(doc, searchTerms);

  const baseResult = {
    id: doc._id.toString(),
    type,
    relevanceScore,
    highlights,
  };

  switch (type) {
    case 'task':
      return {
        ...baseResult,
        title: doc.name,
        description: doc.description,
        url: `/tasks/${doc._id}`,
        metadata: {
          status: doc.status,
          priority: doc.priority,
          assignee: doc.assignedTo?.[0]?.name,
          projectName: doc.projectId?.name,
          createdAt: doc.createdAt,
        },
      };
    case 'project':
      return {
        ...baseResult,
        title: doc.name,
        description: doc.description,
        url: `/projects/${doc._id}`,
        metadata: {
          status: doc.status,
          membersCount: doc.members?.length || 0,
          createdAt: doc.createdAt,
        },
      };
    case 'note':
      return {
        ...baseResult,
        title: doc.title,
        description: doc.content?.substring(0, 150),
        url: `/notes/${doc._id}`,
        metadata: {
          createdBy: doc.createdBy?.name,
          createdAt: doc.createdAt,
        },
      };
    case 'user':
      return {
        ...baseResult,
        title: doc.name,
        description: `${doc.email} - ${doc.role || 'User'}`,
        url: `/users/${doc._id}`,
        metadata: {
          role: doc.role,
          email: doc.email,
          createdAt: doc.createdAt,
        },
      };
    default:
      throw new Error('Unknown type');
  }
}

function calculateRelevance(doc: any, searchTerms: string[]): number {
  if (!searchTerms.length) return 1;

  let score = 0;
  const text =
    `${doc.name || doc.title || ''} ${doc.description || doc.content || ''}`.toLowerCase();

  searchTerms.forEach(term => {
    const termLower = term.toLowerCase();
    if (text.includes(termLower)) {
      score += text.split(termLower).length - 1;
    }
  });

  return Math.min(score / searchTerms.length, 10);
}

function generateHighlights(doc: any, searchTerms: string[]) {
  if (!searchTerms.length) return {};

  const highlights: any = {};
  const title = doc.name || doc.title || '';
  const content = doc.description || doc.content || '';

  searchTerms.forEach(term => {
    const regex = new RegExp(`(${term})`, 'gi');
    if (title.match(regex)) {
      highlights.title = highlights.title || [];
      highlights.title.push(title.replace(regex, '<mark>$1</mark>'));
    }
    if (content.match(regex)) {
      highlights.content = highlights.content || [];
      highlights.content.push(content.substring(0, 200).replace(regex, '<mark>$1</mark>'));
    }
  });

  return highlights;
}

async function getTotalCount(query: any, contentTypes: string[]): Promise<number> {
  let total = 0;

  if (contentTypes.includes('task')) total += await Task.countDocuments(query);
  if (contentTypes.includes('project')) total += await Project.countDocuments(query);
  if (contentTypes.includes('note')) total += await Note.countDocuments(query);
  if (contentTypes.includes('user')) total += await User.countDocuments(query);

  return total;
}

export const GET = handle(app);
export const POST = handle(app);
