import { gzip } from 'pako';

// Connection health status type
export interface ConnectionHealth {
  status: 'healthy' | 'degraded' | 'failing';
  score: number;
  lastCheck: number;
  failures: number;
  successRate: number;
}

// Connection info type
export interface ConnectionInfo {
  userId: string;
  controller: ReadableStreamDefaultController;
  priority: number;
  timestamp: number;
  lastActivity: number;
  health: ConnectionHealth;
  heartbeatInterval?: ReturnType<typeof setTimeout>;
  subscriptionTier?: string;
}

// Connection metrics type
export interface ConnectionMetrics {
  activeConnections: number;
  totalConnections: number;
  droppedConnections: number;
  messagesDelivered: number;
  messagesFailed: number;
  avgResponseTime: number;
  compressionRatio: number;
  resourceUsage: {
    memory: number;
    cpu: number;
  };
  connectionsByHealth: {
    healthy: number;
    degraded: number;
    failing: number;
  };
  connectionsByTier: Record<string, number>;
}

// Configuration interface
export interface SSEConfig {
  maxConcurrentConnections: number;
  idleTimeout: number;
  heartbeatIntervalBase: number;
  heartbeatIntervalMin: number;
  heartbeatIntervalMax: number;
  maxConnectionDuration: number;
  batchThresholdSize: number;
  compressionThreshold: number;
  cleanupInterval: number;
  healthCheckInterval: number;
  enableCompression: boolean;
  enableLoadBalancing: boolean;
  debug: boolean;
}

// Event data interface
export interface EventData {
  type: string;
  data: any;
  timestamp?: number;
  compress?: boolean;
}

export class SSEConnectionManager {
  private static instance: SSEConnectionManager;
  private connections: Map<string, ConnectionInfo> = new Map();
  private metrics: ConnectionMetrics;
  private config: SSEConfig;
  private cleanupTimer?: ReturnType<typeof setTimeout>;
  private healthCheckTimer?: ReturnType<typeof setTimeout>;

  private constructor(config?: Partial<SSEConfig>) {
    this.config = {
      maxConcurrentConnections: 50,
      idleTimeout: 5 * 60 * 1000, // 5 minutes
      heartbeatIntervalBase: 20000, // 20 seconds
      heartbeatIntervalMin: 10000, // 10 seconds
      heartbeatIntervalMax: 60000, // 60 seconds
      maxConnectionDuration: 25 * 60 * 1000, // 25 minutes
      batchThresholdSize: 10,
      compressionThreshold: 1000, // bytes
      cleanupInterval: 60000, // 1 minute
      healthCheckInterval: 30000, // 30 seconds
      enableCompression: true,
      enableLoadBalancing: true,
      debug: process.env.NODE_ENV === 'development',
      ...config,
    };

    this.metrics = {
      activeConnections: 0,
      totalConnections: 0,
      droppedConnections: 0,
      messagesDelivered: 0,
      messagesFailed: 0,
      avgResponseTime: 0,
      compressionRatio: 0,
      resourceUsage: {
        memory: 0,
        cpu: 0,
      },
      connectionsByHealth: {
        healthy: 0,
        degraded: 0,
        failing: 0,
      },
      connectionsByTier: {},
    };

    this.startBackgroundTasks();
  }

  public static getInstance(config?: Partial<SSEConfig>): SSEConnectionManager {
    if (!SSEConnectionManager.instance) {
      SSEConnectionManager.instance = new SSEConnectionManager(config);
    }
    return SSEConnectionManager.instance;
  }

  // Add a new connection
  public addConnection(
    userId: string,
    controller: ReadableStreamDefaultController,
    priority: number = 5,
    subscriptionTier: string = 'free'
  ): boolean {
    // Check if we should allow this connection
    if (!this.shouldAllowConnection(userId, priority)) {
      return false;
    }

    // Clean up any existing connection for this user
    this.removeConnection(userId);

    const now = Date.now();
    const connectionInfo: ConnectionInfo = {
      userId,
      controller,
      priority,
      timestamp: now,
      lastActivity: now,
      subscriptionTier,
      health: {
        status: 'healthy',
        score: 10,
        lastCheck: now,
        failures: 0,
        successRate: 1.0,
      },
    };

    this.connections.set(userId, connectionInfo);
    this.updateMetrics();

    // Start heartbeat for this connection
    this.startHeartbeat(connectionInfo);

    // Send initial connection message
    this.sendConnectionMessage(connectionInfo);

    return true;
  }

  // Remove a connection
  public removeConnection(userId: string): void {
    console.log(`[SSE] Removing connection for userId: ${userId}`);
    const connection = this.connections.get(userId);
    if (!connection) return;

    // Clear heartbeat
    if (connection.heartbeatInterval) {
      clearInterval(connection.heartbeatInterval);
    }

    // Close controller if still open
    try {
      connection.controller.close();
    } catch (error) {
      // Ignore errors during close
    }

    this.connections.delete(userId);
    this.updateMetrics();
  }

  // Disconnect a user with reason
  public disconnectUser(userId: string, reason: string): void {
    console.log(`[SSE] Disconnecting userId: ${userId} for reason: ${reason}`);
    const connection = this.connections.get(userId);
    if (!connection) return;

    try {
      // Send disconnect message
      const disconnectMsg = this.formatEventData({
        type: 'disconnect',
        data: {
          reason,
          reconnectDelay: this.calculateReconnectDelay(reason),
        },
      });

      connection.controller.enqueue(new TextEncoder().encode(disconnectMsg));
    } catch (error) {
      // Ignore errors during disconnect
    }

    this.removeConnection(userId);
    this.metrics.droppedConnections++;
  }

  // Broadcast message to all connections
  public broadcast(
    eventData: EventData,
    filterFn?: (connection: ConnectionInfo) => boolean
  ): number {
    let delivered = 0;
    const message = this.formatEventData(eventData);

    for (const [userId, connection] of this.connections.entries()) {
      if (this.config.debug) {
        console.log(`[SSE] Broadcasting to userId: ${userId}`);
      }
      if (filterFn && !filterFn(connection)) {
        continue;
      }

      if (this.sendToConnection(connection, message)) {
        delivered++;
      }
    }

    return delivered;
  }

  // Send message to specific user
  public sendToUser(userId: string, eventData: EventData): boolean {
    const connection = this.connections.get(userId);
    if (!connection) return false;

    const message = this.formatEventData(eventData);
    return this.sendToConnection(connection, message);
  }

  // Send batch of notifications to user
  public sendBatchToUser(userId: string, notifications: any[]): boolean {
    const connection = this.connections.get(userId);
    if (!connection) return false;

    const eventData: EventData = {
      type: 'notifications',
      data: {
        notifications,
        batchSize: notifications.length,
        compressionEnabled: this.config.enableCompression,
      },
    };

    const message = this.formatEventData(eventData);
    return this.sendToConnection(connection, message);
  }

  // Get connection metrics
  public getMetrics(): ConnectionMetrics {
    this.updateResourceUsage();
    return { ...this.metrics };
  }

  // Get connection health status
  public getConnectionHealth(userId: string): ConnectionHealth | null {
    const connection = this.connections.get(userId);
    return connection ? { ...connection.health } : null;
  }

  // Update connection configuration
  public updateConfig(newConfig: Partial<SSEConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  // Graceful shutdown
  public shutdown(): void {
    // Stop background tasks
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }
    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer);
    }

    // Disconnect all connections
    for (const [userId] of this.connections.entries()) {
      this.disconnectUser(userId, 'server_shutdown');
    }
  }

  // Private methods

  private shouldAllowConnection(userId: string, priority: number): boolean {
    console.log(
      `[SSE] Checking if connection is allowed for userId: ${userId} with priority: ${priority}`
    );
    if (this.connections.size < this.config.maxConcurrentConnections) {
      return true;
    }

    if (!this.config.enableLoadBalancing) {
      return false;
    }

    // Find lowest priority connection to potentially replace
    const lowestPriorityConnection = [...this.connections.values()]
      .sort((a, b) => a.priority - b.priority)
      .find(conn => conn.priority < priority);

    if (lowestPriorityConnection) {
      this.disconnectUser(lowestPriorityConnection.userId, 'priority_displacement');
      return true;
    }

    return false;
  }

  private startHeartbeat(connection: ConnectionInfo): void {
    const interval = this.calculateHeartbeatInterval(connection);

    connection.heartbeatInterval = setInterval(() => {
      this.sendHeartbeat(connection);
    }, interval);
  }

  private sendHeartbeat(connection: ConnectionInfo): void {
    // Check connection duration
    if (Date.now() - connection.timestamp > this.config.maxConnectionDuration) {
      this.disconnectUser(connection.userId, 'max_duration_exceeded');
      return;
    }

    const heartbeatData: EventData = {
      type: 'heartbeat',
      data: {
        timestamp: new Date().toISOString(),
        health: connection.health.status,
        nextInterval: this.calculateHeartbeatInterval(connection),
      },
    };

    const message = this.formatEventData(heartbeatData);

    if (this.sendToConnection(connection, message)) {
      this.updateConnectionHealth(connection, true);

      // Adjust heartbeat interval if health changed
      const newInterval = this.calculateHeartbeatInterval(connection);
      if (connection.heartbeatInterval && newInterval !== this.config.heartbeatIntervalBase) {
        clearInterval(connection.heartbeatInterval);
        this.startHeartbeat(connection);
      }
    } else {
      this.updateConnectionHealth(connection, false);
    }
  }

  private sendConnectionMessage(connection: ConnectionInfo): void {
    const connectionData: EventData = {
      type: 'connection',
      data: {
        message: 'Connected to notification stream',
        config: {
          heartbeatInterval: this.calculateHeartbeatInterval(connection),
          connectionTTL: this.config.maxConnectionDuration,
          compressionEnabled: this.config.enableCompression,
        },
      },
    };

    const message = this.formatEventData(connectionData);
    this.sendToConnection(connection, message);
  }

  private sendToConnection(connection: ConnectionInfo, message: string): boolean {
    try {
      connection.controller.enqueue(new TextEncoder().encode(message));
      connection.lastActivity = Date.now();
      this.metrics.messagesDelivered++;
      return true;
    } catch (error) {
      this.metrics.messagesFailed++;
      this.removeConnection(connection.userId);
      return false;
    }
  }

  private formatEventData(eventData: EventData): string {
    const eventId = `${eventData.type}-${Date.now()}`;
    const dataString = JSON.stringify(eventData.data);

    // Apply compression if enabled and data is large enough
    if (
      this.config.enableCompression &&
      eventData.compress !== false &&
      dataString.length > this.config.compressionThreshold
    ) {
      try {
        const compressed = gzip(new TextEncoder().encode(dataString));
        this.metrics.compressionRatio = dataString.length / compressed.length;
        // Note: In practice, you'd need to handle compression on client side too
        // For now, we'll just track the ratio but send uncompressed
      } catch (error) {
        if (this.config.debug) {
          console.error('Compression failed:', error);
        }
      }
    }

    return `id: ${eventId}\ndata: ${dataString}\n\n`;
  }

  private calculateHeartbeatInterval(connection: ConnectionInfo): number {
    const health = connection.health;

    if (health.status === 'failing') {
      return this.config.heartbeatIntervalMin;
    } else if (health.status === 'degraded') {
      return this.config.heartbeatIntervalBase;
    } else {
      // For healthy connections, gradually increase interval
      return Math.min(
        this.config.heartbeatIntervalMax,
        this.config.heartbeatIntervalBase + (health.score - 7) * 5000
      );
    }
  }

  private calculateReconnectDelay(reason: string): number {
    switch (reason) {
      case 'priority_displacement':
        return 10000; // 10 seconds
      case 'idle_timeout':
        return 5000; // 5 seconds
      case 'max_duration_exceeded':
        return 30000; // 30 seconds
      case 'server_shutdown':
        return 60000; // 1 minute
      default:
        return 5000; // 5 seconds default
    }
  }

  private updateConnectionHealth(connection: ConnectionInfo, success: boolean): void {
    const health = connection.health;
    const now = Date.now();

    // Update failure count
    if (success) {
      health.failures = Math.max(0, health.failures - 1);
    } else {
      health.failures++;
    }

    // Calculate success rate (over last 10 attempts)
    const totalAttempts = 10;
    health.successRate = Math.max(0, (totalAttempts - health.failures) / totalAttempts);

    // Update score based on success rate
    health.score = health.successRate * 10;

    // Determine status
    if (health.score <= 3) {
      health.status = 'failing';
    } else if (health.score <= 7) {
      health.status = 'degraded';
    } else {
      health.status = 'healthy';
    }

    health.lastCheck = now;
  }

  private startBackgroundTasks(): void {
    // Cleanup idle connections
    this.cleanupTimer = setInterval(() => {
      this.cleanupIdleConnections();
    }, this.config.cleanupInterval);

    // Health checks
    this.healthCheckTimer = setInterval(() => {
      this.performHealthChecks();
    }, this.config.healthCheckInterval);
  }

  private cleanupIdleConnections(): void {
    const now = Date.now();
    const toRemove: string[] = [];

    for (const [userId, connection] of this.connections.entries()) {
      if (now - connection.lastActivity > this.config.idleTimeout) {
        toRemove.push(userId);
      }
    }

    for (const userId of toRemove) {
      this.disconnectUser(userId, 'idle_timeout');
    }
  }

  private performHealthChecks(): void {
    for (const [userId, connection] of this.connections.entries()) {
      // Remove connections that have been failing for too long
      if (connection.health.status === 'failing' && connection.health.failures > 5) {
        this.disconnectUser(userId, 'health_check_failed');
      }
    }
  }

  private updateMetrics(): void {
    this.metrics.activeConnections = this.connections.size;

    // Update health distribution
    this.metrics.connectionsByHealth = {
      healthy: 0,
      degraded: 0,
      failing: 0,
    };

    this.metrics.connectionsByTier = {};

    for (const connection of this.connections.values()) {
      // Update health counts
      this.metrics.connectionsByHealth[connection.health.status]++;

      // Update tier counts
      const tier = connection.subscriptionTier || 'free';
      this.metrics.connectionsByTier[tier] = (this.metrics.connectionsByTier[tier] || 0) + 1;
    }
  }

  private updateResourceUsage(): void {
    // Simple memory usage estimation
    const connectionsMemory = this.connections.size * 1024; // Rough estimate per connection
    this.metrics.resourceUsage.memory = connectionsMemory;

    // CPU usage would need more sophisticated monitoring
    // For now, estimate based on active connections and message throughput
    this.metrics.resourceUsage.cpu = Math.min(
      100,
      (this.connections.size / this.config.maxConcurrentConnections) * 50 +
        (this.metrics.messagesDelivered / 1000) * 10
    );
  }
}

// Export singleton instance
export const sseConnectionManager = SSEConnectionManager.getInstance();
