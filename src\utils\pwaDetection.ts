export const isPWAInstalled = (): boolean => {
  // Check if running in standalone mode
  if (window.matchMedia && window.matchMedia('(display-mode: standalone)').matches) {
    return true;
  }

  // Check for iOS Safari standalone mode
  if ((window.navigator as any).standalone === true) {
    return true;
  }

  // Check for Android Chrome standalone mode
  if (document.referrer.includes('android-app://')) {
    return true;
  }

  return false;
};

export const getPWADisplayMode = (): string => {
  if (window.matchMedia && window.matchMedia('(display-mode: standalone)').matches) {
    return 'standalone';
  }
  if (window.matchMedia && window.matchMedia('(display-mode: fullscreen)').matches) {
    return 'fullscreen';
  }
  if (window.matchMedia && window.matchMedia('(display-mode: minimal-ui)').matches) {
    return 'minimal-ui';
  }
  return 'browser';
};

export const isServiceWorkerSupported = (): boolean => {
  return 'serviceWorker' in navigator;
};

export const isPushNotificationSupported = (): boolean => {
  return 'PushManager' in window && 'Notification' in window;
};

export const isBackgroundSyncSupported = (): boolean => {
  return 'serviceWorker' in navigator && 'SyncManager' in window;
};
