'use client';

import { memo, useState } from 'react';
import { <PERSON><PERSON>, Position, type NodeProps } from 'reactflow';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CheckSquare, Clock, User, Edit3 } from 'lucide-react';
import { useNodeEdit } from '../contexts/node-edit-context';
import { useMindMapStore } from '@/stores/mindMapStore';

interface TaskNodeData {
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high';
  status: 'todo' | 'in-progress' | 'completed' | 'blocked';
  assignee: string;
  dragging?: boolean;
  isConnecting?: boolean;
  isConnectingFrom?: boolean;
}

export const TaskNode = memo(({ id, data, selected }: NodeProps<TaskNodeData>) => {
  const { startEditing } = useNodeEdit();
  const [isHovered, setIsHovered] = useState(false);

  const priorityColors = {
    low: 'bg-green-100 text-green-800 border-green-200',
    medium: 'bg-yellow-100 text-yellow-800 border-yellow-200',
    high: 'bg-red-100 text-red-800 border-red-200',
  };

  const statusColors = {
    todo: 'bg-gray-100 text-gray-800',
    'in-progress': 'bg-blue-100 text-blue-800',
    completed: 'bg-green-100 text-green-800',
    blocked: 'bg-red-100 text-red-800',
  };

  const handleDoubleClick = () => {
    startEditing(id, { ...data, nodeType: 'task' });
  };

  return (
    <div
      className="relative"
      onDoubleClick={handleDoubleClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <Handle
        type="target"
        position={Position.Top}
        className="w-3 h-3 bg-blue-500 border-2 border-white"
      />

      <Card
        className={`min-w-[250px] max-w-[300px] bg-white/95 backdrop-blur-sm shadow-lg transition-all duration-200 cursor-pointer ${
          selected
            ? 'ring-2 ring-blue-500 shadow-xl scale-105'
            : data.dragging
              ? 'shadow-2xl scale-110 rotate-1'
              : data.isConnectingFrom
                ? 'ring-2 ring-green-500 shadow-xl'
                : data.isConnecting
                  ? 'ring-1 ring-blue-300 hover:ring-2 hover:ring-blue-500'
                  : 'hover:shadow-xl hover:scale-102'
        }`}
      >
        <div className="p-4">
          <div className="flex items-start justify-between mb-3">
            <div className="flex items-center space-x-2">
              <CheckSquare className="h-5 w-5 text-blue-600" />
              <h3 className="font-semibold text-gray-900 text-sm">{data.title}</h3>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="outline" className={`text-xs ${priorityColors[data.priority]}`}>
                {data.priority}
              </Badge>
              {isHovered && (
                <Edit3
                  className="h-3 w-3 text-gray-400 hover:text-blue-500 cursor-pointer transition-colors duration-200"
                  onClick={() => startEditing(id, { ...data, nodeType: 'task' })}
                />
              )}
            </div>
          </div>

          <p className="text-sm text-gray-600 mb-3 line-clamp-2">{data.description}</p>

          <div className="flex items-center justify-between">
            <Badge variant="secondary" className={`text-xs ${statusColors[data.status]}`}>
              <Clock className="h-3 w-3 mr-1" />
              {data.status}
            </Badge>

            {data.assignee && (
              <div className="flex items-center text-xs text-gray-500">
                <User className="h-3 w-3 mr-1" />
                {data.assignee}
              </div>
            )}
          </div>
        </div>
      </Card>

      <Handle
        type="source"
        position={Position.Bottom}
        className="w-3 h-3 bg-blue-500 border-2 border-white"
      />
    </div>
  );
});

TaskNode.displayName = 'TaskNode';
