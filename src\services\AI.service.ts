// AI Service for TaskMantra
// This service provides AI-powered features for task management

export interface AITaskSuggestion {
  title: string;
  description: string;
  priority: 'Low' | 'Medium' | 'High' | 'Urgent';
  estimatedTime: number; // in minutes
  tags: string[];
  dueDate?: string;
  subtasks?: string[];
  dependencies?: string[];
  confidence: number; // 0-1
}

export interface AIProductivityInsight {
  type: 'warning' | 'suggestion' | 'achievement' | 'optimization';
  title: string;
  description: string;
  actionable: boolean;
  action?: string;
  impact: 'low' | 'medium' | 'high';
  category: 'time' | 'focus' | 'workload' | 'efficiency' | 'wellbeing';
}

export interface AIWorkloadAnalysis {
  currentLoad: number; // 0-100
  optimalLoad: number;
  burnoutRisk: number; // 0-100
  recommendations: string[];
  redistributionSuggestions: Array<{
    taskId: string;
    suggestion: string;
    reason: string;
  }>;
}

export class AIService {
  // Task Intelligence
  static async enhanceTaskDescription(title: string): Promise<AITaskSuggestion> {
    // Simulate AI enhancement - replace with actual AI API call
    await this.delay(1000);

    const mockSuggestions: Record<string, Partial<AITaskSuggestion>> = {
      'api integration': {
        description:
          'Implement REST API integration with proper error handling, authentication, and rate limiting. Include comprehensive testing and documentation.',
        priority: 'High',
        estimatedTime: 240,
        tags: ['development', 'backend', 'api'],
        subtasks: [
          'Set up API client configuration',
          'Implement authentication flow',
          'Add error handling and retry logic',
          'Write unit tests',
          'Update documentation',
        ],
      },
      design: {
        description:
          'Create user-centered design with modern UI/UX principles. Include wireframes, prototypes, and design system components.',
        priority: 'Medium',
        estimatedTime: 180,
        tags: ['design', 'ui/ux', 'frontend'],
        subtasks: [
          'Research user requirements',
          'Create wireframes',
          'Design high-fidelity mockups',
          'Build interactive prototype',
          'Conduct usability testing',
        ],
      },
      meeting: {
        description:
          'Structured meeting with clear agenda, objectives, and action items. Prepare materials and follow-up documentation.',
        priority: 'Medium',
        estimatedTime: 60,
        tags: ['meeting', 'collaboration'],
        subtasks: [
          'Prepare meeting agenda',
          'Send calendar invites',
          'Prepare presentation materials',
          'Conduct meeting',
          'Send follow-up notes',
        ],
      },
    };

    const key =
      Object.keys(mockSuggestions).find(k => title.toLowerCase().includes(k)) || 'default';

    const baseSuggestion = mockSuggestions[key] || {};

    return {
      title,
      description: baseSuggestion.description || `Enhanced description for: ${title}`,
      priority: baseSuggestion.priority || 'Medium',
      estimatedTime: baseSuggestion.estimatedTime || 120,
      tags: baseSuggestion.tags || ['general'],
      subtasks: baseSuggestion.subtasks || [],
      confidence: 0.85,
      dueDate: this.suggestDueDate(baseSuggestion.priority || 'Medium'),
    };
  }

  static async analyzeProductivity(): Promise<AIProductivityInsight[]> {
    await this.delay(800);

    // Mock productivity insights
    return [
      {
        type: 'warning',
        title: 'High Workload Detected',
        description:
          'You have 15 high-priority tasks due this week. Consider redistributing or extending deadlines.',
        actionable: true,
        action: 'Review and prioritize tasks',
        impact: 'high',
        category: 'workload',
      },
      {
        type: 'suggestion',
        title: 'Optimal Focus Time',
        description:
          'Your productivity peaks between 9-11 AM. Schedule important tasks during this window.',
        actionable: true,
        action: 'Reschedule tasks',
        impact: 'medium',
        category: 'time',
      },
      {
        type: 'achievement',
        title: 'Consistency Streak',
        description: "You've completed tasks for 7 consecutive days! Keep up the momentum.",
        actionable: false,
        impact: 'low',
        category: 'efficiency',
      },
      {
        type: 'optimization',
        title: 'Break Recommendation',
        description:
          "You've been working for 3 hours straight. A 15-minute break could improve focus.",
        actionable: true,
        action: 'Take a break',
        impact: 'medium',
        category: 'wellbeing',
      },
    ];
  }

  static async analyzeWorkload(): Promise<AIWorkloadAnalysis> {
    await this.delay(600);

    return {
      currentLoad: 85,
      optimalLoad: 70,
      burnoutRisk: 65,
      recommendations: [
        'Delegate 2-3 low-priority tasks to team members',
        'Extend deadlines for non-critical items',
        'Block focus time for deep work sessions',
        'Schedule regular breaks between tasks',
      ],
      redistributionSuggestions: [
        {
          taskId: '1',
          suggestion: 'Move to next week',
          reason: 'Non-critical and can be delayed without impact',
        },
        {
          taskId: '2',
          suggestion: 'Delegate to Alice',
          reason: 'Matches her expertise and current capacity',
        },
      ],
    };
  }

  static async suggestTaskPriority(
    title: string,
    description: string,
    dueDate?: string
  ): Promise<{
    priority: 'Low' | 'Medium' | 'High' | 'Urgent';
    reasoning: string;
    confidence: number;
  }> {
    await this.delay(500);

    // Simple AI logic for priority suggestion
    const urgentKeywords = ['urgent', 'asap', 'critical', 'emergency', 'hotfix'];
    const highKeywords = ['important', 'deadline', 'client', 'release', 'bug'];
    const lowKeywords = ['research', 'explore', 'consider', 'maybe', 'someday'];

    const text = `${title} ${description}`.toLowerCase();

    if (urgentKeywords.some(keyword => text.includes(keyword))) {
      return {
        priority: 'Urgent',
        reasoning: 'Contains urgent keywords and requires immediate attention',
        confidence: 0.9,
      };
    }

    if (highKeywords.some(keyword => text.includes(keyword))) {
      return {
        priority: 'High',
        reasoning: 'Important task that should be prioritized',
        confidence: 0.8,
      };
    }

    if (lowKeywords.some(keyword => text.includes(keyword))) {
      return {
        priority: 'Low',
        reasoning: 'Exploratory or non-critical task',
        confidence: 0.7,
      };
    }

    // Check due date proximity
    if (dueDate) {
      const daysUntilDue = Math.ceil(
        (new Date(dueDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)
      );

      if (daysUntilDue <= 1) {
        return {
          priority: 'Urgent',
          reasoning: 'Due within 24 hours',
          confidence: 0.95,
        };
      }

      if (daysUntilDue <= 3) {
        return {
          priority: 'High',
          reasoning: 'Due within 3 days',
          confidence: 0.85,
        };
      }
    }

    return {
      priority: 'Medium',
      reasoning: 'Standard priority based on content analysis',
      confidence: 0.6,
    };
  }

  static async estimateTaskTime(
    title: string,
    description: string,
    complexity?: 'simple' | 'medium' | 'complex'
  ): Promise<{
    estimatedMinutes: number;
    range: { min: number; max: number };
    reasoning: string;
    confidence: number;
  }> {
    await this.delay(400);

    // Base estimates by task type
    const taskTypeEstimates: Record<string, number> = {
      meeting: 60,
      call: 30,
      email: 15,
      review: 45,
      design: 180,
      development: 240,
      testing: 120,
      documentation: 90,
      research: 120,
      planning: 60,
    };

    const text = `${title} ${description}`.toLowerCase();
    let baseEstimate = 60; // Default 1 hour

    // Find matching task type
    for (const [type, estimate] of Object.entries(taskTypeEstimates)) {
      if (text.includes(type)) {
        baseEstimate = estimate;
        break;
      }
    }

    // Adjust for complexity
    const complexityMultiplier = {
      simple: 0.7,
      medium: 1.0,
      complex: 1.5,
    }[complexity || 'medium'];

    const estimatedMinutes = Math.round(baseEstimate * complexityMultiplier);
    const variance = estimatedMinutes * 0.3; // 30% variance

    return {
      estimatedMinutes,
      range: {
        min: Math.round(estimatedMinutes - variance),
        max: Math.round(estimatedMinutes + variance),
      },
      reasoning: `Based on task type analysis and ${complexity || 'medium'} complexity`,
      confidence: 0.75,
    };
  }

  static async generateSubtasks(title: string, description: string): Promise<string[]> {
    await this.delay(700);

    // Simple subtask generation based on task type
    const text = `${title} ${description}`.toLowerCase();

    if (text.includes('development') || text.includes('code') || text.includes('implement')) {
      return [
        'Set up development environment',
        'Create initial implementation',
        'Write unit tests',
        'Code review and refactoring',
        'Integration testing',
        'Documentation update',
      ];
    }

    if (text.includes('design') || text.includes('ui') || text.includes('mockup')) {
      return [
        'Research and gather requirements',
        'Create wireframes',
        'Design high-fidelity mockups',
        'Create interactive prototype',
        'Gather feedback and iterate',
        'Finalize design assets',
      ];
    }

    if (text.includes('meeting') || text.includes('call')) {
      return [
        'Prepare agenda',
        'Send calendar invites',
        'Prepare materials/presentation',
        'Conduct meeting',
        'Send follow-up notes',
      ];
    }

    // Generic subtasks
    return ['Plan and research', 'Execute main work', 'Review and refine', 'Finalize and deliver'];
  }

  // Utility methods
  private static delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private static suggestDueDate(priority: string): string {
    const now = new Date();
    const daysToAdd =
      {
        Urgent: 1,
        High: 3,
        Medium: 7,
        Low: 14,
      }[priority] || 7;

    const dueDate = new Date(now.getTime() + daysToAdd * 24 * 60 * 60 * 1000);
    return dueDate.toISOString().split('T')[0];
  }

  // Voice-to-text simulation
  static async transcribeAudio(): Promise<string> {
    await this.delay(2000);

    // Simulate transcription
    const mockTranscriptions = [
      'Create a new task for implementing user authentication with OAuth',
      'Schedule a meeting with the design team for next Tuesday',
      'Review the pull request for the dashboard updates',
      'Research best practices for API rate limiting',
      'Update the documentation for the new features',
    ];

    return mockTranscriptions[Math.floor(Math.random() * mockTranscriptions.length)];
  }

  // Smart scheduling
  static async suggestOptimalSchedule(tasks: any[]): Promise<any[]> {
    await this.delay(1000);

    // Mock smart scheduling logic
    return tasks.map((task, index) => ({
      ...task,
      suggestedStartTime: new Date(Date.now() + index * 2 * 60 * 60 * 1000).toISOString(),
      reasoning: 'Optimized based on your productivity patterns and task dependencies',
    }));
  }
}
