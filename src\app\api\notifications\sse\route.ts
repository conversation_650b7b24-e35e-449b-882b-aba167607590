import { getServerSession } from 'next-auth';
import { authOptions } from '../../auth/[...nextauth]/options';
import { connectDB } from '@/Utility/db';
import { Notification } from '@/models/Notification';
import { promisify } from 'util';
import { gzip } from 'zlib';

const gzipAsync = promisify(gzip);
const clients = new Map<string, ReadableStreamDefaultController<Uint8Array>>();
const heartbeatIntervals = new Map<string, ReturnType<typeof setInterval>>();
const connectionActivity = new Map<string, number>();
const connectionHealth = new Map<
  string,
  { status: 'healthy' | 'degraded' | 'failing'; score: number }
>();
const connectionPriorities = new Map<string, number>();

const MAX_CONCURRENT_CONNECTIONS = 100;
const IDLE_TIMEOUT = 10 * 60 * 1000;
const HEARTBEAT_INTERVAL_BASE = 30000;
const HEARTBEAT_INTERVAL_MIN = 15000;
const HEARTBEAT_INTERVAL_MAX = 120000;
const MAX_CONNECTION_DURATION = 30 * 60 * 1000;
const BATCH_THRESHOLD_SIZE = 20;
const COMPRESSION_THRESHOLD = 2048;
const CLEANUP_INTERVAL = 5 * 60 * 1000;

async function compressData(data: string): Promise<Buffer> {
  try {
    return await gzipAsync(Buffer.from(data, 'utf8'));
  } catch {
    return Buffer.from(data, 'utf8');
  }
}

function updateConnectionHealth(userId: string, success: boolean): number {
  const current = connectionHealth.get(userId) || { status: 'healthy', score: 10 };
  const newScore = Math.max(0, Math.min(10, current.score + (success ? 0.3 : -1.5)));
  const status: 'healthy' | 'degraded' | 'failing' =
    newScore <= 3 ? 'failing' : newScore <= 6 ? 'degraded' : 'healthy';

  connectionHealth.set(userId, { status, score: newScore });

  return status === 'failing'
    ? HEARTBEAT_INTERVAL_MIN
    : status === 'degraded'
      ? HEARTBEAT_INTERVAL_BASE
      : Math.min(HEARTBEAT_INTERVAL_MAX, HEARTBEAT_INTERVAL_BASE + (newScore - 6) * 10000);
}

function shouldAllowConnection(_userId: string, priority: number): boolean {
  if (clients.size < MAX_CONCURRENT_CONNECTIONS) return true;

  const lowestPriorityUser = [...connectionPriorities.entries()].sort((a, b) => a[1] - b[1])[0];

  if (lowestPriorityUser && priority > lowestPriorityUser[1]) {
    disconnectUser(lowestPriorityUser[0], 'priority_displacement');
    return true;
  }
  return false;
}

function disconnectUser(userId: string, reason: string) {
  const controller = clients.get(userId);
  if (controller) {
    try {
      controller.enqueue(
        new TextEncoder().encode(
          `id: disconnect-${Date.now()}\ndata: ${JSON.stringify({
            type: 'disconnect',
            reason,
            reconnectDelay: 10000,
          })}\n\n`
        )
      );
      controller.close();
    } catch {
      // Controller already closed or in invalid state
    }
  }
  cleanupUserConnection(userId);
}

function cleanupUserConnection(userId: string) {
  clients.delete(userId);
  connectionActivity.delete(userId);
  connectionHealth.delete(userId);
  connectionPriorities.delete(userId);

  const interval = heartbeatIntervals.get(userId);
  if (interval) {
    clearInterval(interval);
    heartbeatIntervals.delete(userId);
  }
}

setInterval(() => {
  const now = Date.now();
  const usersToDisconnect: string[] = [];

  for (const [userId, lastActivity] of connectionActivity.entries()) {
    if (now - lastActivity > IDLE_TIMEOUT) {
      usersToDisconnect.push(userId);
    }
  }

  usersToDisconnect.forEach(userId => {
    disconnectUser(userId, 'idle_timeout');
  });
}, CLEANUP_INTERVAL);

export const dynamic = 'force-dynamic';

export async function GET(): Promise<Response> {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return new Response('Unauthorized', { status: 401 });
    }

    const userId = session.user.id.toString();
    const userPriority = 5;

    if (!shouldAllowConnection(userId, userPriority)) {
      return new Response(JSON.stringify({ error: 'Too many connections', retryAfter: 60 }), {
        status: 503,
        headers: {
          'Content-Type': 'application/json',
          'Retry-After': '60',
          'Cache-Control': 'no-store',
        },
      });
    }

    if (heartbeatIntervals.has(userId)) {
      clearInterval(heartbeatIntervals.get(userId)!);
      heartbeatIntervals.delete(userId);
    }

    if (clients.has(userId)) {
      try {
        clients.get(userId)?.close();
      } catch {
        // Previous client already closed
      }
      clients.delete(userId);
    }

    connectionActivity.delete(userId);
    connectionPriorities.delete(userId);

    let currentHeartbeatInterval = updateConnectionHealth(userId, true);
    let heartbeatTimer: ReturnType<typeof setInterval> | null = null;
    const connectionStart = Date.now();

    const stream = new ReadableStream<Uint8Array>({
      start(controller) {
        clients.set(userId, controller);
        connectionActivity.set(userId, connectionStart);
        connectionPriorities.set(userId, userPriority);
        connectionHealth.set(userId, { status: 'healthy', score: 10 });

        controller.enqueue(
          new TextEncoder().encode(
            `id: ${connectionStart}\ndata: ${JSON.stringify({
              type: 'connection',
              message: 'Connected',
              config: {
                heartbeatInterval: currentHeartbeatInterval,
                connectionTTL: MAX_CONNECTION_DURATION,
                compressionEnabled: true,
              },
            })}\n\n`
          )
        );

        sendUnreadNotifications(userId, controller).catch(() => {
          updateConnectionHealth(userId, false);
        });

        const sendHeartbeat = () => {
          try {
            const now = Date.now();
            connectionActivity.set(userId, now);

            if (now - connectionStart > MAX_CONNECTION_DURATION) {
              disconnectUser(userId, 'max_duration_exceeded');
              return;
            }

            const healthStatus = connectionHealth.get(userId);
            if (healthStatus?.status === 'failing') {
              disconnectUser(userId, 'health_check_failed');
              return;
            }

            controller.enqueue(
              new TextEncoder().encode(
                `id: ${now}\ndata: ${JSON.stringify({
                  type: 'heartbeat',
                  timestamp: new Date().toISOString(),
                  status: healthStatus?.status || 'healthy',
                })}\n\n`
              )
            );

            const nextInterval = updateConnectionHealth(userId, true);
            if (nextInterval !== currentHeartbeatInterval) {
              if (heartbeatTimer) clearInterval(heartbeatTimer);
              currentHeartbeatInterval = nextInterval;
              heartbeatTimer = setInterval(sendHeartbeat, currentHeartbeatInterval);
              heartbeatIntervals.set(userId, heartbeatTimer);
            }
          } catch {
            updateConnectionHealth(userId, false);
            cleanupUserConnection(userId);
          }
        };

        heartbeatTimer = setInterval(sendHeartbeat, currentHeartbeatInterval);
        heartbeatIntervals.set(userId, heartbeatTimer);
      },
      cancel() {
        cleanupUserConnection(userId);
      },
    });

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-store, no-transform',
        Connection: 'keep-alive',
        'X-Accel-Buffering': 'no',
        'X-Connection-Priority': userPriority.toString(),
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control',
      },
    });
  } catch {
    return new Response('Internal Server Error', { status: 500 });
  }
}

async function sendUnreadNotifications(
  userId: string,
  controller: ReadableStreamDefaultController<Uint8Array>
): Promise<void> {
  try {
    if (!clients.has(userId) || clients.get(userId) !== controller) return;

    await connectDB();
    const notifications: Record<string, any>[] = await Notification.find({
      userId,
      read: false,
    })
      .sort({ createdAt: -1 })
      .limit(BATCH_THRESHOLD_SIZE)
      .lean();

    if (notifications.length === 0 || !clients.has(userId) || clients.get(userId) !== controller) {
      return;
    }

    const eventId = `batch-${Date.now()}`;
    const notificationsJson = JSON.stringify({
      type: 'notifications',
      notifications,
      batchSize: notifications.length,
      hasMore: notifications.length === BATCH_THRESHOLD_SIZE,
    });

    let payload: string;
    if (notificationsJson.length > COMPRESSION_THRESHOLD) {
      const compressed = await compressData(notificationsJson);
      payload = `id: ${eventId}\ndata: ${compressed.toString('base64')}\ncontent-encoding: gzip\n\n`;
    } else {
      payload = `id: ${eventId}\ndata: ${notificationsJson}\n\n`;
    }

    if (!clients.has(userId) || clients.get(userId) !== controller) return;

    controller.enqueue(new TextEncoder().encode(payload));
    connectionActivity.set(userId, Date.now());
    updateConnectionHealth(userId, true);
  } catch {
    if (clients.get(userId) === controller) {
      updateConnectionHealth(userId, false);
    }
  }
}

export async function sendNotificationToUser(
  userId: string,
  notification: Record<string, any>
): Promise<boolean> {
  const userIdStr = userId?.toString ? userId.toString() : userId;

  if (!clients.has(userIdStr)) return false;

  const controller = clients.get(userIdStr);
  if (!controller) return false;

  connectionActivity.set(userIdStr, Date.now());

  try {
    const eventId = notification._id ? notification._id.toString() : `notif-${Date.now()}`;
    const data = `id: ${eventId}\ndata: ${JSON.stringify({
      type: 'notification',
      notification,
    })}\n\n`;

    if (!clients.has(userIdStr) || clients.get(userIdStr) !== controller) return false;

    controller.enqueue(new TextEncoder().encode(data));
    updateConnectionHealth(userIdStr, true);
    return true;
  } catch {
    if (clients.get(userIdStr) === controller) {
      updateConnectionHealth(userIdStr, false);
      cleanupUserConnection(userIdStr);
    }
    return false;
  }
}

process.on('SIGTERM', () => {
  for (const [userId] of clients.entries()) {
    disconnectUser(userId, 'server_shutdown');
  }
});

process.on('SIGINT', () => {
  for (const [userId] of clients.entries()) {
    disconnectUser(userId, 'server_shutdown');
  }
});
