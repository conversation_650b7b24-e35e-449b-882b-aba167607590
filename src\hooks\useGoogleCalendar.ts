import { useState, useEffect, useCallback, useRef } from 'react';
import { useSession } from 'next-auth/react';
import { toast } from 'sonner';

export interface GoogleCalendarEvent {
  id: string;
  title: string;
  description?: string;
  start: {
    dateTime?: string;
    date?: string;
    timeZone?: string;
  };
  end: {
    dateTime?: string;
    date?: string;
    timeZone?: string;
  };
  location?: string;
  attendees?: Array<{
    email: string;
    displayName?: string;
    responseStatus?: 'needsAction' | 'declined' | 'tentative' | 'accepted';
  }>;
  recurrence?: string[];
  reminders?: {
    useDefault: boolean;
    overrides?: Array<{
      method: 'email' | 'popup';
      minutes: number;
    }>;
  };
  visibility?: 'default' | 'public' | 'private' | 'confidential';
  colorId?: string;
  taskId?: string;
  projectId?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface GoogleCalendar {
  id: string;
  summary: string;
  description?: string;
  location?: string;
  timeZone: string;
  colorId?: string;
  backgroundColor?: string;
  foregroundColor?: string;
  accessRole: 'owner' | 'reader' | 'writer' | 'freeBusyReader';
  defaultReminders?: Array<{
    method: 'email' | 'popup';
    minutes: number;
  }>;
  primary?: boolean;
  selected?: boolean;
}

export interface CalendarSyncSettings {
  enabled: boolean;
  calendarId: string;
  syncDirection: 'import' | 'export' | 'bidirectional';
  syncTasks: boolean;
  syncProjects: boolean;
  defaultReminders: boolean;
  reminderMinutes: number[];
  conflictResolution: 'google_wins' | 'taskmantra_wins' | 'manual';
  lastSyncAt?: string;
  autoSync: boolean;
  syncInterval: number; // minutes
}

export interface SyncConflict {
  id: string;
  type: 'event_modified' | 'event_deleted' | 'task_modified' | 'task_deleted';
  resourceType: 'event' | 'task';
  resourceId: string;
  googleData: any;
  taskmantraData: any;
  conflictFields: string[];
  createdAt: string;
  resolvedAt?: string;
  resolution?: 'google_wins' | 'taskmantra_wins' | 'merged';
}

export interface AuthStatus {
  isAuthenticated: boolean;
  isConnecting: boolean;
  error?: string;
  scopes?: string[];
  email?: string;
  expiresAt?: string;
}

const REQUIRED_SCOPES = [
  'https://www.googleapis.com/auth/calendar.events',
  'https://www.googleapis.com/auth/calendar.readonly',
];

const SYNC_INTERVALS = {
  MANUAL: 0,
  EVERY_5_MIN: 5,
  EVERY_15_MIN: 15,
  EVERY_30_MIN: 30,
  EVERY_HOUR: 60,
  EVERY_4_HOURS: 240,
  DAILY: 1440,
};

export const useGoogleCalendar = () => {
  const { data: session } = useSession();

  // State
  const [authStatus, setAuthStatus] = useState<AuthStatus>({
    isAuthenticated: false,
    isConnecting: false,
  });
  const [calendars, setCalendars] = useState<GoogleCalendar[]>([]);
  const [events, setEvents] = useState<GoogleCalendarEvent[]>([]);
  const [syncSettings, setSyncSettings] = useState<CalendarSyncSettings>({
    enabled: false,
    calendarId: 'primary',
    syncDirection: 'bidirectional',
    syncTasks: true,
    syncProjects: false,
    defaultReminders: true,
    reminderMinutes: [15, 60],
    conflictResolution: 'manual',
    autoSync: true,
    syncInterval: SYNC_INTERVALS.EVERY_15_MIN,
  });
  const [syncConflicts, setSyncConflicts] = useState<SyncConflict[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isSyncing, setIsSyncing] = useState(false);
  const [lastSyncAt, setLastSyncAt] = useState<Date | null>(null);
  const [syncError, setSyncError] = useState<string | null>(null);

  // Refs
  const syncTimer = useRef<ReturnType<typeof setInterval> | null>(null);
  const abortController = useRef<AbortController | null>(null);

  // Initialize
  useEffect(() => {
    if (session?.user?.id) {
      loadAuthStatus();
      loadSyncSettings();
    }

    return () => {
      if (syncTimer.current) {
        clearInterval(syncTimer.current);
      }
      if (abortController.current) {
        abortController.current.abort();
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [session?.user?.id]);

  // Setup auto-sync
  useEffect(() => {
    if (syncSettings.enabled && syncSettings.autoSync && syncSettings.syncInterval > 0) {
      setupAutoSync();
    } else {
      clearAutoSync();
    }

    return clearAutoSync;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [syncSettings.enabled, syncSettings.autoSync, syncSettings.syncInterval]);

  // Load authentication status
  const loadAuthStatus = useCallback(async () => {
    if (!session?.user?.id) return;

    try {
      const response = await fetch('/api/google-calendar/auth/status');
      const result = await response.json();

      if (result.success) {
        setAuthStatus(result.data);

        if (result.data.isAuthenticated) {
          loadCalendars();
        }
      }
    } catch (error: any) {
      toast.error('Error loading auth status:', error);
      setAuthStatus(prev => ({ ...prev, error: 'Failed to load authentication status' }));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [session?.user?.id]);

  // Load sync settings
  const loadSyncSettings = useCallback(async () => {
    if (!session?.user?.id) return;

    try {
      const response = await fetch('/api/google-calendar/sync/settings');
      const result = await response.json();

      if (result.success && result.data) {
        setSyncSettings(result.data);
        if (result.data.lastSyncAt) {
          setLastSyncAt(new Date(result.data.lastSyncAt));
        }
      }
    } catch (error: any) {
      toast.error('Error loading sync settings:', error);
    }
  }, [session?.user?.id]);

  // Authenticate with Google Calendar
  const authenticate = useCallback(async (): Promise<boolean> => {
    if (!session?.user?.id) {
      toast.error('Please sign in to connect Google Calendar');
      return false;
    }

    setAuthStatus(prev => ({ ...prev, isConnecting: true, error: undefined }));

    try {
      // Get authorization URL
      const response = await fetch('/api/google-calendar/auth/url', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ scopes: REQUIRED_SCOPES }),
      });

      const result = await response.json();

      if (result.success && result.data?.authUrl) {
        // Open authorization window
        const authWindow = window.open(
          result.data.authUrl,
          'google-calendar-auth',
          'width=500,height=600,scrollbars=yes,resizable=yes'
        );

        // Listen for authorization completion
        return new Promise(resolve => {
          const checkClosed = setInterval(() => {
            if (authWindow?.closed) {
              clearInterval(checkClosed);

              // Check if authentication was successful
              setTimeout(async () => {
                await loadAuthStatus();
                const isSuccess = authStatus.isAuthenticated;

                if (isSuccess) {
                  toast.success('Google Calendar connected successfully');
                  loadCalendars();
                } else {
                  toast.error('Failed to connect Google Calendar');
                }

                setAuthStatus(prev => ({ ...prev, isConnecting: false }));
                resolve(isSuccess);
              }, 1000);
            }
          }, 1000);

          // Timeout after 5 minutes
          setTimeout(() => {
            clearInterval(checkClosed);
            if (authWindow && !authWindow.closed) {
              authWindow.close();
            }
            setAuthStatus(prev => ({ ...prev, isConnecting: false }));
            toast.error('Authentication timeout');
            resolve(false);
          }, 300000);
        });
      } else {
        throw new Error(result.message || 'Failed to get authorization URL');
      }
    } catch (error: any) {
      setAuthStatus(prev => ({
        ...prev,
        isConnecting: false,
        error: error.message || 'Authentication failed',
      }));
      toast.error('Failed to connect Google Calendar');
      return false;
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [session?.user?.id, authStatus.isAuthenticated]);

  // Disconnect Google Calendar
  const disconnect = useCallback(async (): Promise<boolean> => {
    if (!session?.user?.id) return false;

    try {
      const response = await fetch('/api/google-calendar/auth/disconnect', {
        method: 'POST',
      });

      const result = await response.json();

      if (result.success) {
        setAuthStatus({
          isAuthenticated: false,
          isConnecting: false,
        });
        setCalendars([]);
        setEvents([]);
        setSyncSettings(prev => ({ ...prev, enabled: false }));
        clearAutoSync();

        toast.success('Google Calendar disconnected');
        return true;
      } else {
        throw new Error(result.message || 'Failed to disconnect');
      }
    } catch (error: any) {
      toast.error('Error disconnecting Google Calendar:', error);
      toast.error('Failed to disconnect Google Calendar');
      return false;
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [session?.user?.id]);

  // Load user's calendars
  const loadCalendars = useCallback(async () => {
    if (!authStatus.isAuthenticated) return;

    setIsLoading(true);

    try {
      const response = await fetch('/api/google-calendar/calendars');
      const result = await response.json();

      if (result.success && result.data) {
        setCalendars(result.data);
      }
    } catch (error: any) {
      toast.error('Error loading calendars:', error);
      toast.error('Failed to load calendars');
    } finally {
      setIsLoading(false);
    }
  }, [authStatus.isAuthenticated]);

  // Load events from a calendar
  const loadEvents = useCallback(
    async (calendarId: string = 'primary', timeMin?: string, timeMax?: string) => {
      if (!authStatus.isAuthenticated) return;

      setIsLoading(true);

      try {
        const params = new URLSearchParams({
          calendarId,
          ...(timeMin && { timeMin }),
          ...(timeMax && { timeMax }),
        });

        const response = await fetch(`/api/google-calendar/events?${params}`);
        const result = await response.json();

        if (result.success && result.data) {
          setEvents(result.data);
        }
      } catch (error: any) {
        toast.error('Error loading events:', error);
        toast.error('Failed to load events');
      } finally {
        setIsLoading(false);
      }
    },
    [authStatus.isAuthenticated]
  );

  // Create event
  const createEvent = useCallback(
    async (
      eventData: Omit<GoogleCalendarEvent, 'id' | 'createdAt' | 'updatedAt'>,
      calendarId: string = 'primary'
    ): Promise<GoogleCalendarEvent | null> => {
      if (!authStatus.isAuthenticated) {
        toast.error('Google Calendar not connected');
        return null;
      }

      try {
        const response = await fetch('/api/google-calendar/events', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ ...eventData, calendarId }),
        });

        const result = await response.json();

        if (result.success && result.data) {
          toast.success('Event created successfully');
          // Refresh events
          loadEvents(calendarId);
          return result.data;
        } else {
          throw new Error(result.message || 'Failed to create event');
        }
      } catch (error: any) {
        toast.error('Error creating event:', error);
        toast.error('Failed to create event');
        return null;
      }
    },
    [authStatus.isAuthenticated, loadEvents]
  );

  // Update event
  const updateEvent = useCallback(
    async (
      eventId: string,
      updates: Partial<GoogleCalendarEvent>,
      calendarId: string = 'primary'
    ): Promise<boolean> => {
      if (!authStatus.isAuthenticated) {
        toast.error('Google Calendar not connected');
        return false;
      }

      try {
        const response = await fetch(`/api/google-calendar/events/${eventId}`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ ...updates, calendarId }),
        });

        const result = await response.json();

        if (result.success) {
          toast.success('Event updated successfully');
          // Refresh events
          loadEvents(calendarId);
          return true;
        } else {
          throw new Error(result.message || 'Failed to update event');
        }
      } catch (error: any) {
        toast.error('Error updating event:', error);
        toast.error('Failed to update event');
        return false;
      }
    },
    [authStatus.isAuthenticated, loadEvents]
  );

  // Delete event
  const deleteEvent = useCallback(
    async (eventId: string, calendarId: string = 'primary'): Promise<boolean> => {
      if (!authStatus.isAuthenticated) {
        toast.error('Google Calendar not connected');
        return false;
      }

      try {
        const response = await fetch(`/api/google-calendar/events/${eventId}`, {
          method: 'DELETE',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ calendarId }),
        });

        const result = await response.json();

        if (result.success) {
          toast.success('Event deleted successfully');
          // Refresh events
          loadEvents(calendarId);
          return true;
        } else {
          throw new Error(result.message || 'Failed to delete event');
        }
      } catch (error: any) {
        toast.error('Error deleting event:', error);
        toast.error('Failed to delete event');
        return false;
      }
    },
    [authStatus.isAuthenticated, loadEvents]
  );

  // Update sync settings
  const updateSyncSettings = useCallback(
    async (newSettings: Partial<CalendarSyncSettings>): Promise<boolean> => {
      if (!session?.user?.id) return false;

      try {
        const updatedSettings = { ...syncSettings, ...newSettings };

        const response = await fetch('/api/google-calendar/sync/settings', {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(updatedSettings),
        });

        const result = await response.json();

        if (result.success) {
          setSyncSettings(updatedSettings);
          toast.success('Sync settings updated');
          return true;
        } else {
          throw new Error(result.message || 'Failed to update settings');
        }
      } catch (error: any) {
        toast.error('Error updating sync settings:', error);
        toast.error('Failed to update sync settings');
        return false;
      }
    },
    [session?.user?.id, syncSettings]
  );

  // Perform sync
  const performSync = useCallback(
    async (force: boolean = false): Promise<boolean> => {
      if (!authStatus.isAuthenticated || !syncSettings.enabled) {
        toast.error('Calendar sync not configured');
        return false;
      }

      if (isSyncing && !force) {
        toast.info('Sync already in progress');
        return false;
      }

      setIsSyncing(true);
      setSyncError(null);

      // Setup abort controller for this sync
      abortController.current = new AbortController();

      try {
        const response = await fetch('/api/google-calendar/sync', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            calendarId: syncSettings.calendarId,
            syncDirection: syncSettings.syncDirection,
            syncTasks: syncSettings.syncTasks,
            syncProjects: syncSettings.syncProjects,
            conflictResolution: syncSettings.conflictResolution,
            force,
          }),
          signal: abortController.current.signal,
        });

        const result = await response.json();

        if (result.success) {
          setLastSyncAt(new Date());

          if (result.data?.conflicts && result.data.conflicts.length > 0) {
            setSyncConflicts(result.data.conflicts);
            toast.warning(`Sync completed with ${result.data.conflicts.length} conflicts`);
          } else {
            toast.success('Calendar sync completed');
          }

          // Refresh events after sync
          loadEvents(syncSettings.calendarId);

          return true;
        } else {
          throw new Error(result.message || 'Sync failed');
        }
      } catch (error: any) {
        if (error.name === 'AbortError') {
          toast.info('Sync cancelled');
          return false;
        }

        setSyncError(error.message || 'Sync failed');
        toast.error('Calendar sync failed');
        return false;
      } finally {
        setIsSyncing(false);
        abortController.current = null;
      }
    },
    [authStatus.isAuthenticated, syncSettings, isSyncing, loadEvents]
  );

  // Cancel ongoing sync
  const cancelSync = useCallback(() => {
    if (abortController.current) {
      abortController.current.abort();
    }
  }, []);

  // Resolve sync conflict
  const resolveConflict = useCallback(
    async (
      conflictId: string,
      resolution: 'google_wins' | 'taskmantra_wins' | 'merged',
      mergedData?: any
    ): Promise<boolean> => {
      try {
        const response = await fetch(`/api/google-calendar/sync/conflicts/${conflictId}`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ resolution, mergedData }),
        });

        const result = await response.json();

        if (result.success) {
          setSyncConflicts(prev => prev.filter(c => c.id !== conflictId));
          toast.success('Conflict resolved');
          return true;
        } else {
          throw new Error(result.message || 'Failed to resolve conflict');
        }
      } catch (error: any) {
        toast.error('Error resolving conflict:', error);
        toast.error('Failed to resolve conflict');
        return false;
      }
    },
    []
  );

  // Setup auto-sync timer
  const setupAutoSync = useCallback(() => {
    clearAutoSync();

    if (syncSettings.syncInterval > 0) {
      syncTimer.current = setInterval(
        () => {
          if (authStatus.isAuthenticated && syncSettings.enabled && !isSyncing) {
            performSync();
          }
        },
        syncSettings.syncInterval * 60 * 1000
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    syncSettings.syncInterval,
    authStatus.isAuthenticated,
    syncSettings.enabled,
    isSyncing,
    performSync,
  ]);

  // Clear auto-sync timer
  const clearAutoSync = useCallback(() => {
    if (syncTimer.current) {
      clearInterval(syncTimer.current);
      syncTimer.current = null;
    }
  }, []);

  // Sync specific task with calendar
  const syncTaskToEvent = useCallback(
    async (
      taskId: string,
      taskData: {
        title: string;
        description?: string;
        dueDate?: string;
        priority?: string;
        projectName?: string;
      }
    ): Promise<GoogleCalendarEvent | null> => {
      if (!authStatus.isAuthenticated || !syncSettings.enabled || !syncSettings.syncTasks) {
        return null;
      }

      try {
        const eventData: Omit<GoogleCalendarEvent, 'id' | 'createdAt' | 'updatedAt'> = {
          title: `[Task] ${taskData.title}`,
          description: `${taskData.description || ''}\n\nProject: ${taskData.projectName || 'Unknown'}\nPriority: ${taskData.priority || 'Normal'}\n\nManaged by TaskMantra`,
          start: {
            dateTime: taskData.dueDate || new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
          },
          end: {
            dateTime: taskData.dueDate
              ? new Date(new Date(taskData.dueDate).getTime() + 60 * 60 * 1000).toISOString()
              : new Date(Date.now() + 25 * 60 * 60 * 1000).toISOString(),
          },
          taskId,
          colorId: taskData.priority === 'High' ? '11' : taskData.priority === 'Medium' ? '5' : '7',
          reminders: syncSettings.defaultReminders
            ? {
                useDefault: false,
                overrides: syncSettings.reminderMinutes.map(minutes => ({
                  method: 'popup' as const,
                  minutes,
                })),
              }
            : undefined,
        };

        return await createEvent(eventData, syncSettings.calendarId);
      } catch (error: any) {
        toast.error('Error syncing task to calendar:', error);
        return null;
      }
    },
    [authStatus.isAuthenticated, syncSettings, createEvent]
  );

  // Get events for specific date range
  const getEventsInRange = useCallback(
    async (startDate: Date, endDate: Date, calendarId?: string): Promise<GoogleCalendarEvent[]> => {
      if (!authStatus.isAuthenticated) return [];

      try {
        const params = new URLSearchParams({
          calendarId: calendarId || syncSettings.calendarId,
          timeMin: startDate.toISOString(),
          timeMax: endDate.toISOString(),
        });

        const response = await fetch(`/api/google-calendar/events?${params}`);
        const result = await response.json();

        if (result.success && result.data) {
          return result.data;
        }

        return [];
      } catch (error: any) {
        toast.error('Error loading events in range:', error);
        return [];
      }
    },
    [authStatus.isAuthenticated, syncSettings.calendarId]
  );

  // Check for scheduling conflicts
  const checkConflicts = useCallback(
    async (
      startTime: string,
      endTime: string,
      excludeEventId?: string
    ): Promise<GoogleCalendarEvent[]> => {
      if (!authStatus.isAuthenticated) return [];

      const startDate = new Date(startTime);
      const endDate = new Date(endTime);

      // Add buffer around the time to check for conflicts
      const bufferStart = new Date(startDate.getTime() - 30 * 60 * 1000); // 30 min before
      const bufferEnd = new Date(endDate.getTime() + 30 * 60 * 1000); // 30 min after

      const events = await getEventsInRange(bufferStart, bufferEnd);

      return events.filter(event => {
        if (excludeEventId && event.id === excludeEventId) return false;

        const eventStart = new Date(event.start.dateTime || event.start.date!);
        const eventEnd = new Date(event.end.dateTime || event.end.date!);

        // Check for overlap
        return startDate < eventEnd && endDate > eventStart;
      });
    },
    [authStatus.isAuthenticated, getEventsInRange]
  );

  // Get calendar color options
  const getCalendarColors = useCallback(async (): Promise<Record<
    string,
    { background: string; foreground: string }
  > | null> => {
    if (!authStatus.isAuthenticated) return null;

    try {
      const response = await fetch('/api/google-calendar/colors');
      const result = await response.json();

      if (result.success && result.data) {
        return result.data;
      }

      return null;
    } catch (error: any) {
      toast.error('Error loading calendar colors:', error);
      return null;
    }
  }, [authStatus.isAuthenticated]);

  return {
    // State
    authStatus,
    calendars,
    events,
    syncSettings,
    syncConflicts,
    isLoading,
    isSyncing,
    lastSyncAt,
    syncError,

    // Authentication
    authenticate,
    disconnect,
    loadAuthStatus,

    // Calendar management
    loadCalendars,
    loadEvents,
    getEventsInRange,

    // Event management
    createEvent,
    updateEvent,
    deleteEvent,
    checkConflicts,

    // Sync management
    updateSyncSettings,
    performSync,
    cancelSync,
    syncTaskToEvent,

    // Conflict resolution
    resolveConflict,

    // Utilities
    getCalendarColors,
    setupAutoSync,
    clearAutoSync,

    // Constants
    REQUIRED_SCOPES,
    SYNC_INTERVALS,
  };
};

export default useGoogleCalendar;
