import redis, { RedisHelper } from '@/lib/redis';

export class CacheService {
  // Default TTL values (in seconds)
  private static readonly DEFAULT_TTL = 5 * 60; // 5 minutes
  private static readonly SHORT_TTL = 2 * 60; // 2 minutes
  private static readonly MEDIUM_TTL = 10 * 60; // 10 minutes
  private static readonly LONG_TTL = 30 * 60; // 30 minutes

  /**
   * Set cache with custom TTL
   */
  static async set(key: string, data: any, ttl: number = this.DEFAULT_TTL): Promise<void> {
    try {
      if (!redis || !RedisHelper.isConnected()) {
        console.warn('Redis not available, skipping cache set');
        return;
      }

      await RedisHelper.setJSON(key, data, ttl);
      console.log(`Cached data with key: ${key}`);
    } catch (error) {
      console.error('Error setting cache:', error);
    }
  }

  /**
   * Get cached data
   */
  static async get<T = any>(key: string): Promise<T | null> {
    try {
      if (!redis || !RedisHelper.isConnected()) {
        return null;
      }

      const data = await RedisHelper.getJSON<T>(key);
      if (data) {
        console.log(`Cache hit for key: ${key}`);
      }
      return data;
    } catch (error) {
      console.error('Error getting cache:', error);
      return null;
    }
  }

  /**
   * Delete cache by key
   */
  static async delete(key: string): Promise<void> {
    try {
      if (!redis || !RedisHelper.isConnected()) {
        return;
      }

      await redis.del(key);
      console.log(`Deleted cache for key: ${key}`);
    } catch (error) {
      console.error('Error deleting cache:', error);
    }
  }

  /**
   * Delete multiple cache keys by pattern
   */
  static async deleteByPattern(pattern: string): Promise<void> {
    try {
      if (!redis || !RedisHelper.isConnected()) {
        return;
      }

      const deletedCount = await RedisHelper.deleteByPattern(pattern);
      console.log(`Deleted ${deletedCount} cache entries matching pattern: ${pattern}`);
    } catch (error) {
      console.error('Error deleting cache by pattern:', error);
    }
  }

  /**
   * Generate cache key
   */
  static generateKey(prefix: string, ...parts: string[]): string {
    return RedisHelper.generateKey(prefix, ...parts);
  }

  /**
   * Cache with short TTL (2 minutes)
   */
  static async setShort(key: string, data: any): Promise<void> {
    return this.set(key, data, this.SHORT_TTL);
  }

  /**
   * Cache with medium TTL (10 minutes)
   */
  static async setMedium(key: string, data: any): Promise<void> {
    return this.set(key, data, this.MEDIUM_TTL);
  }

  /**
   * Cache with long TTL (30 minutes)
   */
  static async setLong(key: string, data: any): Promise<void> {
    return this.set(key, data, this.LONG_TTL);
  }

  /**
   * Check if key exists in cache
   */
  static async exists(key: string): Promise<boolean> {
    try {
      if (!redis || !RedisHelper.isConnected()) {
        return false;
      }

      const result = await redis.exists(key);
      return result === 1;
    } catch (error) {
      console.error('Error checking cache existence:', error);
      return false;
    }
  }

  /**
   * Get cache TTL (time to live)
   */
  static async getTTL(key: string): Promise<number> {
    try {
      if (!redis || !RedisHelper.isConnected()) {
        return -1;
      }

      return await redis.ttl(key);
    } catch (error) {
      console.error('Error getting cache TTL:', error);
      return -1;
    }
  }

  /**
   * Extend cache TTL
   */
  static async extendTTL(key: string, ttl: number): Promise<void> {
    try {
      if (!redis || !RedisHelper.isConnected()) {
        return;
      }

      await redis.expire(key, ttl);
    } catch (error) {
      console.error('Error extending cache TTL:', error);
    }
  }
}
