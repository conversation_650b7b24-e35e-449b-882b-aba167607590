import React, { useState } from 'react';
import { Download, FileText, Image, FileCode, Printer } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

import { useToast } from '@/hooks/use-toast';
import { MindMap } from '@/types/MindMapsTypes';
import { toPng, toJpeg, toSvg } from 'html-to-image';
import jsPDF from 'jspdf';

interface ExportMindMapModalProps {
  isOpen: boolean;
  onClose: () => void;
  mindMap: MindMap;
}

export function ExportMindMapModal({ isOpen, onClose, mindMap }: ExportMindMapModalProps) {
  const { toast } = useToast();
  const [exportFormat, setExportFormat] = useState<'png' | 'jpg' | 'svg' | 'pdf' | 'json'>('png');
  const [includeBackground, setIncludeBackground] = useState(true);
  const [quality, setQuality] = useState([0.8]);
  const [scale, setScale] = useState([2]);
  const [isExporting, setIsExporting] = useState(false);

  const exportFormats = [
    {
      value: 'png',
      label: 'PNG Image',
      icon: Image,
      description: 'High quality image with transparency support',
      extension: '.png',
    },
    {
      value: 'jpg',
      label: 'JPEG Image',
      icon: Image,
      description: 'Compressed image format, smaller file size',
      extension: '.jpg',
    },
    {
      value: 'svg',
      label: 'SVG Vector',
      icon: FileCode,
      description: 'Scalable vector graphics, perfect for print',
      extension: '.svg',
    },
    {
      value: 'pdf',
      label: 'PDF Document',
      icon: FileText,
      description: 'Portable document format for sharing',
      extension: '.pdf',
    },
    {
      value: 'json',
      label: 'JSON Data',
      icon: FileCode,
      description: 'Raw data format for backup or import',
      extension: '.json',
    },
  ];

  const getCanvasElement = () => {
    const reactFlowElement = document.querySelector('.react-flow');
    if (!reactFlowElement) {
      throw new Error('Mind map canvas not found');
    }
    return reactFlowElement as HTMLElement;
  };

  const generateFileName = (format: string) => {
    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
    const sanitizedTitle = mindMap.title.replace(/[^a-z0-9]/gi, '_').toLowerCase();
    return `${sanitizedTitle}_${timestamp}.${format}`;
  };

  const exportAsImage = async (format: 'png' | 'jpg' | 'svg') => {
    try {
      const element = getCanvasElement();
      const options = {
        quality: quality[0],
        pixelRatio: scale[0],
        backgroundColor: includeBackground ? '#ffffff' : 'transparent',
        style: {
          transform: 'scale(1)',
          transformOrigin: 'top left',
        },
      };

      let dataUrl: string;

      switch (format) {
        case 'png':
          dataUrl = await toPng(element, options);
          break;
        case 'jpg':
          dataUrl = await toJpeg(element, { ...options, backgroundColor: '#ffffff' });
          break;
        case 'svg':
          dataUrl = await toSvg(element, options);
          break;
        default:
          throw new Error('Unsupported format');
      }

      // Download the image
      const link = document.createElement('a');
      link.download = generateFileName(format);
      link.href = dataUrl;
      link.click();

      toast({
        title: 'Export successful',
        description: `Mind map exported as ${format.toUpperCase()}`,
      });
    } catch (error) {
      console.error('Export failed:', error);
      toast({
        title: 'Export failed',
        description: 'Could not export mind map. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const exportAsPDF = async () => {
    try {
      const element = getCanvasElement();
      const canvas = await toPng(element, {
        quality: quality[0],
        pixelRatio: scale[0],
        backgroundColor: includeBackground ? '#ffffff' : 'transparent',
      });

      const pdf = new jsPDF({
        orientation: 'landscape',
        unit: 'px',
        format: [element.offsetWidth, element.offsetHeight],
      });

      const imgWidth = element.offsetWidth;
      const imgHeight = element.offsetHeight;

      pdf.addImage(canvas, 'PNG', 0, 0, imgWidth, imgHeight);
      pdf.save(generateFileName('pdf'));

      toast({
        title: 'Export successful',
        description: 'Mind map exported as PDF',
      });
    } catch (error) {
      console.error('PDF export failed:', error);
      toast({
        title: 'Export failed',
        description: 'Could not export mind map as PDF. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const exportAsJSON = () => {
    try {
      const exportData = {
        mindMap: {
          id: mindMap._id,
          title: mindMap.title,
          description: mindMap.description,
          nodes: mindMap.nodes,
          connections: mindMap.connections,
          viewport: mindMap.viewport,
          tags: mindMap.tags,
          status: mindMap.status,
          createdAt: mindMap.createdAt,
          updatedAt: mindMap.updatedAt,
        },
        exportedAt: new Date().toISOString(),
        version: '1.0',
      };

      const dataStr = JSON.stringify(exportData, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);

      const link = document.createElement('a');
      link.download = generateFileName('json');
      link.href = url;
      link.click();

      URL.revokeObjectURL(url);

      toast({
        title: 'Export successful',
        description: 'Mind map data exported as JSON',
      });
    } catch (error) {
      console.error('JSON export failed:', error);
      toast({
        title: 'Export failed',
        description: 'Could not export mind map data. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const handleExport = async () => {
    setIsExporting(true);

    try {
      switch (exportFormat) {
        case 'png':
        case 'jpg':
        case 'svg':
          await exportAsImage(exportFormat);
          break;
        case 'pdf':
          await exportAsPDF();
          break;
        case 'json':
          exportAsJSON();
          break;
        default:
          throw new Error('Unsupported export format');
      }
    } catch (error) {
      // Error handling is done in individual export functions
    } finally {
      setIsExporting(false);
    }
  };

  const selectedFormat = exportFormats.find(f => f.value === exportFormat);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-lg">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Download className="w-5 h-5" />
            Export Mind Map
          </DialogTitle>
          <DialogDescription>Choose your preferred format and settings</DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Format Selection */}
          <div className="space-y-3">
            <Label>Export Format</Label>
            <Select value={exportFormat} onValueChange={(value: any) => setExportFormat(value)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {exportFormats.map(format => (
                  <SelectItem key={format.value} value={format.value}>
                    <div className="flex items-center gap-2">
                      <format.icon className="w-4 h-4" />
                      <span>{format.label}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {selectedFormat && (
              <p className="text-xs text-gray-500">{selectedFormat.description}</p>
            )}
          </div>

          {/* Format-specific options */}
          {(exportFormat === 'png' ||
            exportFormat === 'jpg' ||
            exportFormat === 'svg' ||
            exportFormat === 'pdf') && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label>Include background</Label>
                  <p className="text-xs text-gray-500">Add white background to export</p>
                </div>
                <Switch checked={includeBackground} onCheckedChange={setIncludeBackground} />
              </div>

              {(exportFormat === 'png' || exportFormat === 'jpg') && (
                <>
                  <div className="space-y-2">
                    <Label>Quality: {Math.round(quality[0] * 100)}%</Label>
                    <Slider
                      value={quality}
                      onValueChange={setQuality}
                      max={1}
                      min={0.1}
                      step={0.1}
                      className="w-full"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>Scale: {scale[0]}x</Label>
                    <Slider
                      value={scale}
                      onValueChange={setScale}
                      max={4}
                      min={1}
                      step={0.5}
                      className="w-full"
                    />
                    <p className="text-xs text-gray-500">
                      Higher scale = better quality but larger file size
                    </p>
                  </div>
                </>
              )}
            </div>
          )}

          {/* Preview info */}
          <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div className="flex items-center gap-2 text-sm">
              {selectedFormat && <selectedFormat.icon className="w-4 h-4" />}
              <span className="font-medium">
                {mindMap.title}
                {selectedFormat?.extension}
              </span>
            </div>
            <p className="text-xs text-gray-500 mt-1">
              {mindMap.nodes?.length || 0} nodes • {mindMap.connections?.length || 0} connections
            </p>
          </div>

          {/* Export button */}
          <div className="flex gap-2">
            <Button onClick={handleExport} disabled={isExporting} className="flex-1">
              {isExporting ? (
                'Exporting...'
              ) : (
                <>
                  <Download className="w-4 h-4 mr-2" />
                  Export {selectedFormat?.label}
                </>
              )}
            </Button>
            <Button variant="outline" onClick={() => window.print()} disabled={isExporting}>
              <Printer className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
