import React from 'react';
import type { Metadata } from 'next';
import './globals.css';
import { Inter, Outfit } from 'next/font/google';
import Providers from '@/components/Providers/query-provider';
import { NextUiProviders } from '@/context/HeroUiProvider';
import { Toaster } from '@/components/ui/toaster';
import { Toaster as Sonner } from '@/components/ui/sonner';
import ClientProviders from '@/context/ClientProviders';

import { ThemeProvider } from '@/components/Providers/ThemeProvider';
import { ThemeSyncProvider } from '@/components/Providers/ThemeSyncProvider';
import { Analytics } from '@vercel/analytics/react';
import { ErrorBoundary } from 'react-error-boundary';
import ErrorFallback from '@/components/ErrorFallback';
import PWAProvider from '@/components/PWA/PWAProvider';

const inter = Inter({ subsets: ['latin'] });

const outfit = Outfit({
  subsets: ['latin'],
  variable: '--font-outfit',
  display: 'swap',
});

export const metadata: Metadata = {
  title: 'TaskMantra',
  description: 'TaskMantra is a project management tool that helps you manage your projects.',
  metadataBase: new URL('https://taskmantra.app'),
  applicationName: 'TaskMantra',
  keywords: [
    'project management',
    'task management',
    'productivity',
    'collaboration',
    'team management',
  ],
  authors: [{ name: 'TaskMantra Team' }],
  creator: 'TaskMantra Team',
  publisher: 'TaskMantra',
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://taskmantra.app',
    title: 'TaskMantra - Project Management Tool',
    description: 'A powerful project and task management tool for teams and individuals.',
    siteName: 'TaskMantra',
    images: [
      {
        url: '/logo.png',
        width: 800,
        height: 600,
        alt: 'TaskMantra Logo',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'TaskMantra - Project Management Tool',
    description: 'A powerful project and task management tool for teams and individuals.',
    images: ['/logo.png'],
  },
  formatDetection: {
    telephone: false,
  },
  category: 'productivity',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <meta
          name="viewport"
          content="width=device-width, initial-scale=1, maximum-scale=5, viewport-fit=cover, user-scalable=yes"
        />

        {/* Security Headers */}
        <meta httpEquiv="X-Content-Type-Options" content="nosniff" />
        <meta httpEquiv="X-XSS-Protection" content="1; mode=block" />
        <meta
          httpEquiv="Content-Security-Policy"
          content="default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://analytics.taskmantra.app; connect-src 'self' https://*.taskmantra.app https://api.taskmantra.app; img-src 'self' data: blob: https://*.taskmantra.app https://res.cloudinary.com https://*.googleusercontent.com; style-src 'self' 'unsafe-inline';"
        />

        {/* Canonical URL */}
        <link rel="canonical" href="https://taskmantra.app" />

        {/* PWA Manifest */}
        <link rel="manifest" href="/manifest.json" />

        {/* Icons */}
        <link rel="icon" href="/favicon.ico" sizes="any" />
        <link rel="icon" type="image/png" sizes="32x32" href="/icons/icon-32x32.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/icons/icon-16x16.png" />

        {/* Apple Touch Icons */}
        <link rel="apple-touch-icon" href="/icons/icon-192x192.png" />
        <link rel="apple-touch-icon" sizes="152x152" href="/icons/icon-152x152.png" />
        <link rel="apple-touch-icon" sizes="180x180" href="/icons/icon-192x192.png" />

        {/* Apple PWA Meta Tags */}
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="TaskMantra" />

        {/* Microsoft Tiles */}
        <meta name="msapplication-TileImage" content="/icons/icon-144x144.png" />
        <meta name="msapplication-TileColor" content="#3b82f6" />

        {/* Theme Colors */}
        <meta name="theme-color" content="#3b82f6" />
        <meta name="msapplication-navbutton-color" content="#3b82f6" />

        {/* Preload Critical Resources */}
        <link
          rel="preload"
          href="/fonts/GeistVF.woff"
          as="font"
          type="font/woff"
          crossOrigin="anonymous"
        />
        <link
          rel="preload"
          href="/fonts/GeistMonoVF.woff"
          as="font"
          type="font/woff"
          crossOrigin="anonymous"
        />
        <link rel="preload" href="/logo.png" as="image" />

        {/* Connection Status Script */}
        <script
          id="connection-status"
          dangerouslySetInnerHTML={{
            __html: `
              // Handle offline status changes
              window.addEventListener('online', function() {
                document.dispatchEvent(new CustomEvent('connectionChange', { detail: { online: true } }));
              });
              
              window.addEventListener('offline', function() {
                document.dispatchEvent(new CustomEvent('connectionChange', { detail: { online: false } }));
              });
              
              // Check initial status
              const isOnline = navigator.onLine;
              document.dispatchEvent(new CustomEvent('connectionChange', { detail: { online: isOnline } }));
            `,
          }}
        />
      </head>
      <body
        className={`${inter.className} ${outfit.variable} antialiased`}
        suppressHydrationWarning
      >
        <ErrorBoundary FallbackComponent={ErrorFallback}>
          <ClientProviders>
            <ThemeProvider
              attribute="class"
              defaultTheme="dark"
              enableSystem
              disableTransitionOnChange
            >
              <ThemeSyncProvider>
                <Providers>
                  <NextUiProviders>
                    <main>{children}</main>
                    <Analytics />
                    <Toaster />
                    <Sonner richColors />
                    <PWAProvider />
                  </NextUiProviders>
                </Providers>
              </ThemeSyncProvider>
            </ThemeProvider>
          </ClientProviders>
        </ErrorBoundary>
      </body>
    </html>
  );
}
