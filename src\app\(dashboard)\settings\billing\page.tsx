'use client';

import { useState } from 'react';
import { <PERSON>, CardContent, CardHeader } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { StorageDashboard } from '@/components/Storage/StorageDashboard';
import { useStorage } from '@/hooks/useStorage';
import { useSubscription } from '@/hooks/useSubscription';
import { useSession } from 'next-auth/react';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { SubscriptionDashboard } from '@/components/Subscription/SubscriptionDashboard';
import { Separator } from '@/components/ui/separator';
import { BillingHistorySection } from '@/components/Billing/BillingHistorySection';
import { SubscriptionService } from '@/services/Subscription.service';
import { toast } from 'sonner';

export default function BillingPage() {
  const [activeTab, setActiveTab] = useState('subscription');
  const [downloadingReceipt, setDownloadingReceipt] = useState<string | null>(null);
  const { data: session } = useSession();
  const { formatMB } = useStorage();
  const { subscriptionInfo, billingHistory, isLoadingInfo, isLoadingHistory, formatPrice } =
    useSubscription(session?.user?.organizationId || undefined);

  const handleDownloadReceipt = async (transactionId: string) => {
    try {
      setDownloadingReceipt(transactionId);
      await SubscriptionService.downloadReceiptFile(transactionId);
      toast.success('Receipt downloaded successfully!');
    } catch (error: any) {
      toast.error(error.message || 'Failed to download receipt');
    } finally {
      setDownloadingReceipt(null);
    }
  };

  return (
    <div className="space-y-6 px-4">
      <div>
        <h3 className="text-lg font-medium theme-text-primary">Billing & Plans</h3>
        <p className="text-sm theme-text-secondary">
          Manage your subscription, storage, and payment methods
        </p>
      </div>
      <Separator className="theme-divider" />
      <Tabs defaultValue={activeTab} className="space-y-4" onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="subscription">Subscription</TabsTrigger>
          <TabsTrigger value="storage">Storage</TabsTrigger>
          <TabsTrigger value="billing-history">Billing History</TabsTrigger>
        </TabsList>

        <TabsContent value="subscription" className="space-y-4">
          {isLoadingInfo ? (
            <Card>
              <CardHeader>
                <Skeleton className="h-6 w-32" />
                <Skeleton className="h-4 w-64" />
              </CardHeader>
              <CardContent className="space-y-4">
                <Skeleton className="h-20 w-full" />
                <Skeleton className="h-16 w-full" />
              </CardContent>
            </Card>
          ) : subscriptionInfo ? (
            <SubscriptionDashboard />
          ) : (
            <Alert>
              <AlertDescription>
                Unable to load subscription information. Please try again later.
              </AlertDescription>
            </Alert>
          )}
        </TabsContent>

        <TabsContent value="storage" className="space-y-4">
          <StorageDashboard />
        </TabsContent>

        <TabsContent value="billing-history" className="space-y-4">
          <BillingHistorySection
            billingHistory={billingHistory}
            isLoadingHistory={isLoadingHistory}
            formatPrice={formatPrice}
            formatMB={formatMB}
            onBuyStorage={() => setActiveTab('storage')}
            onDownloadReceipt={handleDownloadReceipt}
            downloadingReceipt={downloadingReceipt}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}
