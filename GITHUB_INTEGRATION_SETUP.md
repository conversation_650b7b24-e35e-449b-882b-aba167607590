# GitHub Integration Setup Guide

This guide will help you set up the GitHub integration for your TaskMantra application.

## Prerequisites

1. A GitHub account
2. Admin access to your TaskMantra application
3. Access to your application's environment variables

## Step 1: Create a GitHub OAuth App

1. Go to [GitHub Developer Settings](https://github.com/settings/developers)
2. Click "New OAuth App"
3. Fill in the application details:
   - **Application name**: TaskMantra GitHub Integration
   - **Homepage URL**: Your application's URL (e.g., `https://your-domain.com`)
   - **Application description**: GitHub integration for TaskMantra task management
   - **Authorization callback URL**: `https://your-domain.com/api/github/oauth/callback`

4. Click "Register application"
5. Note down the **Client ID** and generate a **Client Secret**

## Step 2: Configure Environment Variables

Add the following environment variables to your `.env.local` file:

```env
# GitHub OAuth Configuration
GITHUB_CLIENT_ID=your_github_client_id_here
GITHUB_CLIENT_SECRET=your_github_client_secret_here

# Make sure you have these existing variables
NEXTAUTH_URL=https://your-domain.com
NEXTAUTH_SECRET=your_nextauth_secret_here
```

## Step 3: GitHub Webhook Configuration

For automatic task creation from GitHub issues, you'll need to set up webhooks:

1. The webhook URL will be: `https://your-domain.com/api/github/webhook`
2. Webhooks are automatically configured when you connect a repository
3. The system will listen for the following events:
   - `issues` (for issue creation, updates, and closure)
   - `pull_request` (for PR events, if enabled)

## Step 4: Required GitHub Permissions

The GitHub integration requests the following OAuth scopes:

- `repo`: Access to repositories (read/write)
- `user:email`: Access to user email addresses
- `admin:repo_hook`: Manage repository webhooks

## Step 5: Testing the Integration

1. Start your application with the new environment variables
2. Navigate to the Integrations page
3. Click "Connect" on the GitHub integration card
4. You should be redirected to GitHub for authorization
5. After authorization, you'll be redirected back to the GitHub Hub page

## Features

### Repository Management
- Browse and connect to your GitHub repositories
- View repository details (stars, forks, language, etc.)
- Disconnect repositories when no longer needed

### Issue Synchronization
- View GitHub issues directly in TaskMantra
- Create tasks from GitHub issues with one click
- Automatic task creation when new issues are created (configurable)
- Sync issue status changes (open/closed) with task status

### Pull Request Integration
- View pull requests from connected repositories
- Track PR status and details
- Optional task creation from PRs

### Webhook Automation
- Automatic task creation when new issues are opened
- Task status updates when issues are closed/reopened
- Secure webhook signature verification

## Security Features

1. **OAuth Token Encryption**: Access tokens are encrypted before storage
2. **Webhook Signature Verification**: All webhook payloads are verified using HMAC-SHA256
3. **Scope Limitation**: Only requests necessary permissions
4. **Token Refresh**: Handles token expiration and refresh flows

## Troubleshooting

### Common Issues

1. **OAuth Redirect Mismatch**
   - Ensure the callback URL in GitHub matches exactly: `https://your-domain.com/api/github/oauth/callback`
   - Check that `NEXTAUTH_URL` is set correctly

2. **Webhook Delivery Failures**
   - Verify your application is accessible from the internet
   - Check webhook logs in GitHub repository settings
   - Ensure the webhook URL is correct: `https://your-domain.com/api/github/webhook`

3. **Permission Errors**
   - Verify you have admin access to the repositories you're trying to connect
   - Check that the GitHub OAuth app has the correct permissions

### Debug Mode

To enable debug logging for GitHub integration:

1. Add to your environment variables:
   ```env
   DEBUG=github:*
   ```

2. Check your application logs for detailed GitHub API interactions

## API Endpoints

The GitHub integration exposes the following API endpoints:

- `GET /api/github/oauth/authorize` - Initiate OAuth flow
- `GET /api/github/oauth/callback` - OAuth callback handler
- `GET /api/github/status` - Check connection status
- `DELETE /api/github/disconnect` - Disconnect GitHub integration
- `GET /api/github/repositories/available` - List available repositories
- `GET /api/github/repositories` - List connected repositories
- `POST /api/github/repositories/connect` - Connect a repository
- `POST /api/github/repositories/disconnect` - Disconnect a repository
- `GET /api/github/repositories/{id}/issues` - Get repository issues
- `GET /api/github/repositories/{id}/pulls` - Get repository pull requests
- `POST /api/github/create-task` - Create task from GitHub issue
- `POST /api/github/webhook` - Webhook endpoint for GitHub events

## Database Models

The integration uses the following MongoDB models:

1. **Integration**: Stores OAuth tokens and user connection info
2. **GitHubRepository**: Stores connected repository details and sync settings
3. **GitHubWebhook**: Manages webhook configurations and delivery stats

## Rate Limiting

GitHub API has rate limits:
- **Authenticated requests**: 5,000 per hour
- **Webhook deliveries**: No specific limit, but GitHub may throttle

The integration includes automatic rate limit handling and retry logic.

## Support

For issues with the GitHub integration:

1. Check the application logs for error messages
2. Verify your GitHub OAuth app configuration
3. Test the webhook endpoint manually
4. Review GitHub's webhook delivery logs

## Security Considerations

1. **Environment Variables**: Never commit GitHub credentials to version control
2. **Webhook Secrets**: Use strong, randomly generated webhook secrets
3. **Token Storage**: Tokens are encrypted at rest in the database
4. **HTTPS**: Always use HTTPS for production deployments
5. **Webhook Verification**: All webhook payloads are cryptographically verified

## Updates and Maintenance

1. **Token Refresh**: The system automatically handles token refresh
2. **Webhook Health**: Monitor webhook delivery success rates
3. **API Changes**: Stay updated with GitHub API changes and deprecations
4. **Security Updates**: Regularly update dependencies and review security practices
