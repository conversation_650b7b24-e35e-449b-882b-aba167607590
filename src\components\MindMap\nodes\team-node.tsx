'use client';

import { memo } from 'react';
import { Handle, Position, type NodeProps } from 'reactflow';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Users, Mail, UserCheck } from 'lucide-react';

interface TeamNodeData {
  title: string;
  role: string;
  email: string;
  status: 'active' | 'busy' | 'offline';
}

export const TeamNode = memo(({ data, selected }: NodeProps<TeamNodeData>) => {
  const statusColors = {
    active: 'bg-green-100 text-green-800 border-green-200',
    busy: 'bg-yellow-100 text-yellow-800 border-yellow-200',
    offline: 'bg-gray-100 text-gray-800 border-gray-200',
  };

  const statusIcons = {
    active: 'bg-green-500',
    busy: 'bg-yellow-500',
    offline: 'bg-gray-500',
  };

  return (
    <Card
      className={`min-w-[200px] bg-gradient-to-br from-indigo-50 to-blue-50 border-indigo-200 shadow-lg transition-all duration-200 ${
        selected ? 'ring-2 ring-indigo-500 shadow-xl scale-105' : 'hover:shadow-xl hover:scale-102'
      }`}
    >
      <Handle
        type="target"
        position={Position.Top}
        className="w-3 h-3 bg-indigo-500 border-2 border-white"
      />

      <div className="p-4">
        <div className="flex items-center space-x-2 mb-3">
          <div className="relative">
            <Users className="h-5 w-5 text-indigo-600" />
            <div
              className={`absolute -top-1 -right-1 w-3 h-3 rounded-full ${statusIcons[data.status]} border border-white`}
            />
          </div>
          <h3 className="font-semibold text-gray-900 text-sm">{data.title}</h3>
        </div>

        <div className="space-y-2">
          <div className="flex items-center text-xs text-gray-600">
            <UserCheck className="h-3 w-3 mr-1" />
            {data.role}
          </div>

          {data.email && (
            <div className="flex items-center text-xs text-gray-600">
              <Mail className="h-3 w-3 mr-1" />
              {data.email}
            </div>
          )}

          <Badge variant="outline" className={`text-xs ${statusColors[data.status]}`}>
            {data.status}
          </Badge>
        </div>
      </div>

      <Handle
        type="source"
        position={Position.Bottom}
        className="w-3 h-3 bg-indigo-500 border-2 border-white"
      />
    </Card>
  );
});

TeamNode.displayName = 'TeamNode';
