{"name": "task-management", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack -p 4000", "dev:webpack": "next dev -p 4000", "dev:clean": "set NODE_OPTIONS=--max-old-space-size=4096 && next dev --turbo", "build": "next build", "start": "next start", "lint": "next lint", "prepare": "husky", "type-check": "tsc --noEmit", "check-all": "bun run lint && bun run type-check && bun run build"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"]}, "husky": {"hooks": {"pre-commit": "bun lint-staged", "pre-push": "bun run check-all"}}, "dependencies": {"@ariakit/react": "^0.4.17", "@auth/core": "^0.40.0", "@auth/mongodb-adapter": "^3.10.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emoji-mart/data": "^1.2.1", "@heroui/avatar": "^2.2.7", "@heroui/dropdown": "^2.3.17", "@heroui/input": "^2.4.10", "@heroui/react": "^2.6.14", "@heroui/skeleton": "^2.2.9", "@heroui/spinner": "^2.2.10", "@heroui/system": "^2.4.7", "@heroui/theme": "^2.4.6", "@hono/zod-validator": "^0.7.0", "@hookform/resolvers": "^3.9.1", "@neondatabase/serverless": "^0.10.4", "@nextui-org/react": "^2.6.11", "@nextui-org/system": "^2.4.6", "@nextui-org/theme": "^2.4.5", "@notionhq/client": "^3.0.1", "@radix-ui/react-alert-dialog": "^1.1.11", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.5", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.1", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.2.4", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-toolbar": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@react-email/components": "^0.0.36", "@tanstack/react-query": "^5.62.9", "@tanstack/react-query-devtools": "^5.72.2", "@types/d3": "^7.4.3", "@types/dagre": "^0.7.53", "@types/jsonwebtoken": "^9.0.9", "@types/mongoose": "^5.11.96", "@types/react-big-calendar": "^1.16.1", "@types/react-window": "^1.8.8", "@udecode/cn": "^48.0.3", "@udecode/plate": "^48.0.5", "@udecode/plate-alignment": "^48.0.0", "@udecode/plate-autoformat": "^48.0.0", "@udecode/plate-basic-marks": "^48.0.0", "@udecode/plate-block-quote": "^48.0.0", "@udecode/plate-break": "^48.0.0", "@udecode/plate-callout": "^48.0.0", "@udecode/plate-caption": "^48.0.0", "@udecode/plate-code-block": "^48.0.0", "@udecode/plate-combobox": "^48.0.0", "@udecode/plate-comments": "^48.0.0", "@udecode/plate-date": "^48.0.0", "@udecode/plate-dnd": "^48.0.0", "@udecode/plate-emoji": "^48.0.0", "@udecode/plate-excalidraw": "^48.0.0", "@udecode/plate-font": "^48.0.0", "@udecode/plate-heading": "^48.0.0", "@udecode/plate-highlight": "^48.0.0", "@udecode/plate-horizontal-rule": "^48.0.0", "@udecode/plate-indent-list": "^48.0.0", "@udecode/plate-kbd": "^48.0.0", "@udecode/plate-layout": "^48.0.0", "@udecode/plate-line-height": "^48.0.0", "@udecode/plate-link": "^48.0.0", "@udecode/plate-markdown": "^48.0.0", "@udecode/plate-math": "^48.0.4", "@udecode/plate-media": "^48.0.0", "@udecode/plate-node-id": "^48.0.0", "@udecode/plate-select": "^48.0.0", "@udecode/plate-selection": "^48.0.5", "@udecode/plate-slash-command": "^48.0.0", "@udecode/plate-table": "^48.0.0", "@udecode/plate-toggle": "^48.0.0", "@udecode/plate-trailing-block": "^48.0.0", "@vercel/analytics": "^1.5.0", "axios": "^1.8.2", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "cloudinary": "^2.5.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "cookie": "^1.0.2", "d3": "^7.9.0", "dagre": "^0.8.5", "date-fns": "^3.6.0", "dotenv": "^16.4.7", "formik": "^2.4.6", "framer-motion": "^12.23.0", "googleapis": "^150.0.1", "highlight.js": "^11.11.1", "hono": "^4.6.14", "html-to-image": "^1.11.13", "html2canvas": "^1.4.1", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lodash-es": "^4.17.21", "lowlight": "^3.3.0", "lucide-react": "^0.460.0", "moment": "^2.30.1", "mongodb": "^6.12.0", "mongoose": "^8.16.1", "next": "^15.3.3", "next-auth": "^4.24.11", "next-themes": "^0.4.4", "pako": "^2.1.0", "razorpay": "^2.9.4", "react": "^19.0.0", "react-big-calendar": "^1.18.0", "react-d3-graph": "^2.6.0", "react-day-picker": "8.10.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^19.0.0", "react-dropzone": "^14.3.5", "react-error-boundary": "^6.0.0", "react-google-calendar-api": "^2.3.0", "react-hook-form": "^7.53.2", "react-icons": "^5.4.0", "react-image-crop": "^11.0.7", "react-intersection-observer": "^9.16.0", "react-markdown": "^10.1.0", "react-resizable-panels": "^2.1.7", "react-select": "^5.9.0", "react-sortable-hoc": "^2.0.0", "react-to-print": "^3.0.6", "react-window": "^1.8.11", "react-window-infinite-loader": "^1.0.10", "reactflow": "^11.11.4", "recharts": "^2.15.4", "rehype-highlight": "^7.0.2", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "resend": "^4.4.1", "sonner": "^1.7.0", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "use-file-picker": "^2.1.4", "vaul": "^1.1.1", "vis-network": "^9.1.13", "yup": "^1.6.1", "zod": "^3.23.8", "zustand": "^5.0.3"}, "devDependencies": {"@types/node": "^24.0.15", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^8", "eslint-config-next": "^15.2.3", "husky": "^9.1.7", "lint-staged": "^15.5.1", "postcss": "^8", "prettier": "^3.5.3", "tailwindcss": "^3.4.1", "tsx": "^4.19.4", "typescript": "^5", "webpack": "^5.100.2"}}