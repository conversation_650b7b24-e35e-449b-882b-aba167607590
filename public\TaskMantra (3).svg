<svg viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
  <style>
    .doc { fill: #FFFFFF; stroke: #4A90E2; stroke-width: 2; }
    .line { stroke: #4A90E2; stroke-width: 2; stroke-linecap: round; }
    .check-box { fill: none; stroke: #4A90E2; stroke-width: 2; }
    .check { fill: none; stroke: #4A90E2; stroke-width: 2; stroke-linecap: round; stroke-linejoin: round;
      stroke-dasharray: 6; stroke-dashoffset: 6; animation: draw 0.5s forwards; animation-delay: var(--d);
    }
    @keyframes draw { to { stroke-dashoffset: 0; } }
    .timeline { stroke: #7B8D93; stroke-width: 2; }
    .dot { fill: #7B8D93; animation: highlight 0.4s forwards; animation-delay: var(--dd); }
    @keyframes highlight { to { fill: #4A90E2; } }
    .team { fill: #7B8D93; transform-origin: center; transform: scale(0); animation: pop 0.4s forwards; animation-delay: var(--td); }
    @keyframes pop { to { transform: scale(1); fill: #4A90E2; } }
  </style>

  <!-- Document background -->
  <rect class="doc" x="6" y="6" width="52" height="52" rx="4"/>

  <!-- Checklist items -->
  <g class="check-item" transform="translate(14,18)" style="--d:0.5s">
    <rect class="check-box" width="6" height="6" rx="1"/>
    <path class="check" d="M1.5 3.5 L2.5 4.5 L4.5 2.5"/>
    <line class="line" x1="10" y1="3" x2="40" y2="3"/>
  </g>
  <g class="check-item" transform="translate(14,28)" style="--d:0.8s">
    <rect class="check-box" width="6" height="6" rx="1"/>
    <path class="check" d="M1.5 3.5 L2.5 4.5 L4.5 2.5"/>
    <line class="line" x1="10" y1="3" x2="40" y2="3"/>
  </g>
  <g class="check-item" transform="translate(14,38)" style="--d:1.1s">
    <rect class="check-box" width="6" height="6" rx="1"/>
    <path class="check" d="M1.5 3.5 L2.5 4.5 L4.5 2.5"/>
    <line class="line" x1="10" y1="3" x2="40" y2="3"/>
  </g>

  <!-- Timeline -->
  <line class="timeline" x1="14" y1="50" x2="50" y2="50"/>
  <circle class="dot" style="--dd:1.4s" cx="14" cy="50" r="2.5"/>
  <circle class="dot" style="--dd:1.6s" cx="26" cy="50" r="2.5"/>
  <circle class="dot" style="--dd:1.8s" cx="38" cy="50" r="2.5"/>
  <circle class="dot" style="--dd:2s"  cx="50" cy="50" r="2.5"/>

  <!-- Team icons -->
  <circle class="team" style="--td:2.2s" cx="42" cy="14" r="3"/>
  <circle class="team" style="--td:2.4s" cx="34" cy="14" r="3"/>
  <circle class="team" style="--td:2.6s" cx="38" cy="20" r="3"/>
</svg>