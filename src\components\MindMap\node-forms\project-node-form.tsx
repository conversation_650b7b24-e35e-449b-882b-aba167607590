'use client';

import type React from 'react';

import { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { X, Plus, Save } from 'lucide-react';

interface ProjectNodeData {
  title: string;
  description: string;
  tasks: Array<{ id: string; title: string; completed: boolean }>;
  progress: number;
  status: 'active' | 'completed' | 'on-hold';
}

interface ProjectNodeFormProps {
  data: ProjectNodeData;
  onSave: (data: Partial<ProjectNodeData>) => void;
  onCancel: () => void;
}

export function ProjectNodeForm({ data, onSave, onCancel }: ProjectNodeFormProps) {
  const [formData, setFormData] = useState<ProjectNodeData>({
    ...data,
    tasks: [...data.tasks],
  });

  const handleChange = (field: keyof ProjectNodeData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleAddTask = () => {
    setFormData(prev => ({
      ...prev,
      tasks: [
        ...prev.tasks,
        { id: `task-${Date.now()}`, title: `Task ${prev.tasks.length + 1}`, completed: false },
      ],
    }));
  };

  const handleRemoveTask = (index: number) => {
    setFormData(prev => ({
      ...prev,
      tasks: prev.tasks.filter((_, i) => i !== index),
    }));
  };

  const handleTaskChange = (
    index: number,
    field: keyof ProjectNodeData['tasks'][0],
    value: any
  ) => {
    setFormData(prev => {
      const newTasks = [...prev.tasks];
      newTasks[index] = { ...newTasks[index], [field]: value };

      // Recalculate progress
      const completedTasks = newTasks.filter(task => task.completed).length;
      const progress =
        newTasks.length > 0 ? Math.round((completedTasks / newTasks.length) * 100) : 0;

      return { ...prev, tasks: newTasks, progress };
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  return (
    <Card className="min-w-[350px] p-4 bg-white shadow-lg">
      <form onSubmit={handleSubmit}>
        <div className="space-y-4">
          <div>
            <Label htmlFor="title">Project Title</Label>
            <Input
              id="title"
              value={formData.title}
              onChange={e => handleChange('title', e.target.value)}
              className="mt-1"
            />
          </div>

          <div>
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={e => handleChange('description', e.target.value)}
              className="mt-1"
              rows={3}
            />
          </div>

          <div>
            <Label htmlFor="status">Status</Label>
            <Select
              value={formData.status}
              onValueChange={value => handleChange('status', value as ProjectNodeData['status'])}
            >
              <SelectTrigger className="mt-1">
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="on-hold">On Hold</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <div className="flex items-center justify-between mb-2">
              <Label>Tasks</Label>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={handleAddTask}
                className="h-8 px-2 bg-transparent"
              >
                <Plus className="h-4 w-4 mr-1" /> Add Task
              </Button>
            </div>

            {formData.tasks.map((task, index) => (
              <div key={task.id} className="flex items-center mb-2">
                <Checkbox
                  checked={task.completed}
                  onCheckedChange={checked => handleTaskChange(index, 'completed', !!checked)}
                  className="mr-2"
                />
                <Input
                  value={task.title}
                  onChange={e => handleTaskChange(index, 'title', e.target.value)}
                  className="flex-1"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => handleRemoveTask(index)}
                  className="ml-2 h-8 w-8 p-0"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            ))}

            <div className="text-sm text-gray-500 mt-2">Progress: {formData.progress}%</div>
          </div>
        </div>

        <div className="flex justify-end space-x-2 mt-4">
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="submit">
            <Save className="h-4 w-4 mr-2" /> Save
          </Button>
        </div>
      </form>
    </Card>
  );
}
