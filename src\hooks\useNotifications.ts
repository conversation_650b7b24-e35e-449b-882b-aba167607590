'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { useSession } from 'next-auth/react';
import { useQueryClient, useQuery, useMutation } from '@tanstack/react-query';
import { NotificationClientService } from '@/services/NotificationClient.service';

export type NotificationType = {
  _id: string;
  userId: string;
  title: string;
  description: string;
  type: 'mention' | 'task' | 'team' | 'system' | 'onboarding';
  read: boolean;
  link: string;
  metadata?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
};

interface UseNotificationsReturn {
  notifications: NotificationType[];
  unreadCount: number;
  isConnected: boolean;
  error: string | null;
  fetchNotifications: (
    page?: number
  ) => Promise<{ notifications: NotificationType[]; hasMore: boolean; nextPage?: number }>;
  fetchUnreadCount: () => Promise<number>;
  markAsRead: (notificationId: string) => Promise<boolean>;
  markAllAsRead: () => Promise<boolean>;
  clearAllNotifications: () => Promise<boolean>;
  requestNotificationPermission: () => Promise<boolean>;
}

const SSE_CONFIG = {
  HEARTBEAT_TIMEOUT: 120000, // 2 minutes
  HEARTBEAT_CHECK_INTERVAL: 90000, // 1.5 minutes
  MAX_RECONNECT_ATTEMPTS: 5, // Reduced attempts
  RECONNECT_DELAYS: [2000, 5000, 10000, 30000, 60000], // Longer delays
  QUERY_DEBOUNCE_DELAY: 2000, // Increased debounce
  CONNECTION_STATUS_KEY: 'sse-connection-status',
};

export function useNotifications(): UseNotificationsReturn {
  const { data: session } = useSession();
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const queryClient = useQueryClient();

  const sseRef = useRef<{
    eventSource: EventSource | null;
    reconnectAttempt: number;
    reconnectTimer: ReturnType<typeof setTimeout> | null;
    lastHeartbeat: number;
    heartbeatInterval: ReturnType<typeof setInterval> | null;
    lastEventId: string;
    invalidationTimeout: ReturnType<typeof setTimeout> | null;
  }>({
    eventSource: null,
    reconnectAttempt: 0,
    reconnectTimer: null,
    lastHeartbeat: Date.now(),
    heartbeatInterval: null,
    lastEventId: '',
    invalidationTimeout: null,
  });

  const debouncedInvalidateQueries = useCallback(() => {
    if (sseRef.current.invalidationTimeout) {
      clearTimeout(sseRef.current.invalidationTimeout);
    }

    sseRef.current.invalidationTimeout = setTimeout(() => {
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
      queryClient.invalidateQueries({ queryKey: ['notifications', 'unread-count'] });
    }, SSE_CONFIG.QUERY_DEBOUNCE_DELAY);
  }, [queryClient]);

  const { data: notificationsData } = useQuery({
    queryKey: ['notifications'],
    queryFn: async () => NotificationClientService.getNotificationsClient(),
    enabled: !!session?.user,
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 20 * 60 * 1000, // 20 minutes
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false, // Disable to prevent conflicts with SSE
    retry: 2,
    retryDelay: attemptIndex => Math.min(2000 * 2 ** attemptIndex, 30000),
  });

  const { data: unreadCountData } = useQuery({
    queryKey: ['notifications', 'unread-count'],
    queryFn: async () => NotificationClientService.getUnreadCountClient(),
    enabled: !!session?.user,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false, // Disable to prevent conflicts with SSE
    retry: 1,
    retryDelay: attemptIndex => Math.min(2000 * 2 ** attemptIndex, 10000),
  });

  const markAsReadMutation = useMutation({
    mutationFn: async (notificationId: string) =>
      NotificationClientService.markAsReadClient(notificationId),
    onMutate: async (notificationId: string) => {
      await queryClient.cancelQueries({ queryKey: ['notifications'] });
      await queryClient.cancelQueries({ queryKey: ['notifications', 'unread-count'] });

      const previousNotifications = queryClient.getQueryData(['notifications']);
      const previousUnreadCount = queryClient.getQueryData(['notifications', 'unread-count']);

      queryClient.setQueryData(['notifications'], (old: any) => {
        if (!old?.notifications) return old;
        return {
          ...old,
          notifications: old.notifications.map((notif: NotificationType) =>
            notif._id === notificationId ? { ...notif, read: true } : notif
          ),
        };
      });

      queryClient.setQueryData(['notifications', 'unread-count'], (old: number = 0) =>
        Math.max(0, old - 1)
      );

      return { previousNotifications, previousUnreadCount };
    },
    onError: (_, __, context) => {
      if (context?.previousNotifications) {
        queryClient.setQueryData(['notifications'], context.previousNotifications);
      }
      if (context?.previousUnreadCount !== undefined) {
        queryClient.setQueryData(['notifications', 'unread-count'], context.previousUnreadCount);
      }
    },
    onSettled: () => debouncedInvalidateQueries(),
  });

  const markAllAsReadMutation = useMutation({
    mutationFn: async () => NotificationClientService.markAllAsReadClient(),
    onMutate: async () => {
      await queryClient.cancelQueries({ queryKey: ['notifications'] });
      await queryClient.cancelQueries({ queryKey: ['notifications', 'unread-count'] });

      const previousNotifications = queryClient.getQueryData(['notifications']);
      const previousUnreadCount = queryClient.getQueryData(['notifications', 'unread-count']);

      queryClient.setQueryData(['notifications'], (old: any) => {
        if (!old?.notifications) return old;
        return {
          ...old,
          notifications: old.notifications.map((notif: NotificationType) => ({
            ...notif,
            read: true,
          })),
        };
      });

      queryClient.setQueryData(['notifications', 'unread-count'], 0);

      return { previousNotifications, previousUnreadCount };
    },
    onError: (_, __, context) => {
      if (context?.previousNotifications) {
        queryClient.setQueryData(['notifications'], context.previousNotifications);
      }
      if (context?.previousUnreadCount !== undefined) {
        queryClient.setQueryData(['notifications', 'unread-count'], context.previousUnreadCount);
      }
    },
    onSettled: () => debouncedInvalidateQueries(),
  });

  const clearAllNotificationsMutation = useMutation({
    mutationFn: async () => NotificationClientService.clearAllNotificationsClient(),
    onMutate: async () => {
      await queryClient.cancelQueries({ queryKey: ['notifications'] });
      await queryClient.cancelQueries({ queryKey: ['notifications', 'unread-count'] });

      const previousNotifications = queryClient.getQueryData(['notifications']);
      const previousUnreadCount = queryClient.getQueryData(['notifications', 'unread-count']);

      queryClient.setQueryData(['notifications'], { notifications: [] });
      queryClient.setQueryData(['notifications', 'unread-count'], 0);

      return { previousNotifications, previousUnreadCount };
    },
    onError: (_, __, context) => {
      if (context?.previousNotifications) {
        queryClient.setQueryData(['notifications'], context.previousNotifications);
      }
      if (context?.previousUnreadCount !== undefined) {
        queryClient.setQueryData(['notifications', 'unread-count'], context.previousUnreadCount);
      }
    },
    onSettled: () => debouncedInvalidateQueries(),
  });

  const setConnectionStatus = useCallback((status: string) => {
    try {
      if (typeof window !== 'undefined' && window.localStorage) {
        localStorage.setItem(SSE_CONFIG.CONNECTION_STATUS_KEY, status);
      }
    } catch {
      // Ignore localStorage errors
    }
  }, []);

  const getReconnectDelay = useCallback(() => {
    const { reconnectAttempt } = sseRef.current;
    if (reconnectAttempt >= SSE_CONFIG.RECONNECT_DELAYS.length) {
      return SSE_CONFIG.RECONNECT_DELAYS[SSE_CONFIG.RECONNECT_DELAYS.length - 1];
    }
    return SSE_CONFIG.RECONNECT_DELAYS[reconnectAttempt];
  }, []);

  const showBrowserNotification = useCallback((notification: NotificationType) => {
    if (
      typeof window !== 'undefined' &&
      'Notification' in window &&
      Notification.permission === 'granted'
    ) {
      const browserNotification = new Notification(notification.title, {
        body: notification.description,
        icon: '/favicon.ico',
        tag: notification._id,
      });

      setTimeout(() => browserNotification.close(), 5000);
    }
  }, []);

  const startHeartbeatCheck = useCallback(() => {
    if (sseRef.current.heartbeatInterval) {
      clearInterval(sseRef.current.heartbeatInterval);
    }

    sseRef.current.lastHeartbeat = Date.now();
    sseRef.current.heartbeatInterval = setInterval(() => {
      const timeSinceLastHeartbeat = Date.now() - sseRef.current.lastHeartbeat;

      if (timeSinceLastHeartbeat > SSE_CONFIG.HEARTBEAT_TIMEOUT) {
        if (sseRef.current.eventSource) {
          sseRef.current.eventSource.close();
        }
        setIsConnected(false);
        setError('Connection timeout - reconnecting...');
        setConnectionStatus('disconnected');

        if (sseRef.current.reconnectAttempt < SSE_CONFIG.MAX_RECONNECT_ATTEMPTS) {
          const delay = getReconnectDelay();
          sseRef.current.reconnectTimer = setTimeout(connectSSE, delay);
        }
      }
    }, SSE_CONFIG.HEARTBEAT_CHECK_INTERVAL);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [getReconnectDelay, setConnectionStatus]);

  const connectSSE = useCallback(() => {
    if (sseRef.current.reconnectTimer) {
      clearTimeout(sseRef.current.reconnectTimer);
      sseRef.current.reconnectTimer = null;
    }

    if (sseRef.current.eventSource) {
      sseRef.current.eventSource.close();
    }

    if (sseRef.current.reconnectAttempt >= SSE_CONFIG.MAX_RECONNECT_ATTEMPTS) {
      setError('Maximum reconnection attempts exceeded');
      setConnectionStatus('failed');
      return;
    }

    try {
      const url = new URL('/api/notifications/sse', window.location.origin);
      // Remove timestamp parameter to reduce unnecessary reconnections
      if (sseRef.current.lastEventId) {
        url.searchParams.append('lastEventId', sseRef.current.lastEventId);
      }

      const eventSource = new EventSource(url.toString());
      sseRef.current.eventSource = eventSource;

      const connectionTimeout = setTimeout(() => {
        if (eventSource.readyState === EventSource.CONNECTING) {
          eventSource.close();
          setError('Connection timeout');
          setConnectionStatus('timeout');
          sseRef.current.reconnectAttempt++;

          if (sseRef.current.reconnectAttempt < SSE_CONFIG.MAX_RECONNECT_ATTEMPTS) {
            const delay = getReconnectDelay();
            sseRef.current.reconnectTimer = setTimeout(connectSSE, delay);
          }
        }
      }, 30000); // 30 second connection timeout

      eventSource.onopen = () => {
        clearTimeout(connectionTimeout);
        setIsConnected(true);
        setError(null);
        sseRef.current.reconnectAttempt = 0;
        setConnectionStatus('connected');
        startHeartbeatCheck();
      };

      eventSource.onmessage = event => {
        try {
          if (event.lastEventId) {
            sseRef.current.lastEventId = event.lastEventId;
          }

          sseRef.current.lastHeartbeat = Date.now();
          const data = JSON.parse(event.data);

          switch (data.type) {
            case 'heartbeat':
              break;
            case 'connection':
              break;
            case 'notification':
              if (data.notification) {
                showBrowserNotification(data.notification);
                debouncedInvalidateQueries();
              }
              break;
            case 'notifications':
              debouncedInvalidateQueries();
              break;
            default:
              break;
          }
        } catch (parseError) {
          setError('Invalid server response format');
        }
      };

      eventSource.onerror = () => {
        clearTimeout(connectionTimeout);
        setIsConnected(false);
        setError('Connection lost - reconnecting...');
        setConnectionStatus('disconnected');

        eventSource.close();
        sseRef.current.reconnectAttempt++;

        if (sseRef.current.reconnectAttempt < SSE_CONFIG.MAX_RECONNECT_ATTEMPTS) {
          const delay = getReconnectDelay();
          sseRef.current.reconnectTimer = setTimeout(connectSSE, delay);
        }
      };
    } catch (connectionError) {
      setIsConnected(false);
      setError('Failed to establish connection');
      setConnectionStatus('failed');
      sseRef.current.reconnectAttempt++;

      if (sseRef.current.reconnectAttempt < SSE_CONFIG.MAX_RECONNECT_ATTEMPTS) {
        const delay = getReconnectDelay();
        sseRef.current.reconnectTimer = setTimeout(connectSSE, delay);
      }
    }
  }, [
    debouncedInvalidateQueries,
    getReconnectDelay,
    setConnectionStatus,
    showBrowserNotification,
    startHeartbeatCheck,
  ]);

  useEffect(() => {
    if (!session?.user) return;

    connectSSE();

    return () => {
      if (sseRef.current.eventSource) {
        sseRef.current.eventSource.close();
      }
      if (sseRef.current.reconnectTimer) {
        clearTimeout(sseRef.current.reconnectTimer);
      }
      if (sseRef.current.heartbeatInterval) {
        clearInterval(sseRef.current.heartbeatInterval);
      }
      if (sseRef.current.invalidationTimeout) {
        // eslint-disable-next-line react-hooks/exhaustive-deps
        clearTimeout(sseRef.current.invalidationTimeout);
      }
      setConnectionStatus('closed');
    };
  }, [session, connectSSE, setConnectionStatus]);

  const requestNotificationPermission = useCallback(async (): Promise<boolean> => {
    try {
      if (typeof window !== 'undefined' && 'Notification' in window) {
        if (Notification.permission === 'granted') return true;
        const permission = await Notification.requestPermission();
        return permission === 'granted';
      }
      return false;
    } catch {
      return false;
    }
  }, []);

  const markAsRead = useCallback(
    async (notificationId: string): Promise<boolean> => {
      try {
        await markAsReadMutation.mutateAsync(notificationId);
        return true;
      } catch {
        return false;
      }
    },
    [markAsReadMutation]
  );

  const markAllAsRead = useCallback(async (): Promise<boolean> => {
    try {
      await markAllAsReadMutation.mutateAsync();
      return true;
    } catch {
      return false;
    }
  }, [markAllAsReadMutation]);

  const clearAllNotifications = useCallback(async (): Promise<boolean> => {
    try {
      await clearAllNotificationsMutation.mutateAsync();
      return true;
    } catch {
      return false;
    }
  }, [clearAllNotificationsMutation]);

  const fetchNotifications = useCallback(async (page = 0) => {
    try {
      const response = await NotificationClientService.getNotificationsClient(page);
      return {
        notifications: response.notifications || [],
        hasMore: response.pagination
          ? response.pagination.page < response.pagination.pages - 1
          : false,
        nextPage: response.pagination ? response.pagination.page + 1 : undefined,
      };
    } catch {
      setError('Failed to fetch notifications');
      return { notifications: [], hasMore: false };
    }
  }, []);

  const fetchUnreadCount = useCallback(async (): Promise<number> => {
    return unreadCountData || 0;
  }, [unreadCountData]);

  return {
    notifications: notificationsData?.notifications || [],
    unreadCount: unreadCountData || 0,
    isConnected,
    error,
    fetchNotifications,
    fetchUnreadCount,
    markAsRead,
    markAllAsRead,
    clearAllNotifications,
    requestNotificationPermission,
  };
}
