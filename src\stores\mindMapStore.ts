import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import {
  MindMap,
  MindMapNode,
  MindMapConnection,
  MindMapViewport,
  MindMapFilters,
} from '@/types/MindMapsTypes';

interface MindMapState {
  // Current mind map data
  currentMindMap: MindMap | null;
  mindMaps: MindMap[];

  // UI state
  selectedNodes: string[];
  selectedConnections: string[];
  isLoading: boolean;
  error: string | null;

  // Save state
  hasUnsavedChanges: boolean;
  lastSavedData: string | null;

  // Optimistic updates
  pendingOperations: Set<string>;
  optimisticUpdates: Map<string, any>;

  // Editor state
  viewport: MindMapViewport;
  isDragging: boolean;
  isConnecting: boolean;
  connectingFrom: string | null;

  // Filters and search
  filters: MindMapFilters;
  searchResults: MindMap[];

  // History for undo/redo
  history: {
    past: Array<{ nodes: MindMapNode[]; connections: MindMapConnection[] }>;
    present: { nodes: MindMapNode[]; connections: MindMapConnection[] };
    future: Array<{ nodes: MindMapNode[]; connections: MindMapConnection[] }>;
  };

  // Actions
  setCurrentMindMap: (mindMap: MindMap | null) => void;
  setMindMaps: (mindMaps: MindMap[]) => void;
  addMindMap: (mindMap: MindMap) => void;
  updateMindMap: (id: string, updates: Partial<MindMap>) => void;
  deleteMindMap: (id: string) => void;

  // Node actions
  addNode: (node: MindMapNode) => void;
  updateNode: (id: string, updates: Partial<MindMapNode>) => void;
  deleteNode: (id: string) => void;
  selectNode: (id: string, multiSelect?: boolean) => void;
  clearNodeSelection: () => void;

  // Connection actions
  addConnection: (connection: MindMapConnection) => void;
  updateConnection: (id: string, updates: Partial<MindMapConnection>) => void;
  deleteConnection: (id: string) => void;
  selectConnection: (id: string, multiSelect?: boolean) => void;
  clearConnectionSelection: () => void;

  // Viewport actions
  setViewport: (viewport: Partial<MindMapViewport>) => void;
  zoomIn: () => void;
  zoomOut: () => void;
  resetZoom: () => void;
  centerView: () => void;

  // Editor actions
  setDragging: (isDragging: boolean) => void;
  startConnecting: (nodeId: string) => void;
  endConnecting: () => void;

  // History actions
  undo: () => void;
  redo: () => void;
  saveToHistory: () => void;
  clearHistory: () => void;

  // Filter and search actions
  setFilters: (filters: Partial<MindMapFilters>) => void;
  setSearchResults: (results: MindMap[]) => void;
  clearSearch: () => void;

  // Bulk operations
  selectAllNodes: () => void;
  deleteSelectedNodes: () => void;
  duplicateSelectedNodes: () => void;

  // Save state actions
  markAsChanged: () => void;
  markAsSaved: () => void;
  resetSaveState: () => void;

  // Utility actions
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  reset: () => void;

  // Optimistic update actions
  addPendingOperation: (operationId: string) => void;
  removePendingOperation: (operationId: string) => void;
  addOptimisticUpdate: (key: string, value: any) => void;
  removeOptimisticUpdate: (key: string) => void;
}

const initialState = {
  currentMindMap: null,
  mindMaps: [],
  selectedNodes: [],
  selectedConnections: [],
  isLoading: false,
  error: null,
  hasUnsavedChanges: false,
  lastSavedData: null,
  viewport: { x: 0, y: 0, zoom: 1 },
  isDragging: false,
  isConnecting: false,
  connectingFrom: null,
  pendingOperations: new Set<string>(),
  optimisticUpdates: new Map<string, any>(),

  filters: {},
  searchResults: [],
  history: {
    past: [],
    present: { nodes: [], connections: [] },
    future: [],
  },
};

export const useMindMapStore = create<MindMapState>()(
  devtools(
    set => ({
      ...initialState,

      // Mind map actions
      setCurrentMindMap: mindMap =>
        set({
          currentMindMap: mindMap,
          hasUnsavedChanges: false, // Reset unsaved changes when loading a mind map
          lastSavedData: mindMap ? JSON.stringify(mindMap) : null,
        }),

      setMindMaps: mindMaps => set({ mindMaps }),

      addMindMap: mindMap =>
        set(state => ({
          mindMaps: [mindMap, ...state.mindMaps],
        })),

      updateMindMap: (id, updates) =>
        set(state => ({
          mindMaps: state.mindMaps.map(mm => (mm._id === id ? { ...mm, ...updates } : mm)),
          currentMindMap:
            state.currentMindMap?._id === id
              ? { ...state.currentMindMap, ...updates }
              : state.currentMindMap,
        })),

      deleteMindMap: id =>
        set(state => ({
          mindMaps: state.mindMaps.filter(mm => mm._id !== id),
          currentMindMap: state.currentMindMap?._id === id ? null : state.currentMindMap,
        })),

      // Node actions
      addNode: node =>
        set(state => {
          if (!state.currentMindMap) return state;

          // Ensure nodes array exists
          const currentNodes = state.currentMindMap.nodes || [];
          const currentConnections = state.currentMindMap.connections || [];

          const updatedMindMap = {
            ...state.currentMindMap,
            nodes: [...currentNodes, node],
            connections: currentConnections,
          };

          return {
            currentMindMap: updatedMindMap,
            hasUnsavedChanges: true,
            history: {
              past: [...state.history.past, state.history.present],
              present: { nodes: updatedMindMap.nodes, connections: updatedMindMap.connections },
              future: [],
            },
          };
        }),

      updateNode: (id, updates) =>
        set(state => {
          if (!state.currentMindMap) return state;

          const updatedMindMap = {
            ...state.currentMindMap,
            nodes: state.currentMindMap.nodes.map(node =>
              node.id === id ? { ...node, ...updates } : node
            ),
          };

          return {
            currentMindMap: updatedMindMap,
            hasUnsavedChanges: true,
            history: {
              past: [...state.history.past, state.history.present],
              present: { nodes: updatedMindMap.nodes, connections: updatedMindMap.connections },
              future: [],
            },
          };
        }),

      deleteNode: id =>
        set(state => {
          if (!state.currentMindMap) return state;

          const updatedMindMap = {
            ...state.currentMindMap,
            nodes: state.currentMindMap.nodes.filter(node => node.id !== id),
            connections: state.currentMindMap.connections.filter(
              conn => conn.sourceNodeId !== id && conn.targetNodeId !== id
            ),
          };

          return {
            currentMindMap: updatedMindMap,
            selectedNodes: state.selectedNodes.filter(nodeId => nodeId !== id),
            hasUnsavedChanges: true,
            history: {
              past: [...state.history.past, state.history.present],
              present: { nodes: updatedMindMap.nodes, connections: updatedMindMap.connections },
              future: [],
            },
          };
        }),

      selectNode: (id, multiSelect = false) =>
        set(state => ({
          selectedNodes: multiSelect
            ? state.selectedNodes.includes(id)
              ? state.selectedNodes.filter(nodeId => nodeId !== id)
              : [...state.selectedNodes, id]
            : [id],
        })),

      clearNodeSelection: () => set({ selectedNodes: [] }),

      // Connection actions
      addConnection: connection =>
        set(state => {
          if (!state.currentMindMap) return state;

          const updatedMindMap = {
            ...state.currentMindMap,
            connections: [...state.currentMindMap.connections, connection],
          };

          return {
            currentMindMap: updatedMindMap,
            hasUnsavedChanges: true,
            history: {
              past: [...state.history.past, state.history.present],
              present: { nodes: updatedMindMap.nodes, connections: updatedMindMap.connections },
              future: [],
            },
          };
        }),

      updateConnection: (id, updates) =>
        set(state => {
          if (!state.currentMindMap) return state;

          const updatedMindMap = {
            ...state.currentMindMap,
            connections: state.currentMindMap.connections.map(conn =>
              conn.id === id ? { ...conn, ...updates } : conn
            ),
          };

          return {
            currentMindMap: updatedMindMap,
            hasUnsavedChanges: true,
            history: {
              past: [...state.history.past, state.history.present],
              present: { nodes: updatedMindMap.nodes, connections: updatedMindMap.connections },
              future: [],
            },
          };
        }),

      deleteConnection: id =>
        set(state => {
          if (!state.currentMindMap) return state;

          const updatedMindMap = {
            ...state.currentMindMap,
            connections: state.currentMindMap.connections.filter(conn => conn.id !== id),
          };

          return {
            currentMindMap: updatedMindMap,
            selectedConnections: state.selectedConnections.filter(connId => connId !== id),
            hasUnsavedChanges: true,
            history: {
              past: [...state.history.past, state.history.present],
              present: { nodes: updatedMindMap.nodes, connections: updatedMindMap.connections },
              future: [],
            },
          };
        }),

      selectConnection: (id, multiSelect = false) =>
        set(state => ({
          selectedConnections: multiSelect
            ? state.selectedConnections.includes(id)
              ? state.selectedConnections.filter(connId => connId !== id)
              : [...state.selectedConnections, id]
            : [id],
        })),

      clearConnectionSelection: () => set({ selectedConnections: [] }),

      // Viewport actions
      setViewport: viewport =>
        set(state => ({
          viewport: { ...state.viewport, ...viewport },
        })),

      zoomIn: () =>
        set(state => ({
          viewport: { ...state.viewport, zoom: Math.min(state.viewport.zoom * 1.2, 3) },
        })),

      zoomOut: () =>
        set(state => ({
          viewport: { ...state.viewport, zoom: Math.max(state.viewport.zoom / 1.2, 0.1) },
        })),

      resetZoom: () =>
        set(state => ({
          viewport: { ...state.viewport, zoom: 1 },
        })),

      centerView: () =>
        set(state => ({
          viewport: { ...state.viewport, x: 0, y: 0 },
        })),

      // Editor actions
      setDragging: isDragging => set({ isDragging }),

      startConnecting: nodeId =>
        set({
          isConnecting: true,
          connectingFrom: nodeId,
        }),

      endConnecting: () =>
        set({
          isConnecting: false,
          connectingFrom: null,
        }),

      // History actions
      undo: () =>
        set(state => {
          if (state.history.past.length === 0) return state;

          const previous = state.history.past[state.history.past.length - 1];
          const newPast = state.history.past.slice(0, state.history.past.length - 1);

          if (!state.currentMindMap) return state;

          return {
            currentMindMap: {
              ...state.currentMindMap,
              nodes: previous.nodes,
              connections: previous.connections,
            },
            history: {
              past: newPast,
              present: previous,
              future: [state.history.present, ...state.history.future],
            },
          };
        }),

      redo: () =>
        set(state => {
          if (state.history.future.length === 0) return state;

          const next = state.history.future[0];
          const newFuture = state.history.future.slice(1);

          if (!state.currentMindMap) return state;

          return {
            currentMindMap: {
              ...state.currentMindMap,
              nodes: next.nodes,
              connections: next.connections,
            },
            history: {
              past: [...state.history.past, state.history.present],
              present: next,
              future: newFuture,
            },
          };
        }),

      // Bulk operations
      selectAllNodes: () =>
        set(state => {
          if (!state.currentMindMap) return state;
          return {
            selectedNodes: state.currentMindMap.nodes.map(node => node.id),
          };
        }),

      deleteSelectedNodes: () =>
        set(state => {
          if (!state.currentMindMap || state.selectedNodes.length === 0) return state;

          const updatedMindMap = {
            ...state.currentMindMap,
            nodes: state.currentMindMap.nodes.filter(
              node => !state.selectedNodes.includes(node.id)
            ),
            connections: state.currentMindMap.connections.filter(
              conn =>
                !state.selectedNodes.includes(conn.sourceNodeId) &&
                !state.selectedNodes.includes(conn.targetNodeId)
            ),
          };

          return {
            currentMindMap: updatedMindMap,
            selectedNodes: [],
            history: {
              past: [...state.history.past, state.history.present],
              present: { nodes: updatedMindMap.nodes, connections: updatedMindMap.connections },
              future: [],
            },
          };
        }),

      duplicateSelectedNodes: () =>
        set(state => {
          if (!state.currentMindMap || state.selectedNodes.length === 0) return state;

          const nodesToDuplicate = state.currentMindMap.nodes.filter(node =>
            state.selectedNodes.includes(node.id)
          );
          const duplicatedNodes = nodesToDuplicate.map(node => ({
            ...node,
            id: `${node.id}-copy-${Date.now()}`,
            position: {
              x: node.position.x + 50,
              y: node.position.y + 50,
            },
          }));

          const updatedMindMap = {
            ...state.currentMindMap,
            nodes: [...state.currentMindMap.nodes, ...duplicatedNodes],
          };

          return {
            currentMindMap: updatedMindMap,
            selectedNodes: duplicatedNodes.map(node => node.id),
            history: {
              past: [...state.history.past, state.history.present],
              present: { nodes: updatedMindMap.nodes, connections: updatedMindMap.connections },
              future: [],
            },
          };
        }),

      saveToHistory: () =>
        set(state => {
          if (!state.currentMindMap) return state;

          return {
            history: {
              past: [...state.history.past, state.history.present],
              present: {
                nodes: state.currentMindMap.nodes,
                connections: state.currentMindMap.connections,
              },
              future: [],
            },
          };
        }),

      clearHistory: () =>
        set(state => ({
          history: {
            past: [],
            present: state.currentMindMap
              ? { nodes: state.currentMindMap.nodes, connections: state.currentMindMap.connections }
              : { nodes: [], connections: [] },
            future: [],
          },
        })),

      // Filter and search actions
      setFilters: filters =>
        set(state => ({
          filters: { ...state.filters, ...filters },
        })),

      setSearchResults: results => set({ searchResults: results }),

      clearSearch: () => set({ searchResults: [], filters: {} }),

      // Utility actions
      setLoading: loading => set({ isLoading: loading }),

      setError: error => set({ error }),

      reset: () => set(initialState),

      // Save state actions
      markAsChanged: () => set({ hasUnsavedChanges: true }),

      markAsSaved: () =>
        set(state => {
          if (!state.currentMindMap) return state;

          const currentData = JSON.stringify({
            nodes: state.currentMindMap.nodes,
            connections: state.currentMindMap.connections,
            viewport: state.currentMindMap.viewport,
          });

          return {
            hasUnsavedChanges: false,
            lastSavedData: currentData,
          };
        }),

      resetSaveState: () =>
        set({
          hasUnsavedChanges: false,
          lastSavedData: null,
        }),

      // Optimistic update actions
      addPendingOperation: operationId =>
        set(state => ({
          pendingOperations: new Set([...state.pendingOperations, operationId]),
        })),

      removePendingOperation: operationId =>
        set(state => {
          const newPendingOperations = new Set(state.pendingOperations);
          newPendingOperations.delete(operationId);
          return { pendingOperations: newPendingOperations };
        }),

      addOptimisticUpdate: (key, value) =>
        set(state => ({
          optimisticUpdates: new Map(state.optimisticUpdates).set(key, value),
        })),

      removeOptimisticUpdate: key =>
        set(state => {
          const newOptimisticUpdates = new Map(state.optimisticUpdates);
          newOptimisticUpdates.delete(key);
          return { optimisticUpdates: newOptimisticUpdates };
        }),
    }),
    {
      name: 'mind-map-store',
    }
  )
);
