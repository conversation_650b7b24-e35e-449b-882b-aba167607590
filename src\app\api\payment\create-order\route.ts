import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/options';
import { connectDB } from '@/Utility/db';
import { User } from '@/models/User';
import { StorageService } from '../../storage/[[...route]]/route';

let razorpay: any = null;

const getRazorpay = async () => {
  if (!razorpay) {
    const Razorpay = (await import('razorpay')).default;
    razorpay = new Razorpay({
      key_id: process.env.RAZORPAY_KEY_ID!,
      key_secret: process.env.RAZORPAY_KEY_SECRET!,
    });
  }
  return razorpay;
};

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await connectDB();
    const user = await User.findOne({ email: session.user.email });
    if (!user || !user.organizationId) {
      return NextResponse.json({ error: 'User or organization not found' }, { status: 404 });
    }

    const { storageMB } = await request.json();

    if (!storageMB || storageMB <= 0) {
      return NextResponse.json({ error: 'Invalid storage amount' }, { status: 400 });
    }

    // Calculate price
    const priceInfo = StorageService.calculateStoragePrice(storageMB);

    // Create order data
    const orderData = {
      amount: priceInfo.totalPrice * 100, // Convert to paise
      currency: 'INR',
      receipt: `storage_${user.organizationId}_${Date.now()}`,
      notes: {
        storageMB: storageMB.toString(),
        organizationId: user.organizationId.toString(),
        userId: user._id.toString(),
        type: 'storage_purchase',
      },
    };

    // Create Razorpay order
    const razorpayInstance = await getRazorpay();
    const order = await razorpayInstance.orders.create(orderData);

    return NextResponse.json({
      success: true,
      order,
      priceInfo,
      user: {
        name: user.name,
        email: user.email,
      },
    });
  } catch (error: any) {
    return NextResponse.json({ error: error.message || 'Failed to create order' }, { status: 500 });
  }
}
