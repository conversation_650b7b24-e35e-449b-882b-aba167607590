'use client';
import { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Save } from 'lucide-react';

interface AnnotationNodeData {
  title: string;
  content: string;
}

interface AnnotationNodeFormProps {
  data: AnnotationNodeData;
  onSave: (d: Partial<AnnotationNodeData>) => void;
  onCancel: () => void;
}

export function AnnotationNodeForm({ data, onSave, onCancel }: AnnotationNodeFormProps) {
  const [formData, setFormData] = useState<AnnotationNodeData>(data);

  return (
    <Card className="min-w-[320px] p-4 bg-white shadow-lg">
      <form
        className="space-y-4"
        onSubmit={e => {
          e.preventDefault();
          onSave(formData);
        }}
      >
        <div>
          <Label htmlFor="title">Title</Label>
          <Input
            id="title"
            value={formData.title}
            onChange={e => setFormData({ ...formData, title: e.target.value })}
          />
        </div>

        <div>
          <Label htmlFor="content">Markdown Content</Label>
          <Textarea
            id="content"
            rows={8}
            value={formData.content}
            onChange={e => setFormData({ ...formData, content: e.target.value })}
          />
        </div>

        <div className="flex justify-end gap-2">
          <Button variant="outline" type="button" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="submit">
            <Save className="h-4 w-4 mr-2" />
            Save
          </Button>
        </div>
      </form>
    </Card>
  );
}
