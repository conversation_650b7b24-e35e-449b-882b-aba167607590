import React from 'react';
import Modal from '../Global/Modal';
import { Button } from '../ui/button';
import { Globe } from 'lucide-react';
import { FcSynchronize } from 'react-icons/fc';
import { useToast } from '@/hooks/use-toast';
import { useQueryClient } from '@tanstack/react-query';
const GoogleCalendarSyncModal = ({
  showGoogleSync,
  setShowGoogleSync,
  googleCalendarConnected,
  handleGoogleCalendarSync,
  userId,
  syncGoogleCalendarMutation,
}) => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  return (
    <Modal isOpen={showGoogleSync} onClose={() => setShowGoogleSync(false)}>
      <div>
        <div>
          <p>Google Calendar Integration</p>
        </div>

        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <div
              className={`w-3 h-3 rounded-full ${googleCalendarConnected ? 'bg-green-500' : 'bg-red-500'}`}
            />
            <span>
              {googleCalendarConnected
                ? 'Connected to Google Calendar'
                : 'Not connected to Google Calendar'}
            </span>
          </div>

          {!googleCalendarConnected ? (
            <div>
              <p className="text-sm text-gray-600 mb-4">
                Connect your Google Calendar to sync events automatically.
              </p>
              <Button
                onClick={async () => {
                  window.location.href = `/api/google-calendar/auth?userId=${userId}`;
                }}
                className="w-full"
              >
                <Globe className="w-4 h-4 mr-2" />
                Connect Google Calendar
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              <p className="text-sm text-gray-600">
                Your Google Calendar is connected. You can sync events or disconnect.
              </p>

              <div className="flex space-x-2">
                <Button
                  onClick={handleGoogleCalendarSync}
                  disabled={syncGoogleCalendarMutation.isPending}
                  className="flex-1"
                >
                  <FcSynchronize
                    className={`w-4 h-4 mr-2 ${syncGoogleCalendarMutation.isPending ? 'animate-spin' : ''}`}
                  />
                  Sync Now
                </Button>

                <Button
                  variant="outline"
                  onClick={async () => {
                    try {
                      await fetch(`/api/google-calendar/disconnect?userId=${userId}`, {
                        method: 'POST',
                      });
                      queryClient.invalidateQueries({ queryKey: ['google-calendar-status'] });
                      toast({
                        title: 'Disconnected',
                        description: 'Google Calendar has been disconnected.',
                      });
                    } catch (error) {
                      toast({
                        title: 'Error',
                        description: 'Failed to disconnect Google Calendar.',
                        variant: 'destructive',
                      });
                    }
                  }}
                >
                  Disconnect
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>
    </Modal>
  );
};

export default GoogleCalendarSyncModal;
