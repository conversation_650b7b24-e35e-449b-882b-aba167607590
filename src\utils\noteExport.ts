import { toast } from 'sonner';

export interface ExportNote {
  _id: string;
  title: string;
  content: string;
  category: string;
  tags: string[];
  createdAt: string;
  updatedAt: string;
}

export class NoteExportUtils {
  /**
   * Export note as PDF using jsPDF and html2canvas with improved formatting
   */
  static async exportToPDF(note: ExportNote): Promise<void> {
    try {
      const jsPDF = (await import('jspdf')).default;
      const html2canvas = (await import('html2canvas')).default;

      const tempDiv = document.createElement('div');
      tempDiv.style.position = 'absolute';
      tempDiv.style.left = '-9999px';
      tempDiv.style.width = '794px';
      tempDiv.style.minHeight = '1123px';
      tempDiv.style.padding = '40px 40px';
      tempDiv.style.backgroundColor = 'white';
      tempDiv.style.color = 'black';
      tempDiv.style.fontFamily =
        '-apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif';
      tempDiv.style.fontSize = '14px';
      tempDiv.style.lineHeight = '1.6';
      tempDiv.style.boxSizing = 'border-box';

      const htmlContent = await this.generatePDFContentWithImages(note);
      tempDiv.innerHTML = htmlContent;

      document.body.appendChild(tempDiv);

      // Wait for images to load with improved handling
      await this.waitForImagesToLoad(tempDiv);

      const canvas = await html2canvas(tempDiv, {
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff',
        scrollX: 0,
        scrollY: 0,
        windowWidth: 794,
        windowHeight: 1123,
        imageTimeout: 30000,
        removeContainer: false,
        logging: false,
        onclone: clonedDoc => {
          // Ensure all images in cloned document are properly loaded
          const clonedImages = clonedDoc.querySelectorAll('img');
          clonedImages.forEach(img => {
            if (img.src && !img.complete) {
              img.style.display = 'none';
            }
          });
        },
      });

      document.body.removeChild(tempDiv);

      const pdf = new jsPDF('p', 'mm', 'a4');
      const imgWidth = 210;
      const pageHeight = 297;
      const imgHeight = (canvas.height * imgWidth) / canvas.width;
      let heightLeft = imgHeight;
      let position = 0;

      // Add first page
      pdf.addImage(canvas.toDataURL('image/jpeg', 0.95), 'JPEG', 0, position, imgWidth, imgHeight);
      heightLeft -= pageHeight;

      // Add additional pages if needed
      while (heightLeft >= 0) {
        position = heightLeft - imgHeight;
        pdf.addPage();
        pdf.addImage(
          canvas.toDataURL('image/jpeg', 0.95),
          'JPEG',
          0,
          position,
          imgWidth,
          imgHeight
        );
        heightLeft -= pageHeight;
      }

      const fileName = `${this.sanitizeFileName(note.title)}.pdf`;
      pdf.save(fileName);

      toast.success('PDF exported successfully', {
        description: 'Your note has been saved as a PDF file',
      });
    } catch (error) {
      toast.error('Failed to export PDF', {
        description: error instanceof Error ? error.message : 'An unexpected error occurred',
      });
      throw error;
    }
  }

  private static async waitForImagesToLoad(container: HTMLElement): Promise<void> {
    const images = container.querySelectorAll('img');

    if (images.length === 0) {
      return Promise.resolve();
    }

    const imagePromises = Array.from(images).map(img => {
      return new Promise<void>(resolve => {
        if (img.complete && img.naturalHeight !== 0) {
          resolve();
        } else {
          let resolved = false;

          const resolveOnce = () => {
            if (!resolved) {
              resolved = true;
              resolve();
            }
          };

          img.onload = resolveOnce;
          img.onerror = () => {
            img.style.display = 'none';
            resolveOnce();
          };

          setTimeout(resolveOnce, 15000);
        }
      });
    });

    await Promise.all(imagePromises);

    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  private static async generatePDFContentWithImages(note: ExportNote): Promise<string> {
    const processedContent = await this.processContentWithImages(note.content);

    return `
      <div style="margin-bottom: 30px; border-bottom: 3px solid #2563eb; padding-bottom: 20px;">
        <h1 style="margin: 0 0 15px 0; font-size: 32px; color: #1e293b; font-weight: 700; line-height: 1.2;">
          ${this.escapeHtml(note.title)}
        </h1>
        <div style="display: flex; flex-wrap: wrap; gap: 20px; margin: 15px 0; padding: 15px; background: #f8fafc; border-radius: 8px; border-left: 4px solid #2563eb;">
          <div style="font-size: 13px; color: #64748b;">
            <strong style="color: #334155;">Category:</strong> ${this.escapeHtml(note.category)}
          </div>
          <div style="font-size: 13px; color: #64748b;">
            <strong style="color: #334155;">Created:</strong> ${new Date(note.createdAt).toLocaleDateString()}
          </div>
          <div style="font-size: 13px; color: #64748b;">
            <strong style="color: #334155;">Updated:</strong> ${new Date(note.updatedAt).toLocaleDateString()}
          </div>
        </div>
        ${
          note.tags.length > 0
            ? `
          <div style="margin-top: 15px;">
            ${note.tags.map(tag => `<span style="display: inline-block; background: #e2e8f0; color: #475569; padding: 4px 12px; border-radius: 20px; font-size: 12px; margin: 2px 4px 2px 0; font-weight: 500;">${this.escapeHtml(tag)}</span>`).join('')}
          </div>
        `
            : ''
        }
      </div>
      <div style="line-height: 1.7; font-size: 15px; color: #374151;">
        ${processedContent}
      </div>
    `;
  }

  private static async processContentWithImages(content: string): Promise<string> {
    const imageRegex =
      /!\[([^\]]*)\]\(([^)]+)\)|<img[^>]+src=["']([^"']+)["'][^>]*>|https?:\/\/[^\s<>"]+\.(jpg|jpeg|png|gif|webp|svg|bmp)(\?[^\s<>"]*)?/gi;

    let processedContent = content;
    const matches = Array.from(content.matchAll(imageRegex));

    for (const match of matches) {
      try {
        let imageUrl: string;

        if (match[0].startsWith('![')) {
          imageUrl = match[2];
        } else if (match[0].startsWith('<img')) {
          imageUrl = match[3];
        } else {
          imageUrl = match[0];
        }

        imageUrl = imageUrl.trim();

        const base64Image = await this.imageToBase64WithFallbacks(imageUrl);

        if (base64Image) {
          const imageHtml = `
            <div style="margin: 20px 0; text-align: center; page-break-inside: avoid;">
              <img src="${base64Image}" />
            </div>
          `;
          processedContent = processedContent.replace(match[0], imageHtml);
        } else {
          const placeholderHtml = ``;
          processedContent = processedContent.replace(match[0], placeholderHtml);
        }
      } catch (error) {
        // skip
      }
    }
    return this.processMarkdownToPDF(processedContent);
  }

  private static async imageToBase64WithFallbacks(url: string): Promise<string | null> {
    const fallbackStrategies = [
      () => this.directImageFetch(url),
      () => this.canvasImageConversion(url),
    ];

    for (const strategy of fallbackStrategies) {
      try {
        const result = await strategy();
        if (result) {
          return result;
        }
      } catch (error) {
        continue;
      }
    }

    return null;
  }
  private static async directImageFetch(url: string): Promise<string | null> {
    try {
      const response = await fetch(url, {
        mode: 'cors',
        credentials: 'omit',
      });

      if (!response.ok) throw new Error(`HTTP ${response.status}`);

      const blob = await response.blob();
      return this.blobToBase64(blob);
    } catch (error) {
      throw new Error(`Direct fetch failed: ${error}`);
    }
  }

  private static async canvasImageConversion(url: string): Promise<string | null> {
    return new Promise(resolve => {
      const img = new Image();
      img.crossOrigin = 'anonymous';

      img.onload = () => {
        try {
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');

          if (!ctx) {
            resolve(null);
            return;
          }

          canvas.width = img.naturalWidth;
          canvas.height = img.naturalHeight;

          ctx.drawImage(img, 0, 0);

          const dataURL = canvas.toDataURL('image/jpeg', 0.8);
          resolve(dataURL);
        } catch (error) {
          toast.error('Canvas conversion failed');
          resolve(null);
        }
      };

      img.onerror = () => resolve(null);

      // Set timeout
      setTimeout(() => resolve(null), 10000);

      img.src = url;
    });
  }

  private static async blobToBase64(blob: Blob): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onloadend = () => resolve(reader.result as string);
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  }

  private static processMarkdownToPDF(content: string): string {
    return content
      .split('\n')
      .map((line: string) => {
        // Headers
        if (line.startsWith('### '))
          return `<h3 style="font-size: 20px; margin: 25px 0 12px 0; color: #1e293b; font-weight: 600; border-bottom: 2px solid #e2e8f0; padding-bottom: 8px;">${this.escapeHtml(line.substring(4))}</h3>`;
        if (line.startsWith('## '))
          return `<h2 style="font-size: 24px; margin: 30px 0 15px 0; color: #1e293b; font-weight: 700; border-bottom: 2px solid #cbd5e1; padding-bottom: 10px;">${this.escapeHtml(line.substring(3))}</h2>`;
        if (line.startsWith('# '))
          return `<h1 style="font-size: 28px; margin: 35px 0 18px 0; color: #0f172a; font-weight: 800;">${this.escapeHtml(line.substring(2))}</h1>`;

        // Lists
        if (line.match(/^\s*[-*+]\s+/))
          return `<li style="margin: 8px 0; padding-left: 5px;">${this.processInlineFormatting(line.replace(/^\s*[-*+]\s+/, ''))}</li>`;
        if (line.match(/^\s*\d+\.\s+/))
          return `<li style="margin: 8px 0; padding-left: 5px;">${this.processInlineFormatting(line.replace(/^\s*\d+\.\s+/, ''))}</li>`;

        // Code blocks
        if (line.startsWith('```'))
          return line.endsWith('```')
            ? ''
            : '<pre style="background: #f1f5f9; padding: 20px; border-radius: 8px; border-left: 4px solid #3b82f6; margin: 20px 0; overflow-x: auto; font-family: \'Courier New\', monospace; font-size: 13px; line-height: 1.5;">';
        if (line === '```') return '</pre>';

        // Blockquotes
        if (line.startsWith('> '))
          return `<blockquote style="margin: 20px 0; padding: 15px 20px; border-left: 4px solid #3b82f6; background: #f8fafc; font-style: italic; color: #475569;">${this.processInlineFormatting(line.substring(2))}</blockquote>`;

        // Empty lines
        if (line.trim() === '') return '<br>';

        // Regular paragraphs
        return `<p style="margin: 12px 0; text-align: justify;">${this.processInlineFormatting(line)}</p>`;
      })
      .join('');
  }

  private static processInlineFormatting(text: string): string {
    return text
      .replace(/\*\*(.*?)\*\*/g, '<strong style="font-weight: 700; color: #1f2937;">$1</strong>')
      .replace(/\*(.*?)\*/g, '<em style="font-style: italic; color: #374151;">$1</em>')
      .replace(
        /`(.*?)`/g,
        '<code style="background: #e5e7eb; padding: 2px 6px; border-radius: 4px; font-family: \'Courier New\', monospace; font-size: 13px; color: #dc2626;">$1</code>'
      )
      .replace(
        /\[([^\]]+)\]\(([^)]+)\)/g,
        '<a href="$2" style="color: #2563eb; text-decoration: underline;">$1</a>'
      );
  }

  static async exportToMarkdown(note: ExportNote): Promise<void> {
    try {
      const markdownContent = this.generateMarkdownContent(note);
      const blob = new Blob([markdownContent], { type: 'text/markdown;charset=utf-8' });
      this.downloadBlob(blob, `${this.sanitizeFileName(note.title)}.md`);

      toast.success('Markdown exported successfully', {
        description: 'Your note has been saved as a Markdown file',
      });
    } catch (error) {
      toast.error('Failed to export Markdown', {
        description: 'An error occurred while exporting the file',
      });
      throw error;
    }
  }

  static async exportToText(note: ExportNote): Promise<void> {
    try {
      const textContent = this.generateTextContent(note);
      const blob = new Blob([textContent], { type: 'text/plain;charset=utf-8' });
      this.downloadBlob(blob, `${this.sanitizeFileName(note.title)}.txt`);

      toast.success('Text file exported successfully', {
        description: 'Your note has been saved as a plain text file',
      });
    } catch (error) {
      toast.error('Failed to export text file', {
        description: 'An error occurred while exporting the file',
      });
      throw error;
    }
  }

  private static generateMarkdownContent(note: ExportNote): string {
    const header = `# ${note.title}\n\n`;
    const metadata = `**Category:** ${note.category}  \n**Created:** ${new Date(note.createdAt).toLocaleDateString()}  \n**Updated:** ${new Date(note.updatedAt).toLocaleDateString()}  \n`;
    const tags = note.tags.length > 0 ? `**Tags:** ${note.tags.join(', ')}  \n` : '';
    const separator = '\n---\n\n';

    return header + metadata + tags + separator + note.content;
  }

  private static generateTextContent(note: ExportNote): string {
    const header = `${note.title}\n${'='.repeat(note.title.length)}\n\n`;
    const metadata = `Category: ${note.category}\nCreated: ${new Date(note.createdAt).toLocaleDateString()}\nUpdated: ${new Date(note.updatedAt).toLocaleDateString()}\n`;
    const tags = note.tags.length > 0 ? `Tags: ${note.tags.join(', ')}\n` : '';
    const separator = '\n' + '-'.repeat(50) + '\n\n';

    // Convert markdown to plain text with better formatting
    const plainContent = note.content
      .replace(/^#{1,6}\s+/gm, '') // Remove markdown headers
      .replace(/\*\*(.*?)\*\*/g, '$1') // Remove bold formatting
      .replace(/\*(.*?)\*/g, '$1') // Remove italic formatting
      .replace(/`(.*?)`/g, '$1') // Remove code formatting
      .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '$1 ($2)') // Convert links to text with URL
      .replace(/!\[([^\]]*)\]\(([^)]+)\)/g, '[Image: $1] ($2)'); // Convert images to text

    return header + metadata + tags + separator + plainContent;
  }

  private static downloadBlob(blob: Blob, filename: string): void {
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    URL.revokeObjectURL(url);
    document.body.removeChild(a);
  }

  private static sanitizeFileName(filename: string): string {
    return filename
      .replace(/[^a-z0-9\s\-_]/gi, '')
      .replace(/\s+/g, '_')
      .toLowerCase()
      .slice(0, 100);
  }

  private static escapeHtml(text: string): string {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  static async exportNote(note: ExportNote, format: string): Promise<void> {
    switch (format) {
      case 'pdf':
        return this.exportToPDF(note);
      case 'markdown':
        return this.exportToMarkdown(note);
      case 'text':
        return this.exportToText(note);
      default:
        throw new Error(`Unsupported export format: ${format}`);
    }
  }
}
