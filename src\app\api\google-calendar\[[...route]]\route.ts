import { Hono } from 'hono';
import { handle } from 'hono/vercel';
import { logger } from 'hono/logger';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/options';
import { Integration } from '@/models/Integration';
import { ActivityLog } from '@/models/ActivityLog';
import { google } from 'googleapis';
import { OAuth2Client } from 'google-auth-library';
import { connectDB } from '@/Utility/db';

const GOOGLE_CLIENT_ID = process.env.GOOGLE_CLIENT_ID;
const GOOGLE_CLIENT_SECRET = process.env.GOOGLE_CLIENT_SECRET;
const GOOGLE_REDIRECT_URI =
  process.env.GOOGLE_REDIRECT_URI || `${process.env.NEXTAUTH_URL}/api/auth/callback/google`;

type Variables = {
  user?: {
    id: string;
    name?: string;
    email?: string;
    image?: string;
    organizationId?: string;
  };
};

const app = new Hono<{ Variables: Variables }>().basePath('/api/google-calendar');

app.use('*', logger());

// Middleware to inject user details
app.use('*', async (c, next) => {
  try {
    const session = await getServerSession(authOptions);
    if (session?.user) {
      const userData = {
        id: session.user.id || '',
        name: session.user.name || '',
        email: session.user.email || '',
        image: session.user.image || '',
        organizationId: session.user.organizationId || '',
      };
      c.set('user', userData);
    }
  } catch (error: any) {
    throw new Error(error.message);
  }
  await next();
});

// Helper functions (adapted for Hono context)
async function createOAuth2Client(): Promise<OAuth2Client> {
  return new google.auth.OAuth2(GOOGLE_CLIENT_ID, GOOGLE_CLIENT_SECRET, GOOGLE_REDIRECT_URI);
}

async function getCalendarClient(userId: string) {
  await connectDB();
  const integration = await Integration.findOne({
    userId,
    provider: 'google-calendar',
    isActive: true,
  });
  if (!integration || !integration.accessToken) return null;
  const oauth2Client = await createOAuth2Client();
  if (integration.expiresAt && new Date() >= integration.expiresAt) {
    if (!integration.refreshToken) return null;
    oauth2Client.setCredentials({ refresh_token: integration.refreshToken });
    try {
      const { credentials } = await oauth2Client.refreshAccessToken();
      if (credentials.access_token) {
        await Integration.findOneAndUpdate(
          { userId, provider: 'google-calendar' },
          {
            accessToken: credentials.access_token,
            expiresAt: credentials.expiry_date ? new Date(credentials.expiry_date) : undefined,
          }
        );
      }
    } catch (error) {
      return null;
    }
  }
  oauth2Client.setCredentials({
    access_token: integration.accessToken,
    refresh_token: integration.refreshToken,
  });
  const calendar = google.calendar({ version: 'v3', auth: oauth2Client });
  return { calendar, oauth2Client };
}

// Route handlers
app.get('/auth', async c => {
  const user = c.get('user');
  if (!user) return c.json({ error: 'Unauthorized' }, 401);
  const oauth2Client = await createOAuth2Client();
  const scopes = [
    'https://www.googleapis.com/auth/calendar.events',
    'https://www.googleapis.com/auth/userinfo.email',
    'https://www.googleapis.com/auth/userinfo.profile',
  ];
  const authUrl = oauth2Client.generateAuthUrl({
    access_type: 'offline',
    scope: scopes,
    state: user.id,
    prompt: 'consent',
  });
  return c.json({ success: true, authUrl });
});

app.get('/callback', async c => {
  const code = c.req.query('code');
  const state = c.req.query('state');
  const error = c.req.query('error');
  if (error) return c.json({ error: 'Authentication failed', details: error }, 400);
  if (!code || !state) return c.json({ error: 'Missing authorization code or state' }, 400);
  const oauth2Client = await createOAuth2Client();
  const response = await oauth2Client.getToken(code);
  const tokens = response.tokens;
  if (!tokens.access_token) return c.json({ error: 'No access token received' }, 400);
  oauth2Client.setCredentials(tokens);
  const oauth2 = google.oauth2({ version: 'v2', auth: oauth2Client });
  const userInfo = await oauth2.userinfo.get();
  const integrationData = {
    userId: state,
    provider: 'google-calendar',
    providerUserId: userInfo.data.id,
    accessToken: tokens.access_token,
    refreshToken: tokens.refresh_token,
    expiresAt: tokens.expiry_date ? new Date(tokens.expiry_date) : undefined,
    scopes: ['https://www.googleapis.com/auth/calendar.events'],
    metadata: {
      email: userInfo.data.email,
      name: userInfo.data.name,
      picture: userInfo.data.picture,
    },
    lastSyncedAt: new Date(),
    isActive: true,
  };
  await Integration.findOneAndUpdate(
    { userId: state, provider: 'google-calendar' },
    integrationData,
    { upsert: true, new: true }
  );
  await ActivityLog.create({
    userId: state,
    action: 'create',
    resourceType: 'integration',
    description: 'Connected Google Calendar integration',
    metadata: { provider: 'google-calendar', userEmail: userInfo.data.email },
  });
  return c.json({ success: true, message: 'Google Calendar connected successfully' });
});

app.get('/status', async c => {
  const user = c.get('user');
  if (!user) return c.json({ connected: false, error: 'No integration found' });
  const integration = await Integration.findOne({
    userId: user.id,
    provider: 'google-calendar',
    isActive: true,
  });
  if (!integration) return c.json({ connected: false, error: 'No integration found' });
  const client = await getCalendarClient(user.id);
  if (!client) {
    return c.json({
      connected: false,
      error: 'Authentication failed',
      email: integration.metadata?.email,
      name: integration.metadata?.name,
      lastSync: integration.lastSyncedAt,
    });
  }
  return c.json({
    connected: true,
    email: integration.metadata?.email,
    name: integration.metadata?.name,
    lastSync: integration.lastSyncedAt,
  });
});

app.get('/calendars', async c => {
  const user = c.get('user');
  if (!user) return c.json({ error: 'Unauthorized' }, 401);
  const client = await getCalendarClient(user.id);
  if (!client) return c.json({ error: 'Google Calendar not connected' }, 401);
  const response = await client.calendar.calendarList.list();
  const calendars = response.data.items || [];
  return c.json({
    success: true,
    calendars: calendars.map(cal => ({
      id: cal.id,
      summary: cal.summary,
      description: cal.description,
      primary: cal.primary,
      backgroundColor: cal.backgroundColor,
      foregroundColor: cal.foregroundColor,
      accessRole: cal.accessRole,
      timeZone: cal.timeZone,
    })),
  });
});

app.get('/events', async c => {
  const user = c.get('user');
  if (!user) return c.json({ error: 'Unauthorized' }, 401);
  const client = await getCalendarClient(user.id);
  if (!client) return c.json({ error: 'Google Calendar not connected' }, 401);
  const calendarId = c.req.query('calendarId') || 'primary';
  const timeMin = c.req.query('timeMin');
  const timeMax = c.req.query('timeMax');
  const maxResults = parseInt(c.req.query('maxResults') || '250');
  const response = await client.calendar.events.list({
    calendarId,
    timeMin: timeMin || undefined,
    timeMax: timeMax || undefined,
    maxResults,
    orderBy: 'startTime',
    singleEvents: true,
  });
  const events = response.data.items || [];
  return c.json({
    success: true,
    events: events.map(event => ({
      id: event.id,
      summary: event.summary,
      description: event.description,
      start: event.start,
      end: event.end,
      location: event.location,
      attendees: event.attendees,
      reminders: event.reminders,
      colorId: event.colorId,
      visibility: event.visibility,
      htmlLink: event.htmlLink,
      created: event.created,
      updated: event.updated,
      extendedProperties: event.extendedProperties,
    })),
  });
});

app.get('/sync-status', async c => {
  const user = c.get('user');
  if (!user) return c.json({ connected: false, lastSync: null, syncedEvents: 0 });
  const integration = await Integration.findOne({
    userId: user.id,
    provider: 'google-calendar',
    isActive: true,
  });
  if (!integration) return c.json({ connected: false, lastSync: null, syncedEvents: 0 });
  const syncedEvents = await Integration.countDocuments({
    userId: user.id,
    'googleCalendarSync.googleEventId': { $exists: true },
  });
  return c.json({ connected: true, lastSync: integration.lastSyncedAt, syncedEvents });
});

app.post('/events', async c => {
  const user = c.get('user');
  if (!user) return c.json({ error: 'Unauthorized' }, 401);
  const client = await getCalendarClient(user.id);
  if (!client) return c.json({ error: 'Google Calendar not connected' }, 401);
  const eventData = await c.req.json();
  const calendarId = eventData.calendarId || 'primary';
  const event = {
    summary: eventData.summary,
    description: eventData.description,
    start: eventData.start,
    end: eventData.end,
    location: eventData.location,
    attendees: eventData.attendees,
    reminders: eventData.reminders,
    colorId: eventData.colorId,
    visibility: eventData.visibility,
    extendedProperties: {
      ...eventData.extendedProperties,
      private: {
        ...eventData.extendedProperties?.private,
        source: 'taskmantra',
        created_by: user.id,
      },
    },
  };
  const response = await client.calendar.events.insert({ calendarId, requestBody: event });
  await ActivityLog.create({
    userId: user.id,
    action: 'create',
    resourceType: 'integration',
    description: `Created Google Calendar event: ${eventData.summary}`,
    metadata: {
      provider: 'google-calendar',
      eventId: response.data.id,
      eventTitle: eventData.summary,
    },
  });
  return c.json({ success: true, event: response.data, message: 'Event created successfully' });
});

app.put('/events/:eventId', async c => {
  const user = c.get('user');
  if (!user) return c.json({ error: 'Unauthorized' }, 401);
  const client = await getCalendarClient(user.id);
  if (!client) return c.json({ error: 'Google Calendar not connected' }, 401);
  const eventId = c.req.param('eventId');
  const updates = await c.req.json();
  const calendarId = updates.calendarId || 'primary';
  const response = await client.calendar.events.update({
    calendarId,
    eventId,
    requestBody: updates,
  });
  await ActivityLog.create({
    userId: user.id,
    action: 'update',
    resourceType: 'integration',
    description: `Updated Google Calendar event: ${updates.summary || 'Unknown'}`,
    metadata: { provider: 'google-calendar', eventId, eventTitle: updates.summary },
  });
  return c.json({ success: true, event: response.data, message: 'Event updated successfully' });
});

app.delete('/events/:eventId', async c => {
  const user = c.get('user');
  if (!user) return c.json({ error: 'Unauthorized' }, 401);
  const client = await getCalendarClient(user.id);
  if (!client) return c.json({ error: 'Google Calendar not connected' }, 401);
  const eventId = c.req.param('eventId');
  const calendarId = 'primary';
  await client.calendar.events.delete({ calendarId, eventId });
  await ActivityLog.create({
    userId: user.id,
    action: 'delete',
    resourceType: 'integration',
    description: `Deleted Google Calendar event: ${eventId}`,
    metadata: { provider: 'google-calendar', eventId },
  });
  return c.json({ success: true, message: 'Event deleted successfully' });
});

app.delete('/disconnect', async c => {
  const user = c.get('user');
  if (!user) return c.json({ error: 'Unauthorized' }, 401);
  await Integration.findOneAndUpdate(
    { userId: user.id, provider: 'google-calendar' },
    { isActive: false, disconnectedAt: new Date() }
  );
  await ActivityLog.create({
    userId: user.id,
    action: 'delete',
    resourceType: 'integration',
    description: 'Disconnected Google Calendar integration',
    metadata: { provider: 'google-calendar' },
  });
  return c.json({ success: true, message: 'Google Calendar disconnected successfully' });
});

// Disabled/placeholder endpoints for sync, webhook, import-tasks
app.post('/sync', async c =>
  c.json({ success: true, message: 'Sync functionality temporarily disabled' })
);
app.post('/webhook', async c =>
  c.json({ success: true, message: 'Webhook setup temporarily disabled' })
);
app.post('/webhook-notification', async c => c.json({ success: true }));
app.post('/import-tasks', async c =>
  c.json({ success: true, message: 'Task import temporarily disabled' })
);

export const GET = handle(app);
export const POST = handle(app);
export const PUT = handle(app);
export const DELETE = handle(app);
