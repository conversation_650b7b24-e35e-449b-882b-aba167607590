'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Crown, Users, HardDrive, Zap, Calendar, AlertCircle, CheckCircle } from 'lucide-react';
import { useSubscription } from '@/hooks/useSubscription';
import { useSession } from 'next-auth/react';

export const SubscriptionDashboard: React.FC = () => {
  const { data: session } = useSession();

  const {
    subscriptionInfo,
    isLoadingInfo,
    formatPrice,
    getUsagePercentage,
    isLimitReached,
    getDaysUntilBilling,
  } = useSubscription(session?.user?.organizationId || undefined);

  if (isLoadingInfo) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-32" />
            <Skeleton className="h-4 w-64" />
          </CardHeader>
          <CardContent className="space-y-4">
            <Skeleton className="h-20 w-full" />
            <Skeleton className="h-16 w-full" />
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!subscriptionInfo) {
    return (
      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          Unable to load subscription information. Please try again later.
        </AlertDescription>
      </Alert>
    );
  }

  const getPlanIcon = () => {
    switch (subscriptionInfo.plan.id) {
      case 'free':
        return <HardDrive className="h-5 w-5" />;
      case 'starter':
        return <Zap className="h-5 w-5 text-blue-500" />;
      case 'professional':
        return <Crown className="h-5 w-5 text-purple-500" />;
      case 'enterprise':
        return <Crown className="h-5 w-5 text-orange-500" />;
      default:
        return <HardDrive className="h-5 w-5" />;
    }
  };

  const getUsageColor = (percentage: number) => {
    if (percentage >= 90) return 'text-red-500';
    if (percentage >= 75) return 'text-yellow-500';
    return 'text-green-500';
  };

  return (
    <div className="space-y-6">
      {/* Current Plan Overview */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              {getPlanIcon()}
              <div>
                <CardTitle className="flex items-center gap-2">
                  {subscriptionInfo.plan.name} Plan
                  <Badge
                    variant={
                      subscriptionInfo.subscription.status === 'active' ? 'default' : 'secondary'
                    }
                  >
                    {subscriptionInfo.subscription.status}
                  </Badge>
                </CardTitle>
                <CardDescription>{subscriptionInfo.plan.description}</CardDescription>
              </div>
            </div>
            <div className="text-right">
              <p className="text-2xl font-bold">{formatPrice(subscriptionInfo.billing.amount)}</p>
              <p className="text-sm text-muted-foreground">
                {subscriptionInfo.billing.billingCycle === 'monthly'
                  ? 'per month'
                  : subscriptionInfo.billing.billingCycle === 'yearly'
                    ? 'per year'
                    : 'one-time'}
              </p>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Usage Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Users */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Team Members</span>
                </div>
                <span
                  className={`text-sm font-medium ${getUsageColor(getUsagePercentage('users'))}`}
                >
                  {subscriptionInfo.usage.users} /{' '}
                  {typeof subscriptionInfo.plan.features.users === 'number'
                    ? subscriptionInfo.plan.features.users
                    : '∞'}
                </span>
              </div>
              <Progress value={Math.min(getUsagePercentage('users'), 100)} className="h-2" />
              {isLimitReached('users') && <p className="text-xs text-red-500">Limit reached</p>}
            </div>

            {/* Projects */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <HardDrive className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Projects</span>
                </div>
                <span
                  className={`text-sm font-medium ${getUsageColor(getUsagePercentage('projects'))}`}
                >
                  {subscriptionInfo.usage.projects} /{' '}
                  {typeof subscriptionInfo.plan.features.projects === 'number'
                    ? subscriptionInfo.plan.features.projects
                    : '∞'}
                </span>
              </div>
              <Progress value={Math.min(getUsagePercentage('projects'), 100)} className="h-2" />
              {isLimitReached('projects') && <p className="text-xs text-red-500">Limit reached</p>}
            </div>

            {/* Storage */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <HardDrive className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Storage</span>
                </div>
                <span
                  className={`text-sm font-medium ${getUsageColor(subscriptionInfo.usage.storage.percentage)}`}
                >
                  {subscriptionInfo.plan.features.storage}
                </span>
              </div>
              <Progress
                value={Math.min(subscriptionInfo.usage.storage.percentage, 100)}
                className="h-2"
              />
              <p className="text-xs text-muted-foreground">
                {(subscriptionInfo.usage.storage.used / (1024 * 1024)).toFixed(1)}MB used
              </p>
            </div>

            {/* Integrations */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Zap className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Integrations</span>
                </div>
                <span
                  className={`text-sm font-medium ${getUsageColor(getUsagePercentage('integrations'))}`}
                >
                  {subscriptionInfo.usage.integrations} /{' '}
                  {typeof subscriptionInfo.plan.features.integrations === 'number'
                    ? subscriptionInfo.plan.features.integrations
                    : '∞'}
                </span>
              </div>
              <Progress value={Math.min(getUsagePercentage('integrations'), 100)} className="h-2" />
              {isLimitReached('integrations') && (
                <p className="text-xs text-red-500">Limit reached</p>
              )}
            </div>
          </div>

          <Separator />

          {/* Plan Features */}
          <div>
            <h4 className="font-medium mb-3">Plan Features</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <span className="text-sm">Basic task management</span>
              </div>
              {subscriptionInfo.plan.features.advancedAnalytics && (
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span className="text-sm">Advanced analytics</span>
                </div>
              )}
              {subscriptionInfo.plan.features.customIntegrations && (
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span className="text-sm">Custom integrations</span>
                </div>
              )}
              {subscriptionInfo.plan.features.prioritySupport && (
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span className="text-sm">Priority support</span>
                </div>
              )}
              {subscriptionInfo.plan.features.apiAccess && (
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span className="text-sm">API access</span>
                </div>
              )}
              {subscriptionInfo.plan.features.ssoEnabled && (
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span className="text-sm">SSO & advanced security</span>
                </div>
              )}
            </div>
          </div>

          <Separator />

          {/* Next Billing & Actions */}
          <div className="flex items-center justify-between">
            <div>
              {subscriptionInfo.billing.nextPaymentDate ? (
                <div>
                  <p className="font-medium flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    Next billing:{' '}
                    {new Date(subscriptionInfo.billing.nextPaymentDate).toLocaleDateString()}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    {getDaysUntilBilling() && `${getDaysUntilBilling()} days remaining`}
                  </p>
                </div>
              ) : (
                <p className="text-sm text-muted-foreground">No upcoming billing</p>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
