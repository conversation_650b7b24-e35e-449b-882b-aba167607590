export interface MindMapNode {
  id: string;
  type:
    | 'root'
    | 'branch'
    | 'leaf'
    | 'note'
    | 'image'
    | 'link'
    | 'task'
    | 'milestone'
    | 'decision'
    | 'resource'
    | 'timeline'
    | 'team'
    | 'project'
    | 'deadline'
    | 'status'
    | 'annotation'
    | 'process'
    | 'text';
  position: {
    x: number;
    y: number;
  };
  size: {
    width: number;
    height: number;
  };
  content: {
    text: string;
    color?: string;
    backgroundColor?: string;
    fontSize?: number;
    fontWeight?: string;
    imageUrl?: string;
    linkUrl?: string;
    notes?: string;
    // Additional properties for different node types
    title?: string;
    content?: string;
    completed?: boolean;
    achieved?: boolean;
    decided?: boolean;
    url?: string;
    date?: string;
    members?: string[];
    progress?: number;
    status?: string;
    steps?: Array<{ number: number; description: string }>;
    currentStep?: number;
    priority?: 'low' | 'medium' | 'high';
    assignee?: string;
    description?: string;
    options?: string[];
    selected?: string;
    conditions?: Array<{ handle: string; label: string }>;
  };
  style?: {
    borderColor?: string;
    borderWidth?: number;
    borderRadius?: number;
    opacity?: number;
    shadow?: boolean;
  };
  parentId?: string;
  childIds: string[];
  collapsed?: boolean;
  zIndex?: number;
  selected?: boolean;
  dragging?: boolean;
}

export interface MindMapConnection {
  id: string;
  sourceNodeId: string;
  targetNodeId: string;
  type: 'straight' | 'curved' | 'step' | 'smoothstep';
  style?: {
    stroke?: string;
    strokeWidth?: number;
    strokeDasharray?: string;
    animated?: boolean;
  };
  label?: string;
  labelStyle?: {
    fontSize?: number;
    color?: string;
    backgroundColor?: string;
  };
}

export interface MindMapViewport {
  x: number;
  y: number;
  zoom: number;
}

export interface MindMapAnalytics {
  viewCount: number;
  editCount: number;
  lastViewed?: Date;
  lastEdited?: Date;
}

export interface MindMap {
  _id: string;
  title: string;
  description?: string;
  userId: string;
  projectId?: string;
  organizationId?: string;

  // Mind map content
  nodes: MindMapNode[];
  connections: MindMapConnection[];
  viewport: MindMapViewport;

  // Metadata
  tags: string[];
  status: 'draft' | 'active' | 'archived' | 'template';

  // Analytics
  analytics: MindMapAnalytics;

  // Soft delete
  isDeleted: boolean;
  deletedAt?: Date;
  deletedBy?: string;

  // Timestamps
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateMindMapRequest {
  title: string;
  description?: string;
  projectId?: string;
  tags?: string[];
}

export interface UpdateMindMapRequest {
  title?: string;
  description?: string;
  nodes?: MindMapNode[];
  connections?: MindMapConnection[];
  viewport?: MindMapViewport;
  tags?: string[];
  status?: 'draft' | 'active' | 'archived' | 'template';
}

export interface MindMapFilters {
  projectId?: string;
  status?: string;
  search?: string;
  tags?: string[];
}

export interface ExportMindMapRequest {
  format: 'json' | 'png' | 'svg' | 'pdf' | 'markdown';
}

// Template types for mind map templates
export interface MindMapTemplate {
  _id: string;
  name: string;
  description: string;
  category: string;
  thumbnail?: string;
  nodes: MindMapNode[];
  connections: MindMapConnection[];
  viewport: MindMapViewport;
  tags: string[];
  isPublic: boolean;
  createdBy: string;
  usageCount: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateTemplateRequest {
  name: string;
  description: string;
  category: string;
  thumbnail?: string;
  isPublic?: boolean;
}

// Export formats
export type ExportFormat = 'json' | 'png' | 'svg' | 'pdf' | 'markdown' | 'freemind' | 'xmind';

// Mind map themes
export interface MindMapTheme {
  id: string;
  name: string;
  colors: {
    background: string;
    node: string;
    text: string;
    connection: string;
    accent: string;
  };
  fonts: {
    family: string;
    sizes: {
      small: number;
      medium: number;
      large: number;
    };
  };
  spacing: {
    nodeGap: number;
    levelGap: number;
  };
}

// Search and filter types
export interface MindMapSearchResult {
  mindMap: MindMap;
  matches: {
    field: 'title' | 'description' | 'nodes' | 'tags';
    text: string;
    highlight: string;
  }[];
}

export interface MindMapStats {
  total: number;
  byStatus: Record<string, number>;
  byProject: Record<string, number>;
  recentActivity: {
    created: number;
    updated: number;
    viewed: number;
  };
}
