import axios from 'axios';

// Client-side helper functions for permissions API
export const getUserPermissions = async (
  userId: string,
  resourceType?: string,
  resourceId?: string,
  organizationId?: string
) => {
  const params = new URLSearchParams();
  if (userId) params.append('userId', userId);
  if (resourceType) params.append('resourceType', resourceType);
  if (resourceId) params.append('resourceId', resourceId);
  if (organizationId) params.append('organizationId', organizationId);
  const res = await axios.get(`/api/permissions/user-permissions?${params}`);
  return res.data;
};

export const updateUserRole = async (userId: string, organizationId: string, role: string) => {
  const res = await axios.post('/api/permissions/update-user-role', {
    userId,
    organizationId,
    role,
  });
  return res.data;
};

export const grantPermission = async (permissionData: any) => {
  const res = await axios.post('/api/permissions/grant', permissionData);
  return res.data;
};

export const revokePermission = async (permissionId: string) => {
  const res = await axios.delete(`/api/permissions/permissions/${permissionId}`);
  return res.data;
};

export const getOrganizationMembers = async (
  organizationId: string,
  search?: string,
  page: number = 1,
  limit: number = 50
) => {
  const params = new URLSearchParams({
    organizationId,
    page: page.toString(),
    limit: limit.toString(),
  });
  if (search) params.append('search', search);
  const res = await axios.get(`/api/permissions/organization-members?${params}`);
  return res.data;
};
