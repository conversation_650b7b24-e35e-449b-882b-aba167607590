'use client';

import React, { useState, useCallback } from 'react';
import {
  DndContext,
  DragOverlay,
  closestCenter,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { Plus, ChevronLeft, ChevronRight, RefreshCw } from 'lucide-react';
import {
  format,
  addDays,
  subDays,
  startOfWeek,
  addWeeks,
  subWeeks,
  addMonths,
  subMonths,
} from 'date-fns';
import { IEvent } from '@/models/Event';
import MonthView from './MonthView';
import WeekView from './WeekView';
import DayView from './DayView';
import AgendaView from './AgendaView';

interface DragDropCalendarProps {
  initialView?: 'month' | 'week' | 'day' | 'agenda';
  showGoogleCalendarSync?: boolean;
  allowEventCreation?: boolean;
  events?: IEvent[];
  onEventUpdate?: (eventId: string, updates: Partial<IEvent>) => void;
  onGoogleCalendarSync?: () => void;
  onEventClick?: (event: IEvent) => void;
  onEventDrop?: (eventId: string, dropDate: Date) => void;
  className?: string;
}

export default function DragDropCalendar({
  initialView = 'month',
  showGoogleCalendarSync = false,
  allowEventCreation = true,
  events = [],
  onEventUpdate,
  onGoogleCalendarSync,
  onEventClick,
  onEventDrop,
  className = '',
}: DragDropCalendarProps) {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [viewType, setViewType] = useState<'month' | 'week' | 'day' | 'agenda'>(initialView);
  const [isCreatingEvent, setIsCreatingEvent] = useState(false);
  const [draggedEvent, setDraggedEvent] = useState<IEvent | null>(null);
  const { toast } = useToast();

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    })
  );

  const handleDragStart = (event: any) => {
    const { active } = event;
    if (active.data.current?.type === 'event') {
      setDraggedEvent(active.data.current.event);
    }
  };

  const handleDragEnd = (event: any) => {
    const { active, over } = event;
    setDraggedEvent(null);

    if (!over) return;

    const activeData = active.data.current;
    const overData = over.data.current;

    if (activeData?.type === 'event' && overData?.type === 'timeslot') {
      const eventToMove = activeData.event;
      const dropDate = new Date(overData.date);

      if (overData.hour !== undefined) {
        dropDate.setHours(overData.hour, overData.minute || 0, 0, 0);
      }

      const eventDuration =
        new Date(eventToMove.endTime).getTime() - new Date(eventToMove.startTime).getTime();
      const newEndTime = new Date(dropDate.getTime() + eventDuration);

      handleEventDrop(String(eventToMove._id), dropDate, newEndTime);
    }
  };

  const handleEventClick = useCallback(
    (event: IEvent) => {
      if (onEventClick) {
        onEventClick(event);
      }
    },
    [onEventClick]
  );

  const handleEventDrop = useCallback(
    (eventId: string, newStart: Date, newEnd: Date) => {
      if (onEventDrop) {
        onEventDrop(eventId, newStart);
        return;
      }
      if (!onEventUpdate) return;

      try {
        onEventUpdate(eventId, {
          startTime: newStart,
          endTime: newEnd,
        });
        toast({
          title: 'Event moved',
          description: 'Event has been successfully moved to the new time.',
        });
      } catch (error) {
        toast({
          title: 'Error',
          description: 'Failed to move event. Please try again.',
          variant: 'destructive',
        });
      }
    },
    [onEventDrop, onEventUpdate, toast]
  );

  const handleTimeSlotClick = useCallback(
    (date: Date) => {
      if (!allowEventCreation) return;

      setIsCreatingEvent(true);
      // Here you would typically open a modal or form to create a new event
      // For now, we'll just show a toast
      toast({
        title: 'Create Event',
        description: `Create new event at ${format(date, 'PPP p')}`,
      });
    },
    [allowEventCreation, toast]
  );

  const handleEventResize = useCallback(
    (eventId: string, newStart: Date, newEnd: Date) => {
      if (!onEventUpdate) return;

      try {
        onEventUpdate(eventId, {
          startTime: newStart,
          endTime: newEnd,
        });
        toast({
          title: 'Event resized',
          description: 'Event duration has been updated.',
        });
      } catch (error) {
        toast({
          title: 'Error',
          description: 'Failed to resize event. Please try again.',
          variant: 'destructive',
        });
      }
    },
    [onEventUpdate, toast]
  );

  const navigateDate = useCallback(
    (direction: 'prev' | 'next') => {
      setCurrentDate(prevDate => {
        switch (viewType) {
          case 'month':
            return direction === 'next' ? addMonths(prevDate, 1) : subMonths(prevDate, 1);
          case 'week':
            return direction === 'next' ? addWeeks(prevDate, 1) : subWeeks(prevDate, 1);
          case 'day':
          case 'agenda':
            return direction === 'next' ? addDays(prevDate, 1) : subDays(prevDate, 1);
          default:
            return prevDate;
        }
      });
    },
    [viewType]
  );

  const goToToday = useCallback(() => {
    setCurrentDate(new Date());
  }, []);

  const renderCalendarView = () => {
    const commonProps = {
      events,
      currentDate,
      viewType,
      onEventClick: handleEventClick,
      onEventDrop: handleEventDrop,
      onTimeSlotClick: handleTimeSlotClick,
      onEventResize: handleEventResize,
      selectedEvent: undefined, // Now controlled by parent if needed
      isCreatingEvent,
    };

    switch (viewType) {
      case 'month':
        return <MonthView {...commonProps} />;
      case 'week':
        return <WeekView {...commonProps} />;
      case 'day':
        return <DayView {...commonProps} />;
      case 'agenda':
        return <AgendaView {...commonProps} />;
      default:
        return <MonthView {...commonProps} />;
    }
  };

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCenter}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      <div className={`flex flex-col h-full ${className} theme-transition`}>
        {/* Calendar Header */}
        <div className="flex items-center justify-between p-4 border-b theme-border theme-surface dashboard-card-header">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => navigateDate('prev')}
                className="theme-button-secondary theme-focus"
              >
                <ChevronLeft className="w-4 h-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => navigateDate('next')}
                className="theme-button-secondary theme-focus"
              >
                <ChevronRight className="w-4 h-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={goToToday}
                className="theme-button-secondary theme-focus"
              >
                Today
              </Button>
            </div>

            <div className="text-xl font-semibold theme-text-primary">
              {viewType === 'month' && format(currentDate, 'MMMM yyyy')}
              {viewType === 'week' && `Week of ${format(startOfWeek(currentDate), 'MMM d, yyyy')}`}
              {viewType === 'day' && format(currentDate, 'EEEE, MMMM d, yyyy')}
              {viewType === 'agenda' && 'Agenda'}
            </div>
          </div>

          <div className="flex items-center gap-2">
            {/* View Type Selector */}
            <div className="flex border rounded-md theme-border tabs-enhanced">
              {(['month', 'week', 'day', 'agenda'] as const).map(view => (
                <Button
                  key={view}
                  variant={viewType === view ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewType(view)}
                  className={`rounded-none first:rounded-l-md last:rounded-r-md capitalize ${
                    viewType === view ? 'theme-active-primary' : 'theme-hover-primary'
                  } theme-focus`}
                  data-state={viewType === view ? 'active' : 'inactive'}
                >
                  {view}
                </Button>
              ))}
            </div>

            {/* Additional Actions */}
            {showGoogleCalendarSync && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  if (onGoogleCalendarSync) {
                    onGoogleCalendarSync();
                  }
                }}
                className="theme-button-secondary theme-focus"
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                Sync
              </Button>
            )}

            {allowEventCreation && (
              <Button
                size="sm"
                onClick={() => handleTimeSlotClick(new Date())}
                className="theme-button-primary theme-focus"
              >
                <Plus className="w-4 h-4 mr-2" />
                New Event
              </Button>
            )}
          </div>
        </div>

        {/* Calendar Content */}
        <div className="flex-1 overflow-hidden theme-scrollbar theme-surface-elevated">
          {renderCalendarView()}
        </div>
      </div>
      <DragOverlay>
        {draggedEvent && (
          <div className="px-2 py-1 rounded text-xs font-medium bg-primary text-primary-foreground border-l-2 border-primary/40 opacity-90 theme-shadow-sm cursor-grab">
            {draggedEvent.title}
          </div>
        )}
      </DragOverlay>
    </DndContext>
  );
}
