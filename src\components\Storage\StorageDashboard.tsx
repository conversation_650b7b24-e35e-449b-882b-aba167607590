'use client';

import React, { useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useStorage } from '@/hooks/useStorage';
import { useSubscription } from '@/hooks/useSubscription';
import { HardDrive, Plus, AlertTriangle, CheckCircle, TrendingUp, BarChart } from 'lucide-react';
import { StoragePurchaseModal } from './StoragePurchaseModal';
import { useState } from 'react';
import { motion } from 'framer-motion';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  Legend,
} from 'recharts';
import { StorageTierUpgradeModal } from '@/components/Subscription/StorageTierUpgradeModal';

export const StorageDashboard: React.FC = () => {
  const {
    storageInfo,
    storageBreakdown,
    isLoadingInfo,
    isLoadingBreakdown,
    formatBytes,
    formatMB,
    getStorageStatus,
    getStorageQuotaInfo,
    isLoadingQuotaInfo,
    storageQuotaInfo,
    getStorageUsageAnalytics,
    storageUsageAnalytics,
    isLoadingStorageAnalytics,
    getStorageOptimizationRecommendations,
    storageOptimizationRecommendations,
    storageForecast,
    isLoadingStorageForecast,
  } = useStorage();

  const { isLoadingInfo: isLoadingSubscriptionInfo } = useSubscription(storageInfo?.organizationId);

  const [showPurchaseModal, setShowPurchaseModal] = useState(false);
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);
  const [selectedTierId, setSelectedTierId] = useState<string | null>(null);

  useEffect(() => {
    if (storageInfo?.organizationId) {
      getStorageQuotaInfo(storageInfo.organizationId);
      getStorageUsageAnalytics(storageInfo.organizationId);
      getStorageOptimizationRecommendations(storageInfo.organizationId);
    }
  }, [
    storageInfo?.organizationId,
    getStorageQuotaInfo,
    getStorageUsageAnalytics,
    getStorageOptimizationRecommendations,
  ]);

  if (isLoadingInfo || isLoadingQuotaInfo || isLoadingSubscriptionInfo) {
    return (
      <div className="space-y-6">
        <div className="loading-skeleton h-32 rounded-lg"></div>
        <div className="loading-skeleton h-64 rounded-lg"></div>
        <div className="loading-skeleton h-64 rounded-lg"></div>
      </div>
    );
  }

  if (!storageInfo) {
    return (
      <Card className="dashboard-card">
        <CardContent className="p-6">
          <div className="empty-state-container">
            <div className="empty-state-icon">
              <AlertTriangle className="h-8 w-8 text-destructive" />
            </div>
            <p className="empty-state-title">Failed to load storage information</p>
            <p className="empty-state-description">Please try refreshing the page</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const statusIcon = () => {
    const percentage = storageInfo.usagePercentage;
    if (percentage >= 100)
      return <AlertTriangle className="h-5 w-5 text-destructive theme-transition" />;
    if (percentage >= 90)
      return <AlertTriangle className="h-5 w-5 text-warning theme-transition" />;
    return <CheckCircle className="h-5 w-5 text-success theme-transition" />;
  };

  const pieChartData =
    storageBreakdown?.map(item => ({
      name: item._id,
      value: item.totalSize,
    })) || [];

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#AF19FF', '#FF1944'];

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="p-2 bg-background border rounded-md shadow-lg">
          <p className="font-bold text-sm capitalize">{label}</p>
          <p className="text-xs text-muted-foreground">{`${formatBytes(payload[0].value)} (${(payload[0].percent * 100).toFixed(1)}%)`}</p>
        </div>
      );
    }
    return null;
  };

  const forecastData =
    storageForecast?.forecastData.map(item => ({
      date: new Date(item.date).toLocaleDateString('en-US', { month: 'short', year: 'numeric' }),
      projectedUsage: parseFloat(formatMB(item.projectedUsage).toFixed(2)),
    })) || [];

  return (
    <div className="space-y-6">
      {/* Storage Overview */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <Card className="dashboard-card hover-reveal">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2 dashboard-card-title">
                  <HardDrive className="h-5 w-5 text-blue-600 dark:text-blue-400 theme-transition" />
                  Storage Usage
                </CardTitle>
                <CardDescription className="dashboard-card-description">
                  Monitor your organization's storage consumption
                </CardDescription>
              </div>
              <div className="flex items-center gap-2">
                {statusIcon()}
                <Badge
                  variant={storageInfo.usagePercentage >= 90 ? 'destructive' : 'secondary'}
                  className="theme-transition"
                >
                  {getStorageStatus(storageInfo.usagePercentage)}
                </Badge>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Storage Progress */}
            <div className="space-y-2">
              <div className="flex justify-between text-sm theme-text-primary">
                <span>Used: {formatBytes(storageInfo.totalUsed)}</span>
                <span>Total: {formatBytes(storageInfo.totalLimit)}</span>
              </div>
              <Progress value={Math.min(storageInfo.usagePercentage, 100)} className="h-3" />
              <div className="flex justify-between text-xs theme-text-secondary">
                <span>{storageInfo.usagePercentage.toFixed(1)}% used</span>
                <span>{formatBytes(storageInfo.availableStorage)} available</span>
              </div>
            </div>

            {/* Storage Tier Information */}
            {storageQuotaInfo && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card className="p-4 theme-surface rounded-lg theme-border">
                  <CardTitle className="text-lg mb-2 flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500" /> Current Storage Plan
                  </CardTitle>
                  <CardDescription>
                    You are currently on the{' '}
                    <span className="font-semibold text-primary">
                      {storageQuotaInfo.currentTier?.name || 'N/A'}
                    </span>{' '}
                    plan.
                  </CardDescription>
                  <div className="mt-2 text-sm">
                    Limit: {storageQuotaInfo.currentTier?.storageFormatted || 'N/A'}
                  </div>
                </Card>
                <Card className="p-4 theme-surface rounded-lg theme-border">
                  <CardTitle className="text-lg mb-2 flex items-center gap-2">
                    <TrendingUp className="h-4 w-4 text-purple-500" /> Recommended Upgrade
                  </CardTitle>
                  <CardDescription>
                    Based on your usage, we recommend the{' '}
                    <span className="font-semibold text-primary">
                      {storageQuotaInfo.recommendedTier?.name || 'N/A'}
                    </span>{' '}
                    plan.
                  </CardDescription>
                  <div className="mt-2 text-sm">
                    Next Tier Limit: {storageQuotaInfo.recommendedTier?.storageFormatted || 'N/A'}
                  </div>
                  {storageQuotaInfo.canUpgrade && (
                    <Button
                      size="sm"
                      className="mt-4"
                      onClick={() => {
                        setSelectedTierId(storageQuotaInfo.recommendedTier?.id || null);
                        setShowUpgradeModal(true);
                      }}
                    >
                      Upgrade Plan
                    </Button>
                  )}
                </Card>
              </div>
            )}

            {/* Storage Breakdown */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <motion.div
                className="text-center p-4 theme-surface rounded-lg theme-border hover-reveal theme-transition"
                whileHover={{ scale: 1.02 }}
                transition={{ duration: 0.2 }}
              >
                <div className="text-2xl font-bold text-blue-600 dark:text-blue-400 theme-transition">
                  {formatMB(storageInfo.freeStorageLimit)}MB
                </div>
                <div className="text-sm theme-text-secondary">Free Storage</div>
              </motion.div>
              <motion.div
                className="text-center p-4 theme-surface rounded-lg theme-border hover-reveal theme-transition"
                whileHover={{ scale: 1.02 }}
                transition={{ duration: 0.2 }}
              >
                <div className="text-2xl font-bold text-green-600 dark:text-green-400 theme-transition">
                  {formatMB(storageInfo.purchasedStorage)}MB
                </div>
                <div className="text-sm theme-text-secondary">Purchased Storage</div>
              </motion.div>
              <motion.div
                className="text-center p-4 theme-surface rounded-lg theme-border hover-reveal theme-transition"
                whileHover={{ scale: 1.02 }}
                transition={{ duration: 0.2 }}
              >
                <div className="text-2xl font-bold text-purple-600 dark:text-purple-400 theme-transition">
                  {formatMB(storageInfo.totalUsed)}MB
                </div>
                <div className="text-sm theme-text-secondary">Currently Used</div>
              </motion.div>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-2">
              <Button
                onClick={() => setShowPurchaseModal(true)}
                className="flex items-center gap-2"
              >
                <Plus className="h-4 w-4" />
                Buy More Storage
              </Button>
              {storageInfo.usagePercentage >= 90 && (
                <Button variant="outline" className="text-warning theme-transition">
                  <AlertTriangle className="h-4 w-4 mr-2" />
                  Storage Almost Full
                </Button>
              )}
            </div>

            {/* Warning Messages */}
            {storageInfo.isOverLimit && (
              <div className="p-4 bg-destructive/10 border border-destructive/20 rounded-lg theme-transition">
                <div className="flex items-center gap-2 text-destructive">
                  <AlertTriangle className="h-5 w-5" />
                  <span className="font-medium">Storage Limit Exceeded</span>
                </div>
                <p className="text-sm text-destructive/80 mt-1">
                  You cannot upload new files until you purchase additional storage or delete
                  existing files.
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </motion.div>

      {/* Storage Breakdown by Resource Type */}
      <Card className="dashboard-card">
        <CardHeader>
          <CardTitle className="dashboard-card-title">Storage Breakdown</CardTitle>
          <CardDescription className="dashboard-card-description">
            See how your storage is distributed across different resource types
          </CardDescription>
        </CardHeader>
        <CardContent className="theme-scrollbar">
          {isLoadingBreakdown ? (
            <div className="space-y-2">
              {[1, 2, 3].map(i => (
                <div key={i} className="loading-skeleton h-12 rounded"></div>
              ))}
            </div>
          ) : storageBreakdown && storageBreakdown.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-4">
                {storageBreakdown.map(item => (
                  <motion.div
                    key={item._id}
                    className="flex items-center justify-between p-3 theme-surface rounded-lg theme-border hover-reveal theme-transition"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3 }}
                    whileHover={{ scale: 1.01 }}
                  >
                    <div className="flex items-center gap-3">
                      <div className="w-3 h-3 bg-blue-500 dark:bg-blue-400 rounded-full theme-transition"></div>
                      <div>
                        <div className="font-medium capitalize theme-text-primary">{item._id}</div>
                        <div className="text-sm theme-text-secondary">{item.fileCount} files</div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-medium theme-text-primary">
                        {formatBytes(item.totalSize)}
                      </div>
                      <div className="text-sm theme-text-secondary">
                        {((item.totalSize / storageInfo.totalUsed) * 100).toFixed(1)}%
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
              <div className="w-full h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={pieChartData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {pieChartData.map((_entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip content={<CustomTooltip />} />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </div>
          ) : (
            <div className="empty-state-container">
              <div className="empty-state-icon">
                <HardDrive className="h-8 w-8 theme-text-secondary" />
              </div>
              <p className="empty-state-title">No files uploaded yet</p>
              <p className="empty-state-description">
                Start uploading files to see storage breakdown
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Storage Analytics and Forecasting */}
      <Card className="dashboard-card">
        <CardHeader>
          <CardTitle className="dashboard-card-title flex items-center gap-2">
            <BarChart className="h-5 w-5 text-green-600 dark:text-green-400" />
            Storage Analytics & Forecast
          </CardTitle>
          <CardDescription className="dashboard-card-description">
            Understand your storage growth and plan for future needs
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {isLoadingStorageAnalytics || isLoadingStorageForecast ? (
            <div className="loading-skeleton h-64 rounded-lg"></div>
          ) : (
            <>
              {/* Growth Trend */}
              <div className="space-y-2">
                <h4 className="font-medium theme-text-primary">Monthly Storage Trend</h4>
                <div className="w-full h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart
                      data={forecastData}
                      margin={{ top: 5, right: 20, left: 10, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" strokeOpacity={0.5} />
                      <XAxis dataKey="date" className="text-xs" />
                      <YAxis
                        unit="MB"
                        tickFormatter={value => `${value.toFixed(0)}`}
                        className="text-xs"
                      />
                      <Tooltip
                        formatter={(value: number) => [`${value.toFixed(2)} MB`, 'Projected Usage']}
                      />
                      <Line
                        type="monotone"
                        dataKey="projectedUsage"
                        stroke="#8884d8"
                        activeDot={{ r: 8 }}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </div>
              </div>

              {/* Optimization Recommendations */}
              {storageOptimizationRecommendations &&
                storageOptimizationRecommendations.recommendations.length > 0 && (
                  <div className="space-y-2">
                    <h4 className="font-medium theme-text-primary">Optimization Recommendations</h4>
                    <ul className="list-disc list-inside text-sm theme-text-secondary">
                      {storageOptimizationRecommendations.recommendations.map((rec, index) => (
                        <li key={index}>{rec}</li>
                      ))}
                    </ul>
                    <p className="text-sm theme-text-secondary">
                      Potential Savings:{' '}
                      <span className="font-medium">
                        {formatBytes(storageOptimizationRecommendations.potentialSavings)}
                      </span>
                    </p>
                  </div>
                )}

              {/* Usage Breakdown and Insights */}
              {storageUsageAnalytics && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h4 className="font-medium theme-text-primary mb-2">Top File Types</h4>
                    <ul className="space-y-1 text-sm theme-text-secondary">
                      {storageUsageAnalytics.topFileTypes.map((type, index) => (
                        <li key={index} className="flex justify-between">
                          <span>
                            {type.type} ({type.count} files)
                          </span>
                          <span>{formatBytes(type.size)}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-medium theme-text-primary mb-2">Usage by Resource</h4>
                    <ul className="space-y-1 text-sm theme-text-secondary">
                      {storageUsageAnalytics.usageByResource.map((resource, index) => (
                        <li key={index} className="flex justify-between">
                          <span>{resource.resourceType}</span>
                          <span>{formatBytes(resource.usage)}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Storage Purchase Modal */}
      <StoragePurchaseModal
        isOpen={showPurchaseModal}
        onClose={() => setShowPurchaseModal(false)}
      />

      {/* Storage Tier Upgrade Modal */}
      {selectedTierId && (
        <StorageTierUpgradeModal
          isOpen={showUpgradeModal}
          onClose={() => setShowUpgradeModal(false)}
          organizationId={storageInfo?.organizationId || ''}
          currentTierId={storageQuotaInfo?.currentTier?.id || ''}
          targetTierId={selectedTierId}
        />
      )}
    </div>
  );
};
