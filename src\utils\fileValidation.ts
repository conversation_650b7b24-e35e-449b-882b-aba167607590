export interface FileValidationResult {
  isValid: boolean;
  error?: string;
  fileSize?: number;
}

export const calculateBase64FileSize = (base64String: string): number => {
  const base64Data = base64String.split(',')[1] || base64String;

  // Calculate size: base64 is ~33% larger than original
  // Each base64 character represents 6 bits, so 4 chars = 3 bytes
  const padding = (base64Data.match(/=/g) || []).length;
  const sizeInBytes = (base64Data.length * 3) / 4 - padding;

  return Math.floor(sizeInBytes);
};

export const validateFileSize = (
  fileSize: number,
  maxSize: number = 10 * 1024 * 1024 // 10MB default
): FileValidationResult => {
  if (fileSize <= 0) {
    return {
      isValid: false,
      error: 'Invalid file size',
      fileSize,
    };
  }

  if (fileSize > maxSize) {
    const maxSizeMB = Math.round(maxSize / (1024 * 1024));
    const fileSizeMB = Math.round((fileSize / (1024 * 1024)) * 100) / 100;

    return {
      isValid: false,
      error: `File size (${fileSizeMB}MB) exceeds maximum allowed size (${maxSizeMB}MB)`,
      fileSize,
    };
  }

  return {
    isValid: true,
    fileSize,
  };
};

export const validateFileType = (
  fileName: string,
  mimeType: string,
  allowedTypes: string[] = [
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/webp',
    'application/pdf',
    'text/plain',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  ]
): FileValidationResult => {
  if (!allowedTypes.includes(mimeType)) {
    const fileExtension = fileName.split('.').pop()?.toLowerCase();

    return {
      isValid: false,
      error: `File type .${fileExtension} (${mimeType}) is not allowed`,
    };
  }

  return {
    isValid: true,
  };
};

export const validateFile = (
  file: File,
  options: {
    maxSize?: number;
    allowedTypes?: string[];
  } = {}
): FileValidationResult => {
  const {
    maxSize = 10 * 1024 * 1024, // 10MB
    allowedTypes = [
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
      'application/pdf',
      'text/plain',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    ],
  } = options;

  const typeValidation = validateFileType(file.name, file.type, allowedTypes);
  if (!typeValidation.isValid) {
    return typeValidation;
  }

  const sizeValidation = validateFileSize(file.size, maxSize);
  if (!sizeValidation.isValid) {
    return sizeValidation;
  }

  return {
    isValid: true,
    fileSize: file.size,
  };
};

export const validateBase64File = (
  base64Data: string,
  fileName: string,
  mimeType: string,
  options: {
    maxSize?: number;
    allowedTypes?: string[];
  } = {}
): FileValidationResult => {
  const {
    maxSize = 10 * 1024 * 1024, // 10MB
    allowedTypes = [
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
      'application/pdf',
      'text/plain',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    ],
  } = options;

  const typeValidation = validateFileType(fileName, mimeType, allowedTypes);
  if (!typeValidation.isValid) {
    return typeValidation;
  }

  const fileSize = calculateBase64FileSize(base64Data);
  const sizeValidation = validateFileSize(fileSize, maxSize);
  if (!sizeValidation.isValid) {
    return sizeValidation;
  }

  return {
    isValid: true,
    fileSize,
  };
};

export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

export const getFileExtension = (fileName: string): string => {
  return fileName.split('.').pop()?.toLowerCase() || '';
};

export const isImageFile = (mimeType: string): boolean => {
  return mimeType.startsWith('image/');
};

export const isDocumentFile = (mimeType: string): boolean => {
  const documentTypes = [
    'application/pdf',
    'text/plain',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  ];

  return documentTypes.includes(mimeType);
};
