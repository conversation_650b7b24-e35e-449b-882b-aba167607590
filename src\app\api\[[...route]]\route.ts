import { Hono } from 'hono';
import { handle } from 'hono/vercel';
import { logger } from 'hono/logger';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]/options';
import { createProject, getAllProjects, getProjectById, updateTask } from '@/routes/Project/route';
import { User } from '@/models/User';
import { Project } from '@/models/Project';
import { CacheService } from '@/services/Cache.service';

type Variables = {
  user?: {
    id: string;
    name?: string;
    email?: string;
    image?: string;
    organizationId?: string;
  };
};

const app = new Hono<{ Variables: Variables }>().basePath('/api');

app.use('*', logger());

// Middleware to inject user details with caching
app.use('*', async (c, next) => {
  try {
    const session = await getServerSession(authOptions);
    if (session?.user) {
      // Try to get user from cache first
      let cachedUser = null;
      if (session.user.id) {
        const userCacheKey = CacheService.generateKey('user', session.user.id);
        cachedUser = await CacheService.get(userCacheKey);
      }

      const userData = cachedUser || {
        id: session.user.id || '',
        name: session.user.name || '',
        email: session.user.email || '',
        image: session.user.image || '',
        organizationId: session.user.organizationId || '',
        systemRole: session.user.systemRole || 'User',
      };

      // Cache user data if not already cached (12 hours TTL)
      if (!cachedUser && session.user.id) {
        const userCacheKey = CacheService.generateKey('user', session.user.id);
        await CacheService.set(userCacheKey, userData, 12 * 60 * 60);
      }

      c.set('user', userData);
    }
  } catch (error: any) {
    console.error('Error in user middleware:', error);
    throw new Error(error.message);
  }
  await next();
});

const createTask = async c => {
  return c.json({ message: 'Task created successfully' });
};

const createProjectController = async c => {
  try {
    const body = await c.req.json();
    const user = c.get('user');
    if (!user) {
      return c.json({ error: 'User not authenticated' }, 401);
    }

    const result = await createProject(body, user.id, user.organizationId);
    if (result instanceof Error) {
      return c.json({ error: result.message }, 500);
    }

    // Invalidate user's projects cache
    const userProjectsCacheKey = CacheService.generateKey('projects', 'user', user.id);
    await CacheService.delete(userProjectsCacheKey);

    return c.json({ message: 'Project created successfully', project: result });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
};

const getAllProjectsByUserController = async c => {
  try {
    const user = c.get('user');
    if (!user) {
      return c.json({ error: 'User not authenticated' }, 401);
    }

    // Try to get from cache first
    const cacheKey = CacheService.generateKey('projects', 'user', user.id);
    const cachedProjects = await CacheService.get(cacheKey);

    if (cachedProjects) {
      return c.json({ projects: cachedProjects, cached: true });
    }

    // Cache miss - fetch from database
    const result = await getAllProjects(user.id);
    if (result instanceof Error) {
      return c.json({ error: result.message }, 500);
    }

    // Cache the results for 10 minutes
    await CacheService.setMedium(cacheKey, result);

    return c.json({ projects: result, cached: false });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
};

const getProjectByIdController = async (c: any) => {
  try {
    const user = c.get('user');
    if (!user) {
      return c.json({ error: 'User not authenticated' }, 401);
    }

    const projectId = c.req.param('projectId');

    // Try to get from cache first
    const cacheKey = CacheService.generateKey('project', 'detail', projectId);
    const cachedProject = await CacheService.get(cacheKey);

    if (cachedProject) {
      return c.json({ project: cachedProject, cached: true });
    }

    // Cache miss - fetch from database
    const result = await getProjectById(projectId, user.id);
    if (result instanceof Error) {
      return c.json({ error: result.message }, 500);
    }

    // Cache the result for 15 minutes
    await CacheService.set(cacheKey, result, 15 * 60);

    return c.json({ project: result, cached: false });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
};

const updateTaskController = async (c: any) => {
  try {
    const user = c.get('user');
    if (!user) {
      return c.json({ error: 'User not authenticated' }, 401);
    }

    const taskId = c.req.param('taskId');
    const data = await c.req.json();
    const result = await updateTask(taskId, data);

    if (result instanceof Error) {
      return c.json({ error: result.message }, 500);
    }

    // Invalidate related caches
    await CacheService.deleteByPattern(`task:*:${taskId}`);
    await CacheService.deleteByPattern(`tasks:*`);
    await CacheService.deleteByPattern(`projects:user:${user.id}`);

    return c.json({ task: result });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
};

app.post('/create-task', createTask);
app.post('/create-project', createProjectController);
app.get('/get-all-projects', getAllProjectsByUserController);
app.get('/get-project/:projectId', getProjectByIdController);
app.patch('/update-task-status/:taskId', updateTaskController);
app.get('/projects/:projectId/members', async c => {
  try {
    const user = c.get('user');
    if (!user) {
      return c.json({ error: 'User not authenticated' }, 401);
    }

    const projectId = c.req.param('projectId');

    // Try to get from cache first
    const cacheKey = CacheService.generateKey('project', 'members', projectId);
    const cachedMembers = await CacheService.get(cacheKey);

    if (cachedMembers) {
      return c.json({ members: cachedMembers, cached: true });
    }

    // Cache miss - fetch from database
    const project = await Project.findOne({ _id: projectId, organizationId: user.organizationId });
    if (!project) {
      return c.json({ error: 'Project not found' }, 404);
    }

    // Assume project.members is an array of user IDs
    const members = await User.find({ _id: { $in: project.members } }).select(
      'name email image _id'
    );

    // Cache the members for 5 minutes
    await CacheService.setShort(cacheKey, members);

    return c.json({ members, cached: false });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

export const GET = handle(app);
export const POST = handle(app);
export const DELETE = handle(app);
export const PUT = handle(app);
export const PATCH = handle(app);
