<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TaskMantra - You're Offline</title>
    <link rel="icon" href="/logo.png" type="image/png">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            color: #e2e8f0;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
            transition: background-color 0.3s, color 0.3s;
        }

        body.light-theme {
            background: linear-gradient(135deg, #e0f2f7 0%, #f0faff 100%);
            color: #333;
        }

        .container {
            text-align: center;
            max-width: 600px;
            width: 100%;
            background: rgba(30, 41, 59, 0.5);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 20px;
            padding: 3rem 2rem;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
            position: relative;
        }

        body.light-theme .container {
            background: rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(59, 130, 246, 0.1);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.1);
        }

        .theme-toggle {
            position: absolute;
            top: 20px;
            right: 20px;
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #e2e8f0;
            transition: transform 0.2s;
        }

        body.light-theme .theme-toggle {
            color: #333;
        }

        .theme-toggle:hover {
            transform: scale(1.1);
        }

        .icon {
            font-size: 5rem;
            margin-bottom: 1.5rem;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        h1 {
            color: #3b82f6;
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, #3b82f6, #8b5cf6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .subtitle {
            font-size: 1.2rem;
            color: #94a3b8;
            margin-bottom: 2rem;
            line-height: 1.6;
        }

        body.light-theme .subtitle {
            color: #64748b;
        }

        .features, .offline-task-section, .status-section, .help-section {
            background: rgba(15, 23, 42, 0.6);
            border-radius: 12px;
            padding: 1.5rem;
            margin: 1.5rem 0;
            border: 1px solid rgba(59, 130, 246, 0.1);
            text-align: left;
        }

        body.light-theme .features, body.light-theme .offline-task-section, body.light-theme .status-section, body.light-theme .help-section {
            background: rgba(240, 248, 255, 0.8);
            border: 1px solid rgba(59, 130, 246, 0.05);
            color: #333;
        }


        .features h3, .offline-task-section h3, .status-section h3, .help-section h3 {
            color: #60a5fa;
            font-size: 1.1rem;
            margin-bottom: 1rem;
            text-align: center;
        }

        body.light-theme .features h3, body.light-theme .offline-task-section h3, body.light-theme .status-section h3, body.light-theme .help-section h3 {
            color: #3b82f6;
        }

        .features ul {
            list-style: none;
            text-align: left;
        }

        .features li {
            color: #cbd5e1;
            margin-bottom: 0.5rem;
            padding-left: 1.5rem;
            position: relative;
        }

        body.light-theme .features li {
            color: #4a5568;
        }

        .features li::before {
            content: "✓";
            color: #10b981;
            font-weight: bold;
            position: absolute;
            left: 0;
        }

        .button-group {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 2rem;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
        }

        .btn-secondary {
            background: rgba(59, 130, 246, 0.1);
            color: #60a5fa;
            border: 1px solid rgba(59, 130, 246, 0.3);
        }

        body.light-theme .btn-secondary {
            background: rgba(59, 130, 246, 0.05);
            color: #3b82f6;
        }

        .btn-secondary:hover {
            background: rgba(59, 130, 246, 0.2);
            transform: translateY(-1px);
        }

        body.light-theme .btn-secondary:hover {
            background: rgba(59, 130, 246, 0.1);
        }

        .status {
            margin-top: 1.5rem;
            padding: 1rem;
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid rgba(239, 68, 68, 0.3);
            border-radius: 8px;
            color: #fca5a5;
            text-align: center;
        }

        .status.online {
            background: rgba(16, 185, 129, 0.1);
            border-color: rgba(16, 185, 129, 0.3);
            color: #6ee7b7;
        }

        body.light-theme .status {
            background: rgba(239, 68, 68, 0.05);
            border: 1px solid rgba(239, 68, 68, 0.1);
            color: #dc2626;
        }

        body.light-theme .status.online {
            background: rgba(16, 185, 129, 0.05);
            border-color: rgba(16, 185, 129, 0.1);
            color: #059669;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #cbd5e1;
            font-weight: 600;
        }

        body.light-theme .form-group label {
            color: #4a5568;
        }

        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 0.75rem;
            border-radius: 8px;
            border: 1px solid rgba(59, 130, 246, 0.3);
            background: rgba(15, 23, 42, 0.8);
            color: #e2e8f0;
            font-size: 1rem;
        }

        body.light-theme .form-group input,
        body.light-theme .form-group textarea {
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            border: 1px solid rgba(59, 130, 246, 0.1);
        }

        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
        }

        .task-list {
            list-style: none;
            margin-top: 1rem;
            max-height: 200px;
            overflow-y: auto;
            padding-right: 10px; /* For scrollbar space */
        }

        .task-list li {
            background: rgba(59, 130, 246, 0.1);
            padding: 0.75rem 1rem;
            border-radius: 8px;
            margin-bottom: 0.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.95rem;
            color: #cbd5e1;
        }

        body.light-theme .task-list li {
            background: rgba(59, 130, 246, 0.05);
            color: #4a5568;
        }

        .task-list li strong {
            color: #fff;
        }

        body.light-theme .task-list li strong {
            color: #333;
        }

        .task-list li .delete-btn {
            background: none;
            border: none;
            color: #ef4444;
            cursor: pointer;
            font-size: 1.1rem;
            margin-left: 10px;
            transition: color 0.2s;
        }

        .task-list li .delete-btn:hover {
            color: #dc2626;
        }

        .progress-bar-container {
            width: 100%;
            background-color: rgba(59, 130, 246, 0.2);
            border-radius: 5px;
            margin-top: 0.5rem;
            height: 10px;
            overflow: hidden;
        }

        .progress-bar {
            height: 100%;
            width: 0%;
            background-color: #3b82f6;
            border-radius: 5px;
            transition: width 0.5s ease-in-out;
        }

        details {
            margin-top: 1.5rem;
            border: 1px solid rgba(59, 130, 246, 0.1);
            border-radius: 8px;
            background: rgba(15, 23, 42, 0.6);
            padding: 1rem;
        }

        body.light-theme details {
            background: rgba(240, 248, 255, 0.8);
            border: 1px solid rgba(59, 130, 246, 0.05);
        }

        summary {
            font-weight: 600;
            color: #60a5fa;
            cursor: pointer;
            list-style: none;
            display: flex;
            align-items: center;
        }

        summary::before {
            content: '▼';
            margin-right: 0.5rem;
            transition: transform 0.2s;
        }

        details[open] summary::before {
            content: '▲';
            transform: rotate(0deg);
        }

        details p, details ul {
            margin-top: 1rem;
            color: #cbd5e1;
            line-height: 1.5;
        }
        body.light-theme details p, body.light-theme details ul {
            color: #4a5568;
        }

        details ul {
            padding-left: 20px;
        }

        #installPromptContainer {
            margin-top: 1.5rem;
            display: none;
        }

        #installButton {
            margin-top: 1rem;
        }

        .sw-status-indicator {
            font-weight: bold;
        }

        @media (max-width: 640px) {
            .container {
                padding: 2rem 1.5rem;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            .button-group {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <button id="themeToggle" class="theme-toggle">☀️</button>
        <div class="icon">📱</div>
        <h1>You're Offline</h1>
        <p class="subtitle">
            Don't worry! TaskMantra works offline too. You can still view your cached tasks and create new ones.
        </p>

        <div class="features">
            <h3>Available Offline Features:</h3>
            <ul>
                <li>View previously loaded tasks and content (if cached)</li>
                <li>Create new tasks (will sync when online)</li>
                <li>Browse cached projects</li>
                <li>Access your notes</li>
                <li>Use the calendar view</li>
                <li>Enhanced accessibility and keyboard navigation</li>
                <li>Dark/Light theme support</li>
            </ul>
        </div>

        <div class="offline-task-section">
            <h3>Create an Offline Task</h3>
            <form id="offlineTaskForm">
                <div class="form-group">
                    <label for="taskTitle">Task Title:</label>
                    <input type="text" id="taskTitle" name="title" required autocomplete="off">
                </div>
                <div class="form-group">
                    <label for="taskDescription">Description (optional):</label>
                    <textarea id="taskDescription" name="description" rows="3"></textarea>
                </div>
                <button type="submit" class="btn btn-primary">Add Offline Task</button>
            </form>

            <h4 style="margin-top: 1.5rem; text-align: center; color: #60a5fa;">Pending Offline Tasks</h4>
            <ul id="offlineTaskList" class="task-list">
                <!-- Offline tasks will be loaded here -->
            </ul>
        </div>

        <div class="status-section">
            <h3>Connection & Sync Status</h3>
            <div class="status" id="connectionStatus" role="status" aria-live="polite">
                🔴 You're currently offline
            </div>
            <div id="syncStatus" style="margin-top: 1rem; font-size: 0.9em; color: #94a3b8;">
                Offline tasks: <span id="pendingTaskCount">0</span> pending sync.
            </div>
            <div class="progress-bar-container">
                <div class="progress-bar" id="syncProgressBar"></div>
            </div>
            <div id="swStatus" style="margin-top: 1rem; font-size: 0.9em; color: #94a3b8;">
                Service Worker: <span class="sw-status-indicator" id="swRegStatus">Checking...</span>
            </div>
        </div>

        <div class="button-group">
            <button class="btn btn-primary" onclick="tryReconnect()">
                🔄 Try Again
            </button>
            <a href="/" class="btn btn-secondary">
                🏠 Go to Dashboard
            </a>
            <div id="installPromptContainer">
                <button id="installButton" class="btn btn-secondary">
                    🚀 Install App
                </button>
            </div>
        </div>

        <details class="help-section">
            <summary>Help & Troubleshooting Guide</summary>
            <p>If you're seeing this page, it means you're currently offline or experiencing network issues. Here are some tips:</p>
            <ul>
                <li><strong>Check your Internet Connection:</strong> Ensure your Wi-Fi or mobile data is turned on and working.</li>
                <li><strong>Refresh the Page:</strong> Sometimes a simple refresh can fix temporary network glitches.</li>
                <li><strong>Offline Features:</strong> You can continue to use TaskMantra's offline capabilities. Any tasks you create will be saved locally and synced automatically once you're back online.</li>
                <li><strong>Cached Content:</strong> Previously visited pages and data may still be accessible. Try navigating to other parts of the app to see if content loads.</li>
                <li><strong>Re-sync Issues:</strong> If your offline tasks aren't syncing after you're online, try refreshing the page or restarting your browser.</li>
                <li><strong>PWA Installation:</strong> For a better offline experience, consider installing TaskMantra to your device's home screen.</li>
            </ul>
            <p>We apologize for any inconvenience!</p>
        </details>
    </div>

    <script>
        let deferredPrompt;
        const OFFLINE_TASKS_KEY = 'offline_tasks';
        const ANALYTICS_EVENTS_KEY = 'offline_analytics';
        const THEME_KEY = 'app_theme';

        // --- Theme Management ---
        const themeToggle = document.getElementById('themeToggle');
        const body = document.body;

        function applyTheme(theme) {
            if (theme === 'light') {
                body.classList.add('light-theme');
                themeToggle.textContent = '🌙';
            } else {
                body.classList.remove('light-theme');
                themeToggle.textContent = '☀️';
            }
            localStorage.setItem(THEME_KEY, theme);
        }

        function toggleTheme() {
            const currentTheme = localStorage.getItem(THEME_KEY) || 'dark';
            applyTheme(currentTheme === 'dark' ? 'light' : 'dark');
        }

        // Initialize theme on load
        const storedTheme = localStorage.getItem(THEME_KEY) || (window.matchMedia('(prefers-color-scheme: light)').matches ? 'light' : 'dark');
        applyTheme(storedTheme);
        themeToggle.addEventListener('click', toggleTheme);


        // --- Connection Status and Reconnect Logic ---
        function updateConnectionStatus() {
            const statusEl = document.getElementById('connectionStatus');
            if (navigator.onLine) {
                statusEl.textContent = '🟢 Connection restored! Refreshing...';
                statusEl.className = 'status online';
                trackEvent('connection_restored', { from: 'offline_page' });
                setTimeout(() => window.location.reload(), 1500); // Give it a moment to stabilize
            } else {
                statusEl.textContent = '🔴 You're currently offline';
                statusEl.className = 'status';
                trackEvent('connection_lost', { from: 'offline_page' });
            }
        }

        function tryReconnect() {
            if (navigator.onLine) {
                window.location.reload();
            } else {
                updateConnectionStatus();
            }
        }

        window.addEventListener('online', updateConnectionStatus);
        window.addEventListener('offline', updateConnectionStatus);
        updateConnectionStatus(); // Initial check

        // --- Offline Task Management ---
        const offlineTaskForm = document.getElementById('offlineTaskForm');
        const offlineTaskList = document.getElementById('offlineTaskList');
        const pendingTaskCountSpan = document.getElementById('pendingTaskCount');
        const syncProgressBar = document.getElementById('syncProgressBar');

        function getOfflineTasks() {
            try {
                return JSON.parse(localStorage.getItem(OFFLINE_TASKS_KEY) || '[]');
            } catch (e) {
                console.error("Error parsing offline tasks from localStorage:", e);
                return [];
            }
        }

        function saveOfflineTasks(tasks) {
            localStorage.setItem(OFFLINE_TASKS_KEY, JSON.stringify(tasks));
            updatePendingTaskCount();
        }

        function displayOfflineTasks() {
            const tasks = getOfflineTasks();
            offlineTaskList.innerHTML = '';
            if (tasks.length === 0) {
                offlineTaskList.innerHTML = '<li>No tasks added offline yet.</li>';
                return;
            }
            tasks.forEach((task, index) => {
                const li = document.createElement('li');
                li.innerHTML = `
                    <div>
                        <strong>${task.title}</strong>
                        ${task.description ? `<br><small>${task.description}</small>` : ''}
                        <div style="font-size: 0.8em; color: #888;">Added offline on ${new Date(task.timestamp).toLocaleString()}</div>
                    </div>
                    <button class="delete-btn" data-index="${index}" aria-label="Delete task">✖</button>
                `;
                offlineTaskList.appendChild(li);
            });
        }

        function updatePendingTaskCount() {
            const tasks = getOfflineTasks();
            pendingTaskCountSpan.textContent = tasks.length;
            syncProgressBar.style.width = '0%'; // Reset progress bar
        }

        offlineTaskForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            const title = document.getElementById('taskTitle').value;
            const description = document.getElementById('taskDescription').value;

            if (title.trim()) {
                const tasks = getOfflineTasks();
                tasks.push({
                    id: Date.now(), // Simple unique ID
                    title,
                    description,
                    status: 'pending', // Custom status for offline tasks
                    timestamp: new Date().toISOString()
                });
                saveOfflineTasks(tasks);
                displayOfflineTasks();
                trackEvent('task_created_offline', { title });

                // Register background sync
                if ('serviceWorker' in navigator && 'SyncManager' in window) {
                    try {
                        const registration = await navigator.serviceWorker.ready;
                        await registration.sync.register('offline-queue-sync');
                        console.log('Background sync registered for offline tasks.');
                        // Provide immediate feedback to the user
                        const statusEl = document.getElementById('syncStatus');
                        statusEl.textContent = 'Offline tasks: ' + tasks.length + ' pending sync. Sync initiated!';
                        statusEl.style.color = '#6ee7b7';
                        setTimeout(() => {
                            statusEl.style.color = '#94a3b8'; // Reset color
                            updatePendingTaskCount();
                        }, 3000);

                    } catch (err) {
                        console.error('Background sync registration failed:', err);
                        trackEvent('background_sync_failed', { error: err.message });
                    }
                } else {
                    console.warn('Background Sync API not supported. Tasks will sync on next app load.');
                }

                document.getElementById('taskTitle').value = '';
                document.getElementById('taskDescription').value = '';
            }
        });

        offlineTaskList.addEventListener('click', (e) => {
            if (e.target.classList.contains('delete-btn')) {
                const indexToDelete = parseInt(e.target.dataset.index);
                let tasks = getOfflineTasks();
                tasks.splice(indexToDelete, 1);
                saveOfflineTasks(tasks);
                displayOfflineTasks();
                trackEvent('task_deleted_offline', { index: indexToDelete });
            }
        });

        displayOfflineTasks(); // Load and display tasks on page load


        // --- Analytics Tracking (simple localStorage based) ---
        function trackEvent(eventName, data = {}) {
            const events = JSON.parse(localStorage.getItem(ANALYTICS_EVENTS_KEY) || '[]');
            events.push({ eventName, data, timestamp: new Date().toISOString() });
            localStorage.setItem(ANALYTICS_EVENTS_KEY, JSON.stringify(events));

            // Attempt to sync analytics if online, or defer via background sync
            if (navigator.onLine) {
                 syncAnalytics(); // Try immediate sync
            } else if ('serviceWorker' in navigator && 'SyncManager' in window) {
                navigator.serviceWorker.ready.then(registration => {
                    registration.sync.register('offline-analytics-sync').catch(err => console.error("Analytics sync registration failed:", err));
                });
            }
        }

        async function syncAnalytics() {
            const events = JSON.parse(localStorage.getItem(ANALYTICS_EVENTS_KEY) || '[]');
            if (events.length === 0) return;

            console.log('Attempting to sync analytics events:', events);
            try {
                const response = await fetch('/api/analytics', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ events })
                });

                if (response.ok) {
                    localStorage.removeItem(ANALYTICS_EVENTS_KEY); // Clear only on success
                    console.log('Analytics events synced successfully.');
                } else {
                    console.error('Failed to sync analytics:', response.status, response.statusText);
                    // Keep events in localStorage to retry later
                }
            } catch (error) {
                console.error('Network error syncing analytics:', error);
                // Keep events in localStorage to retry later
            }
        }

        // --- PWA Install Prompt ---
        const installPromptContainer = document.getElementById('installPromptContainer');
        const installButton = document.getElementById('installButton');

        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault(); // Prevent the mini-infobar from appearing on mobile
            deferredPrompt = e;
            installPromptContainer.style.display = 'block'; // Show the install button
            trackEvent('pwa_install_prompt_shown');
        });

        installButton.addEventListener('click', async () => {
            if (deferredPrompt) {
                deferredPrompt.prompt();
                const { outcome } = await deferredPrompt.userChoice;
                console.log(`User response to the install prompt: ${outcome}`);
                trackEvent('pwa_install_choice', { outcome });
                deferredPrompt = null; // Clear the prompt
                installPromptContainer.style.display = 'none'; // Hide the button
            }
        });

        window.addEventListener('appinstalled', () => {
            console.log('TaskMantra PWA was installed!');
            installPromptContainer.style.display = 'none'; // Hide the button if app is installed
            trackEvent('pwa_installed');
        });

        // --- Service Worker Status Display ---
        const swRegStatus = document.getElementById('swRegStatus');

        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.getRegistration().then(registration => {
                if (registration) {
                    swRegStatus.textContent = 'Active';
                    swRegStatus.style.color = '#10b981';
                    console.log('Service Worker is active:', registration);

                    // Listen for messages from the service worker (e.g., sync completion)
                    navigator.serviceWorker.addEventListener('message', event => {
                        console.log('Message from SW:', event.data);
                        if (event.data && event.data.type === 'SYNC_COMPLETE') {
                            const tasks = getOfflineTasks();
                            if (tasks.length === 0) {
                                document.getElementById('syncStatus').textContent = 'All offline tasks synced successfully!';
                                document.getElementById('syncStatus').style.color = '#10b981';
                                syncProgressBar.style.width = '100%';
                                setTimeout(() => {
                                    document.getElementById('syncStatus').style.color = '#94a3b8';
                                    updatePendingTaskCount();
                                }, 3000);
                            } else {
                                document.getElementById('syncStatus').textContent = `Sync completed with ${event.data.failed} failures. ${tasks.length} tasks still pending.`;
                                document.getElementById('syncStatus').style.color = '#f59e0b';
                                syncProgressBar.style.width = '80%'; // Indicate partial success
                                setTimeout(() => {
                                    document.getElementById('syncStatus').style.color = '#94a3b8';
                                    updatePendingTaskCount();
                                }, 3000);
                            }
                            displayOfflineTasks(); // Refresh list after sync attempt
                        } else if (event.data && event.data.type === 'SW_UPDATED') {
                            // Prompt user to refresh for new SW
                            const updatePrompt = confirm('A new version of TaskMantra is available. Refresh now?');
                            if (updatePrompt) {
                                window.location.reload();
                            }
                        }
                    });

                    // Check if there's a waiting service worker (meaning an update is available)
                    if (registration.waiting) {
                        swRegStatus.textContent = 'Update available!';
                        swRegStatus.style.color = '#f59e0b';
                    }

                    // For initial load, if the SW is already active, tell it to check for updates
                    if (navigator.serviceWorker.controller) {
                        navigator.serviceWorker.controller.postMessage({ type: 'CHECK_FOR_UPDATES' });
                    }

                } else {
                    swRegStatus.textContent = 'Not Registered';
                    swRegStatus.style.color = '#ef4444';
                }
            }).catch(error => {
                console.error('Service Worker registration check failed:', error);
                swRegStatus.textContent = 'Error';
                swRegStatus.style.color = '#ef4444';
            });
        } else {
            swRegStatus.textContent = 'Not Supported';
            swRegStatus.style.color = '#ef4444';
        }
    </script>
</body>
</html>