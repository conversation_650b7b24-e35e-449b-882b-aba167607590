'use client';

import { useEffect } from 'react';
import OfflineIndicator from './OfflineIndicator';
import PWAUpdateNotification from './PWAUpdateNotification';

export default function PWAProvider() {
  useEffect(() => {
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker
        .register('/sw.js', { scope: '/' })
        .then(registration => {
          // eslint-disable-next-line no-console
          console.log('SW registered: ', registration);
        })
        .catch(registrationError => {
          // eslint-disable-next-line no-console
          console.log('SW registration failed: ', registrationError);
        });
    }
  }, []);

  return (
    <>
      <OfflineIndicator />
      <PWAUpdateNotification />
    </>
  );
}
