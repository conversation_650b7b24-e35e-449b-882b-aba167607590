import Redis from 'ioredis';

const redis = new Redis({
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379'),
  password: process.env.REDIS_PASSWORD,
  maxRetriesPerRequest: 3,
  lazyConnect: true,
  connectTimeout: 10000,
  commandTimeout: 5000,
});

// Handle connection events
redis.on('connect', () => {
  console.log('Redis connected successfully');
});

redis.on('error', err => {
  console.error('Redis connection error:', err);
});

redis.on('ready', () => {
  console.log('Redis is ready to accept commands');
});

// Graceful shutdown
process.on('SIGINT', async () => {
  await redis.quit();
  process.exit(0);
});

export default redis;

// Helper functions for common operations
export const RedisHelper = {
  // Generate cache key with consistent format
  generateKey: (prefix: string, ...parts: string[]) => {
    return `${prefix}:${parts.join(':')}`;
  },

  // Set with automatic JSON serialization
  setJSON: async (key: string, data: any, ttl?: number) => {
    const serialized = JSON.stringify(data);
    if (ttl) {
      return await redis.setex(key, ttl, serialized);
    }
    return await redis.set(key, serialized);
  },

  // Get with automatic JSON deserialization
  getJSON: async <T = any>(key: string): Promise<T | null> => {
    const data = await redis.get(key);
    if (!data) return null;
    try {
      return JSON.parse(data) as T;
    } catch (error) {
      console.error('Redis JSON parse error:', error);
      return null;
    }
  },

  // Delete multiple keys by pattern
  deleteByPattern: async (pattern: string) => {
    const keys = await redis.keys(pattern);
    if (keys.length > 0) {
      return await redis.del(...keys);
    }
    return 0;
  },

  // Check if Redis is connected
  isConnected: () => {
    return redis.status === 'ready';
  },
};
