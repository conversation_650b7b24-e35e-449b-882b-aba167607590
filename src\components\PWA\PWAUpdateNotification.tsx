'use client';

import React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { X, Download, Clock, AlertTriangle, CheckCircle, Info, RotateCcw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { Switch } from '@/components/ui/switch';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';

interface PWAUpdateNotificationProps {
  onUpdateApplied?: () => void;
  onUpdateSkipped?: () => void;
  onUpdateFailed?: (error: string) => void;
  position?: 'top-right' | 'top-center' | 'bottom-right' | 'bottom-center';
  autoUpdate?: boolean;
  showChangelog?: boolean;
  allowScheduling?: boolean;
  className?: string;
}

interface UpdateInfo {
  version: string;
  releaseDate: string;
  changelog: ChangelogItem[];
  critical: boolean;
  size?: string;
}

interface ChangelogItem {
  type: 'feature' | 'bugfix' | 'improvement' | 'security';
  title: string;
  description: string;
}

const PWAUpdateNotification: React.FC<PWAUpdateNotificationProps> = ({
  onUpdateApplied,
  onUpdateSkipped,
  onUpdateFailed,
  position = 'top-right',
  autoUpdate = false,
  showChangelog = true,
  allowScheduling = true,
  className = '',
}) => {
  const [updateAvailable, setUpdateAvailable] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [updateProgress, setUpdateProgress] = useState(0);
  const [updateStatus, setUpdateStatus] = useState<
    'idle' | 'downloading' | 'installing' | 'success' | 'error' | 'rollback'
  >('idle');
  const [errorMessage, setErrorMessage] = useState('');
  const [showDetails, setShowDetails] = useState(false);
  const [autoUpdateEnabled, setAutoUpdateEnabled] = useState(autoUpdate);
  const [scheduledTime, setScheduledTime] = useState<string>('');
  const [skipCount, setSkipCount] = useState(0);
  const [lastSkipTime, setLastSkipTime] = useState<number>(0);
  const [newServiceWorker, setNewServiceWorker] = useState<ServiceWorker | null>(null);
  const [currentUpdateInfo, setCurrentUpdateInfo] = useState<UpdateInfo | null>(null);
  const [isRollbackAvailable, setIsRollbackAvailable] = useState(false);
  const [updateId, setUpdateId] = useState<string>('');

  const updateTimerRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  const retryTimerRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  const progressIntervalRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  const scheduleOptions = useMemo(() => {
    return [
      { value: '5min', label: 'In 5 minutes', delay: 5 * 60 * 1000 },
      { value: '30min', label: 'In 30 minutes', delay: 30 * 60 * 1000 },
      { value: '1hour', label: 'In 1 hour', delay: 60 * 60 * 1000 },
      { value: 'tonight', label: 'Tonight (9 PM)', delay: getDelayUntilTime(21, 0) },
      { value: 'tomorrow', label: 'Tomorrow morning (8 AM)', delay: getDelayUntilTime(8, 0, true) },
    ];
  }, []);

  function getDelayUntilTime(hour: number, minute: number, nextDay: boolean = false): number {
    const now = new Date();
    const target = new Date();
    target.setHours(hour, minute, 0, 0);

    if (nextDay || target <= now) {
      target.setDate(target.getDate() + 1);
    }

    return target.getTime() - now.getTime();
  }

  // Fetch update info from server
  const fetchUpdateInfo = useCallback(async (): Promise<UpdateInfo | null> => {
    try {
      const response = await fetch('/api/app-version');
      if (!response.ok) throw new Error('Failed to fetch update info');
      return await response.json();
    } catch (error) {
      return {
        version: process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0',
        releaseDate: new Date().toLocaleDateString(),
        critical: false,
        changelog: [],
      };
    }
  }, []);

  // Analytics tracking
  const trackUpdateEvent = useCallback(
    (event: string, data: Record<string, any>) => {
      const events = JSON.parse(localStorage.getItem('pwa_update_analytics') || '[]');
      events.push({
        event,
        data: {
          ...data,
          timestamp: new Date().toISOString(),
          userAgent: navigator.userAgent,
          updateId,
        },
      });

      // Keep only last 100 events
      if (events.length > 100) {
        events.splice(0, events.length - 100);
      }

      localStorage.setItem('pwa_update_analytics', JSON.stringify(events));

      // Send to analytics service if available
      if (typeof window !== 'undefined' && (window as any).gtag) {
        (window as any).gtag('event', event, {
          custom_parameter_version: data.version,
          custom_parameter_critical: data.critical,
        });
      }
    },
    [updateId]
  );

  // Handle update installation
  const handleUpdate = useCallback(async () => {
    if (!newServiceWorker) return;

    setIsUpdating(true);
    setUpdateStatus('downloading');
    setUpdateProgress(0);

    try {
      // Simulate download progress
      progressIntervalRef.current = setInterval(() => {
        setUpdateProgress(prev => {
          if (prev >= 90) {
            if (progressIntervalRef.current) clearInterval(progressIntervalRef.current);
            return 90;
          }
          return prev + Math.random() * 10;
        });
      }, 200);

      // Apply the update
      setUpdateStatus('installing');
      setUpdateProgress(95);

      newServiceWorker.postMessage({ type: 'SKIP_WAITING' });
    } catch (error) {
      setUpdateStatus('error');
      setErrorMessage(error instanceof Error ? error.message : 'Update failed');
      onUpdateFailed?.(error instanceof Error ? error.message : 'Update failed');
    } finally {
      setIsUpdating(false);
      if (progressIntervalRef.current) {
        clearInterval(progressIntervalRef.current);
      }
    }
  }, [newServiceWorker, onUpdateFailed]);

  // Handle scheduled update
  const handleScheduledUpdate = useCallback(
    (scheduleValue: string) => {
      const option = scheduleOptions.find(opt => opt.value === scheduleValue);
      if (!option) return;

      setScheduledTime(scheduleValue);

      updateTimerRef.current = setTimeout(() => {
        handleUpdate();
        setScheduledTime('');
      }, option.delay);

      trackUpdateEvent('update_scheduled', {
        version: currentUpdateInfo?.version,
        delay: option.delay,
        label: option.label,
      });
    },
    [scheduleOptions, handleUpdate, currentUpdateInfo, trackUpdateEvent]
  );

  // Handle update skip
  const handleSkip = useCallback(() => {
    const now = Date.now();
    const newSkipCount = skipCount + 1;

    setSkipCount(newSkipCount);
    setLastSkipTime(now);
    setUpdateAvailable(false);

    localStorage.setItem('pwa_update_skip_count', newSkipCount.toString());
    localStorage.setItem('pwa_update_last_skip', now.toString());

    trackUpdateEvent('update_skipped', {
      version: currentUpdateInfo?.version,
      skipCount: newSkipCount,
      updateId,
    });

    onUpdateSkipped?.();
  }, [skipCount, currentUpdateInfo, updateId, onUpdateSkipped, trackUpdateEvent]);

  // Handle rollback
  const handleRollback = useCallback(async () => {
    if (!isRollbackAvailable) return;

    setUpdateStatus('rollback');

    try {
      // Clear cache and reload
      if ('caches' in window) {
        const cacheNames = await caches.keys();
        await Promise.all(cacheNames.map(name => caches.delete(name)));
      }

      localStorage.removeItem('pwa_rollback_available');
      localStorage.removeItem('pwa_previous_version');
      setIsRollbackAvailable(false);

      trackUpdateEvent('update_rollback', {
        version: currentUpdateInfo?.version,
      });

      window.location.reload();
    } catch (error) {
      setUpdateStatus('error');
      setErrorMessage('Rollback failed');
    }
  }, [isRollbackAvailable, currentUpdateInfo, trackUpdateEvent]);

  // Retry failed update
  const handleRetry = useCallback(() => {
    setUpdateStatus('idle');
    setErrorMessage('');
    setUpdateProgress(0);

    retryTimerRef.current = setTimeout(() => {
      handleUpdate();
    }, 2000);
  }, [handleUpdate]);

  // Load saved preferences
  useEffect(() => {
    const savedAutoUpdate = localStorage.getItem('pwa_auto_update');
    const savedSkipCount = localStorage.getItem('pwa_update_skip_count');
    const savedLastSkip = localStorage.getItem('pwa_update_last_skip');
    const savedRollbackAvailable = localStorage.getItem('pwa_rollback_available');

    if (savedAutoUpdate !== null) {
      setAutoUpdateEnabled(savedAutoUpdate === 'true');
    }
    if (savedSkipCount) {
      setSkipCount(parseInt(savedSkipCount));
    }
    if (savedLastSkip) {
      setLastSkipTime(parseInt(savedLastSkip));
    }
    if (savedRollbackAvailable === 'true') {
      setIsRollbackAvailable(true);
    }
  }, []);

  // Service Worker update detection
  useEffect(() => {
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.getRegistration().then(async registration => {
        if (registration) {
          // Check for waiting service worker (update available)
          if (registration.waiting) {
            const updateInfo = await fetchUpdateInfo();
            if (updateInfo) {
              setNewServiceWorker(registration.waiting);
              setUpdateAvailable(true);
              setCurrentUpdateInfo(updateInfo);
              setUpdateId(`update-${Date.now()}`);
            }
          }

          // Listen for new service worker installations
          registration.addEventListener('updatefound', () => {
            const newWorker = registration.installing;
            if (newWorker) {
              newWorker.addEventListener('statechange', async () => {
                if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                  const updateInfo = await fetchUpdateInfo();
                  if (updateInfo) {
                    setNewServiceWorker(newWorker);
                    setUpdateAvailable(true);
                    setCurrentUpdateInfo(updateInfo);
                    setUpdateId(`update-${Date.now()}`);

                    // Track update availability
                    trackUpdateEvent('update_available', {
                      version: updateInfo.version,
                      critical: updateInfo.critical,
                    });
                  }
                }
              });
            }
          });
        }
      });

      // Listen for service worker controller changes
      navigator.serviceWorker.addEventListener('controllerchange', () => {
        if (updateStatus === 'installing') {
          setUpdateStatus('success');
          setUpdateProgress(100);

          // Store rollback information
          localStorage.setItem('pwa_rollback_available', 'true');
          localStorage.setItem('pwa_previous_version', currentUpdateInfo?.version || 'unknown');
          setIsRollbackAvailable(true);

          trackUpdateEvent('update_applied', {
            version: currentUpdateInfo?.version,
            method: autoUpdateEnabled ? 'automatic' : 'manual',
          });

          onUpdateApplied?.();

          // Hide notification after success
          setTimeout(() => {
            setUpdateAvailable(false);
            setUpdateStatus('idle');
          }, 3000);
        }
      });

      // Listen for service worker messages
      navigator.serviceWorker.addEventListener('message', event => {
        if (event.data && event.data.type === 'UPDATE_PROGRESS') {
          setUpdateProgress(event.data.progress);
        } else if (event.data && event.data.type === 'UPDATE_ERROR') {
          setUpdateStatus('error');
          setErrorMessage(event.data.message);
          onUpdateFailed?.(event.data.message);
        }
      });
    }
  }, [
    fetchUpdateInfo,
    trackUpdateEvent,
    updateStatus,
    currentUpdateInfo,
    autoUpdateEnabled,
    onUpdateApplied,
    onUpdateFailed,
  ]);

  // Auto-update logic
  useEffect(() => {
    if (updateAvailable && autoUpdateEnabled && currentUpdateInfo && !currentUpdateInfo.critical) {
      // Delay auto-update for non-critical updates
      const delay = currentUpdateInfo.critical ? 5000 : 30000;
      updateTimerRef.current = setTimeout(() => {
        handleUpdate();
      }, delay);
    }

    return () => {
      if (updateTimerRef.current) {
        clearTimeout(updateTimerRef.current);
      }
    };
  }, [updateAvailable, autoUpdateEnabled, currentUpdateInfo, handleUpdate]);

  // Cleanup timers
  useEffect(() => {
    return () => {
      if (updateTimerRef.current) clearTimeout(updateTimerRef.current);
      if (retryTimerRef.current) clearTimeout(retryTimerRef.current);
      if (progressIntervalRef.current) clearInterval(progressIntervalRef.current);
    };
  }, []);

  // Position classes
  const positionClasses = {
    'top-right': 'top-4 right-4',
    'top-center': 'top-4 left-1/2 transform -translate-x-1/2',
    'bottom-right': 'bottom-4 right-4',
    'bottom-center': 'bottom-4 left-1/2 transform -translate-x-1/2',
  };

  if (!updateAvailable) return null;

  return (
    <div className={`fixed ${positionClasses[position]} z-50 max-w-sm ${className}`}>
      <Card className="shadow-lg border-2">
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div>
              <CardTitle className="text-lg flex items-center gap-2">
                {currentUpdateInfo?.critical ? (
                  <AlertTriangle className="w-5 h-5 text-red-500" />
                ) : (
                  <Download className="w-5 h-5 text-blue-500" />
                )}
                Update Available
                {currentUpdateInfo?.critical && (
                  <Badge variant="destructive" className="ml-2">
                    Critical
                  </Badge>
                )}
              </CardTitle>
              <CardDescription>
                Version {currentUpdateInfo?.version} • {currentUpdateInfo?.size}
              </CardDescription>
            </div>
            <Button variant="ghost" size="sm" onClick={handleSkip} disabled={isUpdating}>
              <X className="w-4 h-4" />
            </Button>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {updateStatus === 'error' && (
            <Alert variant="destructive">
              <AlertTriangle className="w-4 h-4" />
              <AlertDescription>
                {errorMessage}
                <Button variant="link" size="sm" onClick={handleRetry} className="ml-2 p-0 h-auto">
                  Try again
                </Button>
              </AlertDescription>
            </Alert>
          )}

          {updateStatus === 'success' && (
            <Alert>
              <CheckCircle className="w-4 h-4" />
              <AlertDescription>
                Update applied successfully! The app will reload shortly.
              </AlertDescription>
            </Alert>
          )}

          {(updateStatus === 'downloading' || updateStatus === 'installing') && (
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>{updateStatus === 'downloading' ? 'Downloading...' : 'Installing...'}</span>
                <span>{Math.round(updateProgress)}%</span>
              </div>
              <Progress value={updateProgress} className="h-2" />
            </div>
          )}

          {showChangelog &&
            currentUpdateInfo?.changelog &&
            currentUpdateInfo.changelog.length > 0 && (
              <div className="space-y-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowDetails(!showDetails)}
                  className="p-0 h-auto font-normal"
                >
                  <Info className="w-4 h-4 mr-1" />
                  {showDetails ? 'Hide' : 'Show'} what's new
                </Button>

                {showDetails && (
                  <div className="space-y-2 text-sm">
                    {currentUpdateInfo.changelog.map((item, index) => (
                      <div key={index} className="flex gap-2">
                        <Badge variant="outline" className="text-xs">
                          {item.type}
                        </Badge>
                        <div>
                          <div className="font-medium">{item.title}</div>
                          <div className="text-muted-foreground">{item.description}</div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}

          <Separator />

          <div className="space-y-3">
            {allowScheduling && !currentUpdateInfo?.critical && (
              <div className="space-y-2">
                <label className="text-sm font-medium">Schedule update:</label>
                <Select value={scheduledTime} onValueChange={handleScheduledUpdate}>
                  <SelectTrigger>
                    <SelectValue placeholder="Choose when to update" />
                  </SelectTrigger>
                  <SelectContent>
                    {scheduleOptions.map(option => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Switch
                  id="auto-update"
                  checked={autoUpdateEnabled}
                  onCheckedChange={checked => {
                    setAutoUpdateEnabled(checked);
                    localStorage.setItem('pwa_auto_update', checked.toString());
                  }}
                />
                <label htmlFor="auto-update" className="text-sm">
                  Auto-update
                </label>
              </div>

              {isRollbackAvailable && (
                <Button variant="outline" size="sm" onClick={handleRollback} disabled={isUpdating}>
                  <RotateCcw className="w-4 h-4 mr-1" />
                  Rollback
                </Button>
              )}
            </div>

            <div className="flex gap-2">
              {!currentUpdateInfo?.critical && (
                <Button
                  variant="outline"
                  onClick={handleSkip}
                  disabled={isUpdating}
                  className="flex-1"
                >
                  {skipCount > 0 ? `Skip (${skipCount})` : 'Skip'}
                </Button>
              )}

              <Button
                onClick={handleUpdate}
                disabled={isUpdating || updateStatus === 'success'}
                className="flex-1"
              >
                {isUpdating ? (
                  <>
                    <Clock className="w-4 h-4 mr-2 animate-spin" />
                    {updateStatus === 'downloading' ? 'Downloading' : 'Installing'}
                  </>
                ) : (
                  <>
                    <Download className="w-4 h-4 mr-2" />
                    {currentUpdateInfo?.critical ? 'Install Now' : 'Update'}
                  </>
                )}
              </Button>
            </div>

            {scheduledTime && (
              <div className="text-xs text-muted-foreground text-center">
                Update scheduled: {scheduleOptions.find(opt => opt.value === scheduledTime)?.label}
                <Button
                  variant="link"
                  size="sm"
                  onClick={() => {
                    if (updateTimerRef.current) clearTimeout(updateTimerRef.current);
                    setScheduledTime('');
                  }}
                  className="ml-2 p-0 h-auto text-xs"
                >
                  Cancel
                </Button>
              </div>
            )}

            {lastSkipTime > 0 && (
              <div className="text-xs text-muted-foreground text-center">
                Last skipped: {new Date(lastSkipTime).toLocaleString()}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default PWAUpdateNotification;
