'use client';

import { memo } from 'react';
import { <PERSON><PERSON>, Position, type NodeProps } from 'reactflow';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Paperclip, ExternalLink, File, ImageIcon, Video } from 'lucide-react';

interface ResourceNodeData {
  title: string;
  type: 'document' | 'image' | 'video' | 'link';
  url: string;
}

export const ResourceNode = memo(({ data, selected }: NodeProps<ResourceNodeData>) => {
  const getIcon = () => {
    switch (data.type) {
      case 'document':
        return File;
      case 'image':
        return ImageIcon;
      case 'video':
        return Video;
      case 'link':
        return ExternalLink;
      default:
        return Paperclip;
    }
  };

  const Icon = getIcon();

  const typeColors = {
    document: 'bg-blue-100 text-blue-800',
    image: 'bg-green-100 text-green-800',
    video: 'bg-red-100 text-red-800',
    link: 'bg-purple-100 text-purple-800',
  };

  return (
    <Card
      className={`min-w-[200px] bg-gradient-to-br from-orange-50 to-red-50 border-orange-200 shadow-lg transition-all duration-200 ${
        selected ? 'ring-2 ring-orange-500 shadow-xl scale-105' : 'hover:shadow-xl hover:scale-102'
      }`}
    >
      <Handle
        type="target"
        position={Position.Top}
        className="w-3 h-3 bg-orange-500 border-2 border-white"
      />

      <div className="p-4">
        <div className="flex items-center space-x-2 mb-3">
          <Icon className="h-5 w-5 text-orange-600" />
          <h3 className="font-semibold text-gray-900 text-sm">{data.title}</h3>
        </div>

        <div className="flex items-center justify-between">
          <Badge variant="secondary" className={`text-xs ${typeColors[data.type]}`}>
            {data.type}
          </Badge>

          {data.url && <ExternalLink className="h-3 w-3 text-gray-400" />}
        </div>
      </div>

      <Handle
        type="source"
        position={Position.Bottom}
        className="w-3 h-3 bg-orange-500 border-2 border-white"
      />
    </Card>
  );
});

ResourceNode.displayName = 'ResourceNode';
