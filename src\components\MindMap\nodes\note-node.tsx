'use client';

import { memo } from 'react';
import { Handle, Position, type NodeProps } from 'reactflow';
import { Card } from '@/components/ui/card';
import { FileText, Edit3 } from 'lucide-react';

interface NoteNodeData {
  title: string;
  content: string;
}

export const NoteNode = memo(({ data, selected }: NodeProps<NoteNodeData>) => {
  return (
    <Card
      className={`min-w-[250px] max-w-[300px] bg-gradient-to-br from-purple-50 to-violet-50 border-purple-200 shadow-lg transition-all duration-200 ${
        selected ? 'ring-2 ring-purple-500 shadow-xl scale-105' : 'hover:shadow-xl hover:scale-102'
      }`}
    >
      <Handle
        type="target"
        position={Position.Top}
        className="w-3 h-3 bg-purple-500 border-2 border-white"
      />

      <div className="p-4">
        <div className="flex items-center space-x-2 mb-3">
          <FileText className="h-5 w-5 text-purple-600" />
          <h3 className="font-semibold text-gray-900 text-sm">{data.title}</h3>
          <Edit3 className="h-3 w-3 text-gray-400 ml-auto" />
        </div>

        <div className="bg-white/70 rounded-md p-3 border border-purple-100">
          <p className="text-sm text-gray-700 line-clamp-4">{data.content}</p>
        </div>
      </div>

      <Handle
        type="source"
        position={Position.Bottom}
        className="w-3 h-3 bg-purple-500 border-2 border-white"
      />
    </Card>
  );
});

NoteNode.displayName = 'NoteNode';
