import { Hono } from 'hono';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/options';
import { connectDB } from '@/Utility/db';
import { Organization } from '@/models/organization';
import { StorageUsage } from '@/models/StorageUsage';
import { StoragePurchase } from '@/models/StoragePurchase';
import { Subscription } from '@/models/Subscription';
import { logger } from 'hono/logger';
import { handle } from 'hono/vercel';
import { User } from '@/models/User';

export interface StorageInfo {
  organizationId: string;
  totalLimit: number;
  totalUsed: number;
  availableStorage: number;
  usagePercentage: number;
  isOverLimit: boolean;
  freeStorageLimit: number;
  purchasedStorage: number;
}

export interface StorageUsageAnalytics {
  organizationId: string;
  period: string;
  startDate: Date;
  endDate: Date;
  totalUsage: number;
  averageDailyUsage: number;
  growthRate: number;
  topFileTypes: { type: string; size: number; count: number }[];
  topUsers: { userId: string; userName: string; usage: number }[];
  usageByResource: { resourceType: string; usage: number }[];
  predictions: {
    nextMonth: number;
    nextQuarter: number;
    nextYear: number;
  };
}

export interface FileUploadInfo {
  fileName: string;
  fileSize: number;
  fileType: string;
  uploadedBy: string;
  resourceType: 'project' | 'task' | 'feedback' | 'organization' | 'user';
  resourceId: string;
}

export class StorageService {
  private static readonly FREE_STORAGE_LIMIT = 52428800; // 50MB in bytes
  private static readonly PRICE_PER_MB = 2; // 2 rupees per MB
  private static readonly DEFAULT_GROWTH_RATE = 0.1; // 10% monthly growth rate

  static async calculateStorageUsage(organizationId: string): Promise<number> {
    await connectDB();
    const result = await StorageUsage.aggregate([
      {
        $match: {
          organizationId: organizationId,
          isDeleted: false,
        },
      },
      {
        $group: {
          _id: null,
          totalSize: { $sum: '$fileSize' },
        },
      },
    ]);

    return result.length > 0 ? result[0].totalSize : 0;
  }

  static async getStorageInfo(organizationId: string): Promise<StorageInfo> {
    await connectDB();

    const [organization, subscription, totalUsed] = await Promise.all([
      Organization.findById(organizationId),
      Subscription.findOne({ organizationId }),
      this.calculateStorageUsage(organizationId),
    ]);

    if (!organization) {
      throw new Error('Organization not found');
    }

    const freeStorageLimit = this.FREE_STORAGE_LIMIT;
    const purchasedStorage = subscription?.totalStoragePurchased || 0;
    const totalLimit = freeStorageLimit + purchasedStorage;
    const availableStorage = Math.max(0, totalLimit - totalUsed);
    const usagePercentage = totalLimit > 0 ? (totalUsed / totalLimit) * 100 : 0;
    const isOverLimit = totalUsed > totalLimit;

    await Organization.findByIdAndUpdate(organizationId, {
      storageUsed: totalUsed,
      storageLimit: totalLimit,
      lastStorageCalculated: new Date(),
    });

    return {
      organizationId,
      totalLimit,
      totalUsed,
      availableStorage,
      usagePercentage,
      isOverLimit,
      freeStorageLimit,
      purchasedStorage,
    };
  }

  static async canUploadFile(
    organizationId: string,
    fileSize: number
  ): Promise<{
    canUpload: boolean;
    reason?: string;
    storageInfo: StorageInfo;
  }> {
    const storageInfo = await this.getStorageInfo(organizationId);

    if (storageInfo.availableStorage >= fileSize) {
      return { canUpload: true, storageInfo };
    }

    const requiredStorage = fileSize - storageInfo.availableStorage;
    const requiredMB = Math.ceil(requiredStorage / (1024 * 1024));

    return {
      canUpload: false,
      reason: `Insufficient storage. You need ${requiredMB}MB more storage to upload this file.`,
      storageInfo,
    };
  }

  static async recordFileUpload(
    organizationId: string,
    cloudinaryResult: any,
    fileInfo: FileUploadInfo
  ): Promise<void> {
    await connectDB();
    const fileSize = cloudinaryResult.bytes || 0;

    await StorageUsage.create({
      organizationId,
      fileId: cloudinaryResult.public_id,
      fileName: fileInfo.fileName,
      fileSize,
      fileType: fileInfo.fileType,
      uploadedBy: fileInfo.uploadedBy,
      resourceType: fileInfo.resourceType,
      resourceId: fileInfo.resourceId,
      cloudinaryUrl: cloudinaryResult.secure_url,
    });

    // Update organization's storage usage
    await this.getStorageInfo(organizationId);
  }
  static async recordFileDeletion(organizationId: string, fileId: string): Promise<void> {
    await connectDB();

    await StorageUsage.findOneAndUpdate(
      { organizationId, fileId, isDeleted: false },
      {
        isDeleted: true,
        deletedAt: new Date(),
      }
    );
    await this.getStorageInfo(organizationId);
  }
  static calculateStoragePrice(storageMB: number): {
    storageMB: number;
    pricePerMB: number;
    totalPrice: number;
    storageBytes: number;
  } {
    return {
      storageMB,
      pricePerMB: this.PRICE_PER_MB,
      totalPrice: storageMB * this.PRICE_PER_MB,
      storageBytes: storageMB * 1024 * 1024,
    };
  }
  static async purchaseStorage(
    organizationId: string,
    purchasedBy: string,
    storageMB: number,
    paymentDetails: {
      paymentId: string;
      orderId: string;
      paymentMethod: string;
    }
  ): Promise<void> {
    await connectDB();

    const priceInfo = this.calculateStoragePrice(storageMB);

    await StoragePurchase.create({
      organizationId,
      purchasedBy,
      storageAmount: priceInfo.storageBytes,
      pricePerMB: priceInfo.pricePerMB,
      totalPrice: priceInfo.totalPrice,
      paymentStatus: 'completed',
      paymentMethod: paymentDetails.paymentMethod,
      paymentId: paymentDetails.paymentId,
      orderId: paymentDetails.orderId,
      purchaseType: 'lifetime',
      description: `Purchased ${storageMB}MB additional storage`,
    });

    // Update or create subscription
    await Subscription.findOneAndUpdate(
      { organizationId },
      {
        $inc: { totalStoragePurchased: priceInfo.storageBytes },
        status: 'active',
      },
      { upsert: true, new: true }
    );

    // Update organization storage limit
    await this.getStorageInfo(organizationId);
  }

  static async getStorageBreakdown(organizationId: string) {
    await connectDB();

    const breakdown = await StorageUsage.aggregate([
      {
        $match: {
          organizationId: organizationId,
          isDeleted: false,
        },
      },
      {
        $group: {
          _id: '$resourceType',
          totalSize: { $sum: '$fileSize' },
          fileCount: { $sum: 1 },
        },
      },
      {
        $sort: { totalSize: -1 },
      },
    ]);

    return breakdown;
  }

  static async getPurchaseHistory(organizationId: string) {
    await connectDB();

    return await StoragePurchase.find({ organizationId })
      .populate('purchasedBy', 'name email')
      .sort({ createdAt: -1 });
  }

  static async getStorageUsageHistory(organizationId: string, months: number = 6): Promise<any[]> {
    await connectDB();

    // Calculate start date (months ago from now)
    const startDate = new Date();
    startDate.setMonth(startDate.getMonth() - months);

    // Get historical usage data grouped by month
    const usageHistory = await StorageUsage.aggregate([
      {
        $match: {
          organizationId: organizationId,
          createdAt: { $gte: startDate },
          isDeleted: false,
        },
      },
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' },
          },
          totalSize: { $sum: '$fileSize' },
          count: { $sum: 1 },
        },
      },
      { $sort: { '_id.year': 1, '_id.month': 1 } },
    ]);

    return usageHistory;
  }

  static calculateGrowthRate(usageHistory: any[]): number {
    // If we don't have enough history, return default growth rate
    if (usageHistory.length < 2) {
      return this.DEFAULT_GROWTH_RATE;
    }

    // Calculate growth rate based on first and last month in history
    const firstMonth = usageHistory[0];
    const lastMonth = usageHistory[usageHistory.length - 1];

    // If first month has no data, return default
    if (firstMonth.totalSize === 0) {
      return this.DEFAULT_GROWTH_RATE;
    }

    // Calculate monthly growth rate
    const growthRate = (lastMonth.totalSize - firstMonth.totalSize) / firstMonth.totalSize;
    const monthsDiff =
      lastMonth._id.year * 12 +
      lastMonth._id.month -
      (firstMonth._id.year * 12 + firstMonth._id.month);

    // Calculate average monthly growth rate
    const monthlyGrowthRate = monthsDiff > 0 ? growthRate / monthsDiff : this.DEFAULT_GROWTH_RATE;

    // Cap growth rate between 0% and 50% per month to avoid extreme projections
    return Math.max(0, Math.min(0.5, monthlyGrowthRate));
  }

  static formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  static generateRecommendations(forecastData: any, currentUsage: number): any {
    // Simple recommendation based on projected usage
    const projectedGrowth = forecastData.projectedUsage - currentUsage;
    const growthPercentage = (projectedGrowth / currentUsage) * 100;

    if (growthPercentage > 50) {
      return {
        type: 'critical',
        message: 'Storage usage is growing rapidly. Consider upgrading your storage plan soon.',
        actions: ['upgrade_plan', 'cleanup_files'],
      };
    } else if (growthPercentage > 20) {
      return {
        type: 'warning',
        message: 'Storage usage is growing steadily. Monitor your usage and plan accordingly.',
        actions: ['monitor_usage', 'cleanup_files'],
      };
    } else {
      return {
        type: 'info',
        message: 'Storage usage is growing at a sustainable rate.',
        actions: ['monitor_usage'],
      };
    }
  }

  static async getStorageAnalytics(
    organizationId: string,
    period: 'day' | 'week' | 'month' | 'year' = 'month'
  ): Promise<StorageUsageAnalytics> {
    await connectDB();

    // Calculate date range based on period
    const endDate = new Date();
    const startDate = new Date();

    switch (period) {
      case 'day':
        startDate.setDate(startDate.getDate() - 1);
        break;
      case 'week':
        startDate.setDate(startDate.getDate() - 7);
        break;
      case 'month':
        startDate.setMonth(startDate.getMonth() - 1);
        break;
      case 'year':
        startDate.setFullYear(startDate.getFullYear() - 1);
        break;
    }

    // Get usage data for the period
    const usageData = await StorageUsage.aggregate([
      {
        $match: {
          organizationId: organizationId,
          isDeleted: false,
          createdAt: { $gte: startDate, $lte: endDate },
        },
      },
      {
        $group: {
          _id: null,
          totalUsage: { $sum: '$fileSize' },
          count: { $sum: 1 },
        },
      },
    ]);

    // Get top file types
    const topFileTypes = await StorageUsage.aggregate([
      {
        $match: {
          organizationId: organizationId,
          isDeleted: false,
        },
      },
      {
        $group: {
          _id: '$fileType',
          size: { $sum: '$fileSize' },
          count: { $sum: 1 },
        },
      },
      { $sort: { size: -1 } },
      { $limit: 5 },
    ]);

    // Get top users
    const topUsers = await StorageUsage.aggregate([
      {
        $match: {
          organizationId: organizationId,
          isDeleted: false,
        },
      },
      {
        $group: {
          _id: '$uploadedBy',
          usage: { $sum: '$fileSize' },
        },
      },
      { $sort: { usage: -1 } },
      { $limit: 5 },
    ]);

    // Get usage by resource type
    const usageByResource = await StorageUsage.aggregate([
      {
        $match: {
          organizationId: organizationId,
          isDeleted: false,
        },
      },
      {
        $group: {
          _id: '$resourceType',
          usage: { $sum: '$fileSize' },
        },
      },
      { $sort: { usage: -1 } },
    ]);

    // Calculate average daily usage
    const daysDiff = Math.max(
      1,
      Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24))
    );
    const totalUsage = usageData.length > 0 ? usageData[0].totalUsage : 0;
    const averageDailyUsage = totalUsage / daysDiff;

    // Calculate growth rate
    const usageHistory = await this.getStorageUsageHistory(organizationId, 6);
    const growthRate = this.calculateGrowthRate(usageHistory);

    // Generate predictions
    const predictions = {
      nextMonth: totalUsage * (1 + growthRate),
      nextQuarter: totalUsage * (1 + growthRate * 3),
      nextYear: totalUsage * (1 + growthRate * 12),
    };

    return {
      organizationId,
      period,
      startDate,
      endDate,
      totalUsage,
      averageDailyUsage,
      growthRate,
      topFileTypes: topFileTypes.map(item => ({
        type: item._id || 'Unknown',
        size: item.size,
        count: item.count,
      })),
      topUsers: await Promise.all(
        topUsers.map(async item => {
          try {
            const user = await User.findById(item._id).select('name');
            return {
              userId: item._id,
              userName: user?.name || 'Unknown User',
              usage: item.usage,
            };
          } catch (error) {
            return {
              userId: item._id || 'unknown',
              userName: 'Unknown User',
              usage: item.usage,
            };
          }
        })
      ),
      usageByResource: usageByResource.map(item => ({
        resourceType: item._id || 'Unknown',
        usage: item.usage,
      })),
      predictions,
    };
  }

  static async getStorageForecast(organizationId: string, months: number = 6): Promise<any> {
    await connectDB();

    // Get historical usage data
    const usageHistory = await this.getStorageUsageHistory(organizationId, months);

    // Calculate growth rate based on historical data
    const growthRate = this.calculateGrowthRate(usageHistory);

    // Get current usage
    const currentUsage = await this.calculateStorageUsage(organizationId);

    // Import StorageTier from Subscription service
    const { SubscriptionService } = await import('@/services/Subscription.service');

    // Generate forecast data
    const forecastData: Array<{
      date: Date;
      projectedUsage: number;
      projectedUsageFormatted: string;
      recommendedTier: any;
    }> = [];
    let projectedUsage = currentUsage;

    for (let i = 1; i <= months; i++) {
      const date = new Date();
      date.setMonth(date.getMonth() + i);

      // Apply growth rate to project usage
      projectedUsage = projectedUsage * (1 + growthRate);

      // Calculate recommended tier
      const recommendedTier = SubscriptionService.calculateRecommendedTier(projectedUsage);

      forecastData.push({
        date,
        projectedUsage,
        projectedUsageFormatted: this.formatBytes(projectedUsage),
        recommendedTier,
      });
    }

    return {
      growthRate,
      forecastData,
      recommendations: {
        shortTerm: this.generateRecommendations(forecastData[0], currentUsage),
        longTerm: this.generateRecommendations(forecastData[forecastData.length - 1], currentUsage),
      },
    };
  }
}

type Variables = {
  user?: {
    id: string;
    name?: string;
    email?: string;
    image?: string;
    organizationId?: string;
  };
};

const app = new Hono<{ Variables: Variables }>().basePath('/api/storage');

app.use('*', logger());

app.use('*', async (c, next) => {
  try {
    const session = await getServerSession(authOptions);

    if (session?.user) {
      const userData = {
        id: session.user.id || '',
        name: session.user.name || '',
        email: session.user.email || '',
        image: session.user.image || '',
        organizationId: session.user.organizationId || '',
      };
      c.set('user', userData);
    }
  } catch (error: any) {
    throw new Error(error.message);
  }
  await next();
});

app.get('/info', async c => {
  try {
    const user = c.get('user');

    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    if (!user.organizationId) {
      return c.json({ error: 'User not associated with any organization' }, 400);
    }

    const storageInfo = await StorageService.getStorageInfo(user.organizationId.toString());

    return c.json({
      success: true,
      data: storageInfo,
    });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

app.get('/breakdown', async c => {
  try {
    const user = c.get('user');

    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    if (!user.organizationId) {
      return c.json({ error: 'User not associated with any organization' }, 400);
    }

    const breakdown = await StorageService.getStorageBreakdown(user.organizationId.toString());

    return c.json({
      success: true,
      data: breakdown,
    });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

app.post('/calculate-price', async c => {
  try {
    const { storageMB } = await c.req.json();

    if (!storageMB || storageMB <= 0) {
      return c.json({ error: 'Invalid storage amount' }, 400);
    }

    const priceInfo = StorageService.calculateStoragePrice(storageMB);

    return c.json({
      success: true,
      data: priceInfo,
    });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

app.post('/purchase', async c => {
  try {
    const user = c.get('user');
    const { storageMB, paymentId, orderId, paymentMethod } = await c.req.json();

    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    if (!user.organizationId) {
      return c.json({ error: 'User not associated with any organization' }, 400);
    }
    const organization = await Organization.findById(user.organizationId);
    if (!organization || organization.ownerId.toString() !== user.id.toString()) {
      return c.json({ error: 'Only organization owner can purchase storage' }, 403);
    }

    if (!storageMB || storageMB <= 0) {
      return c.json({ error: 'Invalid storage amount' }, 400);
    }

    if (!paymentId || !orderId) {
      return c.json({ error: 'Payment details are required' }, 400);
    }

    await StorageService.purchaseStorage(
      user.organizationId.toString(),
      user.id.toString(),
      storageMB,
      {
        paymentId,
        orderId,
        paymentMethod: paymentMethod || 'razorpay',
      }
    );

    const updatedStorageInfo = await StorageService.getStorageInfo(user.organizationId.toString());

    return c.json({
      success: true,
      message: `Successfully purchased ${storageMB}MB storage`,
      data: updatedStorageInfo,
    });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

app.get('/purchases', async c => {
  try {
    const user = c.get('user');

    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    if (!user.organizationId) {
      return c.json({ error: 'User not associated with any organization' }, 400);
    }

    const purchases = await StorageService.getPurchaseHistory(user.organizationId.toString());

    return c.json({
      success: true,
      data: purchases,
    });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

app.post('/check-upload', async c => {
  try {
    const user = c.get('user');
    const { fileSize } = await c.req.json();

    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    if (!user.organizationId) {
      return c.json({ error: 'User not associated with any organization' }, 400);
    }

    if (!fileSize || fileSize <= 0) {
      return c.json({ error: 'Invalid file size' }, 400);
    }

    const uploadCheck = await StorageService.canUploadFile(
      user.organizationId.toString(),
      fileSize
    );

    return c.json({
      success: true,
      data: uploadCheck,
    });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

app.get('/analytics', async c => {
  try {
    const user = c.get('user');

    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    if (!user.organizationId) {
      return c.json({ error: 'User not associated with any organization' }, 400);
    }

    // Get period parameter from query string, default to 'month' if not provided
    const periodParam = (c.req.query('period') as 'day' | 'week' | 'month' | 'year') || 'month';

    // Validate period parameter
    const validPeriods = ['day', 'week', 'month', 'year'];
    if (periodParam && !validPeriods.includes(periodParam)) {
      return c.json({ error: 'Invalid period parameter. Must be day, week, month, or year.' }, 400);
    }

    const analytics = await StorageService.getStorageAnalytics(
      user.organizationId.toString(),
      periodParam
    );

    return c.json({
      success: true,
      data: analytics,
    });
  } catch (error: any) {
    console.error('Storage analytics error:', error);
    return c.json({ error: error.message || 'Failed to generate storage analytics' }, 500);
  }
});

app.get('/forecast', async c => {
  try {
    const user = c.get('user');

    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    if (!user.organizationId) {
      return c.json({ error: 'User not associated with any organization' }, 400);
    }

    // Get months parameter from query string, default to 6 if not provided
    const monthsParam = c.req.query('months');
    const months = monthsParam ? parseInt(monthsParam) : 6;

    // Validate months parameter
    if (isNaN(months) || months <= 0 || months > 24) {
      return c.json({ error: 'Invalid months parameter. Must be between 1 and 24.' }, 400);
    }

    const forecast = await StorageService.getStorageForecast(
      user.organizationId.toString(),
      months
    );

    return c.json({
      success: true,
      data: forecast,
    });
  } catch (error: any) {
    console.error('Storage forecast error:', error);
    return c.json({ error: error.message || 'Failed to generate storage forecast' }, 500);
  }
});

export const GET = handle(app);
export const POST = handle(app);
