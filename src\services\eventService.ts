import { IEvent } from '@/models/Event';

export interface EventFilters {
  userId?: string;
  organizationId?: string;
  projectId?: string;
  startDate?: string;
  endDate?: string;
  status?: string;
}

export interface CreateEventData {
  title: string;
  description: string;
  location: string;
  startTime: Date;
  endTime: Date;
  isAllDay: boolean;
  timezone: string;
  attendees: any[];
  reminders: any[];
  taskIds: string[];
  colorId: string;
  category: string;
  tags: string[];
  priority: 'low' | 'medium' | 'high';
  visibility: 'default' | 'public' | 'private' | 'confidential';
  userId?: string;
  organizationId?: string;
  projectId?: string;
  recurrence?: any;
  attachments?: any[];
}

export interface UpdateEventData {
  startTime?: Date;
  endTime?: Date;
  [key: string]: any;
}

class EventService {
  private baseUrl = '/api/events';

  async getEvents(filters: EventFilters): Promise<{ events: IEvent[] }> {
    const params = new URLSearchParams();

    Object.entries(filters).forEach(([key, value]) => {
      if (value) params.append(key, value);
    });

    const response = await fetch(`${this.baseUrl}?${params}`);
    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || 'Failed to fetch events');
    }

    return data;
  }

  async createEvent(eventData: CreateEventData): Promise<IEvent> {
    const response = await fetch(this.baseUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(eventData),
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || 'Failed to create event');
    }

    return data;
  }

  async updateEvent(eventId: string, updates: UpdateEventData): Promise<IEvent> {
    const response = await fetch(`${this.baseUrl}/${eventId}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(updates),
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || 'Failed to update event');
    }

    return data;
  }

  async deleteEvent(eventId: string): Promise<void> {
    const response = await fetch(`${this.baseUrl}/${eventId}`, {
      method: 'DELETE',
    });

    if (!response.ok) {
      const data = await response.json();
      throw new Error(data.message || 'Failed to delete event');
    }
  }

  async syncGoogleCalendar(userId: string): Promise<{ syncedEvents: number }> {
    const response = await fetch('/api/google-calendar/sync', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ userId }),
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || 'Failed to sync with Google Calendar');
    }

    return data;
  }

  async getGoogleCalendarStatus(userId: string): Promise<{ connected: boolean }> {
    const response = await fetch(`/api/google-calendar/status?userId=${userId}`);
    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || 'Failed to check Google Calendar status');
    }

    return data;
  }

  async getTasks(filters: EventFilters): Promise<{ tasks: any[] }> {
    const params = new URLSearchParams();

    Object.entries(filters).forEach(([key, value]) => {
      if (value) params.append(key, value);
    });

    const response = await fetch(`/api/tasks?${params}`);
    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || 'Failed to fetch tasks');
    }

    return data;
  }
}

export const eventService = new EventService();
