import { Hono } from 'hono';
import { handle } from 'hono/vercel';
import { logger } from 'hono/logger';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../auth/[...nextauth]/options';
import { connectDB } from '@/Utility/db';
import { Subscription } from '@/models/Subscription';
import { Notification } from '@/models/Notification';
import { storageQuotaManager, QuotaStatus } from '@/services/StorageQuotaManager.service';

// Define Variables type for context
type Variables = {
  user?: {
    id: string;
    name?: string;
    email?: string;
    image?: string;
    organizationId?: string;
  };
};

const app = new Hono<{ Variables: Variables }>().basePath('/api/storage/quota-enforcement');

app.use('*', logger());

app.use('*', async (c, next) => {
  try {
    const session = await getServerSession(authOptions);
    if (session?.user) {
      c.set('user', {
        id: session.user.id || '',
        name: session.user.name || '',
        email: session.user.email || '',
        image: session.user.image || '',
        organizationId: session.user.organizationId || '',
      });
    }
  } catch (error: any) {
    throw new Error(error.message);
  }
  await next();
});

// Configuration constants
const QUOTA_THRESHOLDS = {
  warning: 75, // 75% usage
  critical: 90, // 90% usage
  overQuota: 100, // 100% usage
};

const GRACE_PERIOD_HOURS = 24; // 24 hours grace period for critical operations
const EMERGENCY_ALLOCATION_BYTES = 50 * 1024 * 1024; // 50MB emergency allocation

// GET: Check quota status and enforcement rules
app.get('/', async c => {
  const user = c.get('user');
  if (!user?.organizationId) {
    return c.json({ error: 'Unauthorized' }, 401);
  }
  try {
    const url = new URL(c.req.url);
    const includeRecommendations = url.searchParams.get('recommendations') === 'true';
    const includeCleanupSuggestions = url.searchParams.get('cleanup') === 'true';
    await connectDB();
    const quotaStatus = await storageQuotaManager.getQuotaStatus(user.organizationId);
    let recommendations: any;
    let cleanupSuggestions: any;
    if (includeRecommendations) {
      recommendations = await storageQuotaManager.getOptimizationRecommendations(
        user.organizationId
      );
    }
    if (includeCleanupSuggestions) {
      cleanupSuggestions = await storageQuotaManager.getCleanupRecommendations(user.organizationId);
    }
    const gracePeriodInfo = await checkGracePeriodStatus(user.organizationId);
    return c.json({
      success: true,
      data: {
        quotaStatus,
        gracePeriodInfo,
        thresholds: QUOTA_THRESHOLDS,
        emergencyAllocation: EMERGENCY_ALLOCATION_BYTES,
        recommendations,
        cleanupSuggestions,
        enforcementLevel: getEnforcementLevel(quotaStatus),
      },
    });
  } catch (error) {
    console.error('Error fetching quota enforcement status:', error);
    return c.json({ error: 'Failed to fetch quota enforcement status' }, 500);
  }
});

// POST: Enforce quota limits and trigger automated actions
app.post('/', async c => {
  const user = c.get('user');
  if (!user?.organizationId) {
    return c.json({ error: 'Unauthorized' }, 401);
  }
  try {
    const body = await c.req.json();
    const {
      action,
      fileSize,
      forceEnforcement = false,
      requestEmergencyAllocation = false,
      cleanupMode = 'safe',
    } = body;
    if (!action) {
      return c.json({ error: 'Action is required' }, 400);
    }
    await connectDB();
    switch (action) {
      case 'check_upload_permission':
        return await handleUploadPermissionCheckHono(c, user.organizationId, fileSize);
      case 'enforce_quota':
        return await handleQuotaEnforcementHono(c, user.organizationId, forceEnforcement);
      case 'trigger_cleanup':
        return await handleAutomatedCleanupHono(c, user.organizationId, cleanupMode);
      case 'request_emergency_allocation':
        return await handleEmergencyAllocationHono(
          c,
          user.organizationId,
          requestEmergencyAllocation
        );
      case 'process_violations':
        return await handleQuotaViolationsHono(c, user.organizationId);
      case 'send_notifications':
        return await handleQuotaNotificationsHono(c, user.organizationId);
      default:
        return c.json({ error: 'Invalid action' }, 400);
    }
  } catch (error) {
    console.error('Error in quota enforcement:', error);
    return c.json({ error: 'Failed to process quota enforcement request' }, 500);
  }
});

// PUT: Update quota enforcement settings
app.put('/', async c => {
  const user = c.get('user');
  if (!user?.organizationId) {
    return c.json({ error: 'Unauthorized' }, 401);
  }
  try {
    const body = await c.req.json();
    const { enableAutoCleanup, autoUpgradeThreshold, gracePeriodExtension, notificationSettings } =
      body;
    await connectDB();
    const updateFields: any = {};
    if (enableAutoCleanup !== undefined) {
      updateFields.autoCleanupEnabled = enableAutoCleanup;
    }
    if (autoUpgradeThreshold !== undefined) {
      updateFields.autoUpgradeThreshold = autoUpgradeThreshold;
    }
    if (gracePeriodExtension !== undefined) {
      updateFields.gracePeriodExtension = gracePeriodExtension;
    }
    if (notificationSettings !== undefined) {
      updateFields.quotaNotificationSettings = notificationSettings;
    }
    await Subscription.findOneAndUpdate(
      { organizationId: user.organizationId },
      { ...updateFields, updatedAt: new Date() }
    );
    return c.json({
      success: true,
      message: 'Quota enforcement settings updated successfully',
    });
  } catch (error) {
    console.error('Error updating quota enforcement settings:', error);
    return c.json({ error: 'Failed to update quota enforcement settings' }, 500);
  }
});

// DELETE: Clean up quota violations and reset enforcement
app.delete('/', async c => {
  const user = c.get('user');
  if (!user?.organizationId) {
    return c.json({ error: 'Unauthorized' }, 401);
  }
  try {
    const url = new URL(c.req.url);
    const resetType = url.searchParams.get('type') || 'soft';
    await connectDB();
    let result;
    switch (resetType) {
      case 'soft':
        result = await performSoftReset(user.organizationId);
        break;
      case 'hard':
        result = await performHardReset(user.organizationId);
        break;
      case 'emergency':
        result = await performEmergencyReset(user.organizationId);
        break;
      default:
        return c.json({ error: 'Invalid reset type' }, 400);
    }
    return c.json({
      success: true,
      data: result,
    });
  } catch (error) {
    console.error('Error resetting quota enforcement:', error);
    return c.json({ error: 'Failed to reset quota enforcement' }, 500);
  }
});

// --- Hono-adapted helper functions ---
async function handleUploadPermissionCheckHono(c: any, organizationId: string, fileSize: number) {
  if (!fileSize || fileSize <= 0) {
    return c.json({ error: 'Valid file size is required' }, 400);
  }
  const permission = await storageQuotaManager.canUploadFile(organizationId, fileSize);
  const quotaStatus = await storageQuotaManager.getQuotaStatus(organizationId);
  return c.json({
    success: true,
    data: {
      allowed: permission.allowed,
      reason: permission.reason,
      quotaStatus,
      recommendations: permission.allowed
        ? null
        : await getUploadRecommendations(organizationId, fileSize),
    },
  });
}

async function handleQuotaEnforcementHono(
  c: any,
  organizationId: string,
  forceEnforcement: boolean
) {
  const enforcement = await storageQuotaManager.enforceQuotaLimits(organizationId);
  const quotaStatus = await storageQuotaManager.getQuotaStatus(organizationId);
  if (!enforcement.allowed && forceEnforcement) {
    const emergencyResult = await applyEmergencyMeasures(organizationId);
    return c.json({
      success: true,
      data: {
        enforcement,
        quotaStatus,
        emergencyMeasuresApplied: emergencyResult,
      },
    });
  }
  if (!enforcement.allowed) {
    await sendQuotaEnforcementNotification(
      organizationId,
      enforcement.reason ?? 'No reason provided'
    );
  }
  return c.json({
    success: true,
    data: {
      enforcement,
      quotaStatus,
    },
  });
}

async function handleAutomatedCleanupHono(c: any, organizationId: string, cleanupMode: string) {
  const dryRun = cleanupMode === 'preview';
  const cleanupResult = await storageQuotaManager.performAutomaticCleanup(organizationId, dryRun);
  if (!dryRun && cleanupResult.filesMarkedForDeletion > 0) {
    await createNotification(organizationId, {
      type: 'storage_cleanup',
      title: 'Automated Storage Cleanup Completed',
      message: `Cleaned up ${cleanupResult.filesMarkedForDeletion} files, saving ${formatBytes(cleanupResult.spaceSaved)} of storage.`,
      priority: 'medium',
    });
  }
  return c.json({
    success: true,
    data: {
      cleanupResult,
      mode: cleanupMode,
    },
  });
}

async function handleEmergencyAllocationHono(c: any, organizationId: string, granted: boolean) {
  if (!granted) {
    return c.json({
      success: true,
      data: {
        emergencyAllocationGranted: false,
        reason: 'Emergency allocation not requested',
      },
    });
  }
  const allocation = await grantEmergencyAllocation(organizationId);
  await createNotification(organizationId, {
    type: 'emergency_allocation',
    title: 'Emergency Storage Allocation Granted',
    message: `Temporary allocation of ${formatBytes(EMERGENCY_ALLOCATION_BYTES)} granted for ${GRACE_PERIOD_HOURS} hours.`,
    priority: 'high',
  });
  return c.json({
    success: true,
    data: allocation,
  });
}

async function handleQuotaViolationsHono(c: any, organizationId: string) {
  const quotaStatus = await storageQuotaManager.getQuotaStatus(organizationId);
  if (quotaStatus.status === 'over_quota') {
    const overageBytes = quotaStatus.currentUsage - quotaStatus.totalQuota;
    const overageCost = calculateOverageCost(overageBytes);
    if (overageCost > 0) {
      const billingResult = await processOverageBilling(organizationId, overageCost);
      return c.json({
        success: true,
        data: {
          violation: 'quota_exceeded',
          overageBytes,
          overageCost,
          billingResult,
          quotaStatus,
        },
      });
    }
  }
  return c.json({
    success: true,
    data: {
      violation: 'none',
      quotaStatus,
    },
  });
}

async function handleQuotaNotificationsHono(c: any, organizationId: string) {
  await storageQuotaManager.sendQuotaAlerts(organizationId);
  return c.json({
    success: true,
    message: 'Quota notifications sent successfully',
  });
}

// --- The rest of the helper functions remain unchanged ---
async function checkGracePeriodStatus(organizationId: string) {
  const subscription = await Subscription.findOne({ organizationId });
  if (!subscription || !subscription.gracePeriodEnd) {
    return {
      active: false,
      remaining: 0,
    };
  }
  const now = new Date();
  const gracePeriodEnd = new Date(subscription.gracePeriodEnd);
  const remaining = Math.max(0, gracePeriodEnd.getTime() - now.getTime());
  return {
    active: remaining > 0,
    remaining: Math.ceil(remaining / (1000 * 60 * 60)), // hours
    endDate: gracePeriodEnd,
  };
}

function getEnforcementLevel(quotaStatus: QuotaStatus): string {
  switch (quotaStatus.status) {
    case 'over_quota':
      return 'strict';
    case 'critical':
      return 'moderate';
    case 'warning':
      return 'soft';
    default:
      return 'none';
  }
}

async function getUploadRecommendations(organizationId: string, fileSize: number) {
  const recommendations = await storageQuotaManager.getOptimizationRecommendations(organizationId);
  const quotaStatus = await storageQuotaManager.getQuotaStatus(organizationId);
  const suggestions = [
    `File size: ${formatBytes(fileSize)}`,
    `Current usage: ${quotaStatus.usagePercentage.toFixed(1)}%`,
  ];
  if (recommendations.potentialSavings > fileSize) {
    suggestions.push('Clean up files first - you can free up enough space');
  } else {
    suggestions.push('Consider upgrading your storage plan');
  }
  return suggestions;
}

async function applyEmergencyMeasures(organizationId: string) {
  const allocation = await grantEmergencyAllocation(organizationId);
  const cleanup = await storageQuotaManager.performAutomaticCleanup(organizationId, false);
  return {
    emergencyAllocation: allocation,
    cleanupPerformed: cleanup,
    timestamp: new Date(),
  };
}

async function grantEmergencyAllocation(organizationId: string) {
  const expiresAt = new Date(Date.now() + GRACE_PERIOD_HOURS * 60 * 60 * 1000);
  await Subscription.findOneAndUpdate(
    { organizationId },
    {
      emergencyAllocation: {
        bytes: EMERGENCY_ALLOCATION_BYTES,
        grantedAt: new Date(),
        expiresAt,
        used: false,
      },
    }
  );
  return {
    granted: true,
    bytes: EMERGENCY_ALLOCATION_BYTES,
    expiresAt,
    hoursRemaining: GRACE_PERIOD_HOURS,
  };
}

function calculateOverageCost(overageBytes: number): number {
  const overageGB = overageBytes / (1024 * 1024 * 1024);
  return Math.ceil(overageGB) * 50; // ₹50 per GB
}

async function processOverageBilling(organizationId: string, amount: number) {
  await createNotification(organizationId, {
    type: 'billing_adjustment',
    title: 'Storage Overage Charge',
    message: `A charge of ₹${amount} has been applied for storage overage.`,
    priority: 'high',
  });
  return {
    processed: true,
    amount,
    currency: 'INR',
    timestamp: new Date(),
    status: 'pending',
  };
}

async function sendQuotaEnforcementNotification(organizationId: string, reason: string) {
  await createNotification(organizationId, {
    type: 'quota_enforcement',
    title: 'Storage Quota Enforcement Active',
    message: `Storage access restricted: ${reason}`,
    priority: 'high',
  });
}

async function createNotification(organizationId: string, notificationData: any) {
  const notification = new Notification({
    organizationId,
    ...notificationData,
    metadata: {
      ...notificationData.metadata,
      timestamp: new Date(),
      source: 'quota_enforcement',
    },
  });
  await notification.save();
}

async function performSoftReset(organizationId: string) {
  await Subscription.findOneAndUpdate(
    { organizationId },
    {
      $unset: {
        quotaWarningsSent: 1,
        lastQuotaCheck: 1,
      },
    }
  );
  return {
    type: 'soft_reset',
    message: 'Warning flags and grace period notifications reset',
  };
}

async function performHardReset(organizationId: string) {
  await Subscription.findOneAndUpdate(
    { organizationId },
    {
      $unset: {
        quotaWarningsSent: 1,
        lastQuotaCheck: 1,
        gracePeriodEnd: 1,
        emergencyAllocation: 1,
      },
    }
  );
  return {
    type: 'hard_reset',
    message: 'All quota enforcement flags and allocations reset',
  };
}

async function performEmergencyReset(organizationId: string) {
  const emergencyAllocation = await grantEmergencyAllocation(organizationId);
  await Subscription.findOneAndUpdate(
    { organizationId },
    {
      $unset: {
        quotaWarningsSent: 1,
        lastQuotaCheck: 1,
        gracePeriodEnd: 1,
      },
    }
  );
  return {
    type: 'emergency_reset',
    message: 'Emergency reset with temporary allocation granted',
    emergencyAllocation,
  };
}

function formatBytes(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

export const GET = handle(app);
export const POST = handle(app);
export const PUT = handle(app);
export const DELETE = handle(app);
