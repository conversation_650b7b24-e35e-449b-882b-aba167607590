'use client';

import React, { useEffect, useState, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { toast } from 'sonner';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { LoadingSpinner } from '@/components/Global/LoadingSpinner';

function ProtocolHandler() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isProcessing, setIsProcessing] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    try {
      // Get the URL from the search params
      const encodedUrl = searchParams.get('url');

      if (!encodedUrl) {
        setError('No URL parameter provided');
        setIsProcessing(false);
        return;
      }

      // Decode the URL
      const url = decodeURIComponent(encodedUrl);

      // Parse the custom protocol URL (web+taskmantra://...)
      // Remove the protocol part
      const urlWithoutProtocol = url.replace(/^web\+taskmantra:\/\//, '');

      // Parse the path and query params
      const [path, queryString] = urlWithoutProtocol.split('?');
      const segments = path.split('/').filter(Boolean);
      const params = new URLSearchParams(queryString || '');

      // Determine where to redirect based on the protocol URL structure
      if (segments.length === 0) {
        // Default landing if no path specified
        router.push('/home');
        return;
      }

      // Handle different resource types
      const resourceType = segments[0].toLowerCase();

      switch (resourceType) {
        case 'task':
          if (segments.length > 1 && segments[1]) {
            // Task ID provided - view specific task
            const taskId = segments[1];
            router.push(`/tasks?taskId=${taskId}`);
          } else {
            // Task creation with optional params
            const title = params.get('title');
            const description = params.get('description');
            const priority = params.get('priority');
            const dueDate = params.get('dueDate');

            const taskParams = new URLSearchParams();
            if (title) taskParams.set('title', title);
            if (description) taskParams.set('description', description);
            if (priority) taskParams.set('priority', priority);
            if (dueDate) taskParams.set('dueDate', dueDate);

            router.push(`/tasks?create=true&${taskParams.toString()}`);
          }
          break;

        case 'project':
          if (segments.length > 1 && segments[1]) {
            // Project ID provided - view specific project
            const projectId = segments[1];
            router.push(`/projects/${projectId}`);
          } else {
            // Project listing or creation
            const createNew = params.get('create') === 'true';
            if (createNew) {
              router.push('/projects?create=true');
            } else {
              router.push('/projects');
            }
          }
          break;

        case 'note':
          if (segments.length > 1 && segments[1]) {
            // Note ID provided - view specific note
            const noteId = segments[1];
            router.push(`/notes/view/${noteId}`);
          } else {
            // Create new note
            const title = params.get('title');
            const content = params.get('content');

            const noteParams = new URLSearchParams();
            if (title) noteParams.set('title', title);
            if (content) noteParams.set('content', content);

            router.push(`/notes/editor?${noteParams.toString()}`);
          }
          break;

        case 'calendar':
          // eslint-disable-next-line no-case-declarations
          const date = params.get('date');
          if (date) {
            router.push(`/calendar?date=${date}`);
          } else {
            router.push('/calendar');
          }
          break;

        default:
          // Unknown resource type, redirect to home
          setError(`Unknown resource type: ${resourceType}`);
          setTimeout(() => {
            router.push('/home');
          }, 2000);
      }
    } catch (err) {
      console.error('Error handling protocol:', err);
      setError('Failed to process the protocol request');
      setIsProcessing(false);

      // Show error toast
      toast.error('Failed to process the protocol request', {
        description: 'The URL format may be invalid or unsupported.',
      });

      // Redirect to home after a delay
      setTimeout(() => {
        router.push('/home');
      }, 3000);
    }
  }, [router, searchParams]);

  return (
    <div className="flex items-center justify-center min-h-screen bg-background p-4">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>TaskMantra Protocol Handler</CardTitle>
          <CardDescription>
            {isProcessing ? 'Processing your request...' : error ? 'Error' : 'Redirecting...'}
          </CardDescription>
        </CardHeader>
        <CardContent className="flex flex-col items-center justify-center py-8">
          {isProcessing ? (
            <LoadingSpinner size="lg" />
          ) : error ? (
            <div className="text-center">
              <p className="text-destructive mb-4">{error}</p>
              <p className="text-muted-foreground">Redirecting to home page...</p>
            </div>
          ) : (
            <LoadingSpinner size="lg" />
          )}
        </CardContent>
      </Card>
    </div>
  );
}
// Wrap the component with Suspense to fix the useSearchParams error
export default function HandleProtocolPage() {
  return (
    <Suspense
      fallback={
        <div className="flex items-center justify-center min-h-screen bg-background p-4">
          <Card className="w-full max-w-md">
            <CardHeader>
              <CardTitle>TaskMantra Protocol Handler</CardTitle>
              <CardDescription>Loading...</CardDescription>
            </CardHeader>
            <CardContent className="flex flex-col items-center justify-center py-8">
              <LoadingSpinner size="lg" />
            </CardContent>
          </Card>
        </div>
      }
    >
      <ProtocolHandler />
    </Suspense>
  );
}
