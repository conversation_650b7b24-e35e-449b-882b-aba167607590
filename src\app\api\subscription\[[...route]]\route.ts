import { Hono } from 'hono';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/options';
import { connectDB } from '@/Utility/db';
import { Organization } from '@/models/organization';
import { Subscription } from '@/models/Subscription';
import { StoragePurchase } from '@/models/StoragePurchase';
import { Project } from '@/models/Project';
import { User } from '@/models/User';
import { logger } from 'hono/logger';
import { handle } from 'hono/vercel';
import { SUBSCRIPTION_PLANS, SubscriptionService } from '@/services/Subscription.service';

interface Variables {
  user: {
    id: string;
    name: string;
    email: string;
    image: string;
    organizationId: string;
  };
}

const app = new Hono<{ Variables: Variables }>().basePath('/api/subscription');
app.use('*', logger());

// Middleware to inject user details
app.use('*', async (c, next) => {
  try {
    await connectDB();
    const session = await getServerSession(authOptions);

    if (session?.user?.email) {
      const user = await User.findOne({ email: session.user.email });
      if (user) {
        const userData = {
          id: user._id.toString(),
          name: user.name || '',
          email: user.email || '',
          image: user.image || '',
          organizationId: user.organizationId?.toString() || '',
        };
        c.set('user', userData);
      }
    }
  } catch (error: any) {
    throw new Error(error.message);
  }
  await next();
});

// Get subscription information
app.get('/info', async c => {
  try {
    const user = c.get('user');
    if (!user || !user.organizationId) {
      return c.json({ error: 'User not authenticated or no organization' }, 401);
    }

    const organizationId = user.organizationId;

    // Get or create subscription
    let subscription = await Subscription.findOne({ organizationId });
    if (!subscription) {
      subscription = await Subscription.create({
        organizationId,
        plan: 'free',
        status: 'active',
        storageLimit: 52428800, // 50MB
        userLimit: 3,
        projectLimit: 5,
        integrationLimit: 2,
        billingCycle: 'monthly',
        amount: 0,
        features: {
          advancedAnalytics: false,
          customIntegrations: false,
          prioritySupport: false,
          ssoEnabled: false,
          apiAccess: false,
          customBranding: false,
          advancedSecurity: false,
          unlimitedProjects: false,
          unlimitedUsers: false,
        },
      });
    }

    // Get plan details
    const plan = SUBSCRIPTION_PLANS.find(p => p.id === subscription.plan);
    if (!plan) {
      return c.json({ error: 'Invalid subscription plan' }, 400);
    }

    // Get usage statistics
    const organization = await Organization.findById(organizationId);
    const userCount = organization?.members?.length || 0;
    const projectCount = await Project.countDocuments({ organizationId });

    // Calculate storage usage (you already have this logic in StorageService)
    const storageUsed = organization?.storageUsed || 0;
    const storageLimit = subscription.storageLimit + subscription.totalStoragePurchased;
    const storagePercentage = storageLimit > 0 ? (storageUsed / storageLimit) * 100 : 0;

    // Get integration count (you'll need to implement this based on your integration model)
    const integrationCount = 0; // TODO: Implement based on your Integration model

    const subscriptionInfo = {
      subscription,
      plan,
      usage: {
        users: userCount,
        projects: projectCount,
        storage: {
          used: storageUsed,
          limit: storageLimit,
          percentage: Math.round(storagePercentage * 100) / 100,
        },
        integrations: integrationCount,
      },
      billing: {
        nextPaymentDate: subscription.nextPaymentDate,
        lastPaymentDate: subscription.lastPaymentDate,
        amount: subscription.amount,
        currency: subscription.currency,
        billingCycle: subscription.billingCycle,
      },
      canUpgrade: SubscriptionService.canUpgradeToplan(subscription.plan, 'enterprise'),
      canDowngrade: SubscriptionService.canDowngradeToplan(subscription.plan, 'free'),
    };

    return c.json({
      success: true,
      data: subscriptionInfo,
    });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

// Get available plans
app.get('/plans', async c => {
  try {
    return c.json({
      success: true,
      data: SUBSCRIPTION_PLANS,
    });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

// Upgrade subscription
app.post('/upgrade', async c => {
  try {
    const user = c.get('user');
    if (!user || !user.organizationId) {
      return c.json({ error: 'User not authenticated or no organization' }, 401);
    }

    const { newPlan, billingCycle } = await c.req.json();

    if (!newPlan || !billingCycle) {
      return c.json({ error: 'Plan and billing cycle are required' }, 400);
    }

    const targetPlan = SUBSCRIPTION_PLANS.find(p => p.id === newPlan);
    if (!targetPlan) {
      return c.json({ error: 'Invalid plan selected' }, 400);
    }

    // Check if user is organization owner
    const organization = await Organization.findById(user.organizationId);
    if (!organization || organization.ownerId.toString() !== user.id) {
      return c.json({ error: 'Only organization owner can upgrade subscription' }, 403);
    }

    const subscription = await Subscription.findOne({ organizationId: user.organizationId });
    if (!subscription) {
      return c.json({ error: 'Subscription not found' }, 404);
    }

    if (!SubscriptionService.canUpgradeToplan(subscription.plan, newPlan)) {
      return c.json({ error: 'Cannot upgrade to the selected plan' }, 400);
    }

    // Calculate new limits and features based on plan
    const amount = billingCycle === 'yearly' ? targetPlan.price.yearly : targetPlan.price.monthly;
    const storageBytes =
      targetPlan.features.storage === '50MB'
        ? 52428800
        : targetPlan.features.storage === '1GB'
          ? 1073741824
          : targetPlan.features.storage === '5GB'
            ? 5368709120
            : targetPlan.features.storage === '20GB'
              ? 21474836480
              : 52428800;

    // Update subscription
    await Subscription.findByIdAndUpdate(subscription._id, {
      plan: newPlan,
      billingCycle,
      amount,
      storageLimit: storageBytes,
      userLimit: typeof targetPlan.features.users === 'number' ? targetPlan.features.users : 999999,
      projectLimit:
        typeof targetPlan.features.projects === 'number' ? targetPlan.features.projects : 999999,
      integrationLimit:
        typeof targetPlan.features.integrations === 'number'
          ? targetPlan.features.integrations
          : 999999,
      features: {
        advancedAnalytics: targetPlan.features.advancedAnalytics,
        customIntegrations: targetPlan.features.customIntegrations,
        prioritySupport: targetPlan.features.prioritySupport,
        ssoEnabled: targetPlan.features.ssoEnabled,
        apiAccess: targetPlan.features.apiAccess,
        customBranding: targetPlan.features.customBranding,
        advancedSecurity: targetPlan.features.advancedSecurity,
        unlimitedProjects: targetPlan.features.projects === 'unlimited',
        unlimitedUsers: targetPlan.features.users === 'unlimited',
      },
      nextPaymentDate:
        billingCycle === 'yearly'
          ? new Date(Date.now() + 365 * 24 * 60 * 60 * 1000)
          : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
    });

    return c.json({
      success: true,
      message: `Successfully upgraded to ${targetPlan.name} plan`,
    });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

// Get billing history
app.get('/billing-history', async c => {
  try {
    const user = c.get('user');
    if (!user || !user.organizationId) {
      return c.json({ error: 'User not authenticated or no organization' }, 401);
    }

    // Get storage purchases (existing billing history)
    const storagePurchases = await StoragePurchase.find({
      organizationId: user.organizationId,
    })
      .populate('purchasedBy', 'name email')
      .sort({ createdAt: -1 })
      .limit(50);

    // Format billing history
    const billingHistory = storagePurchases.map(purchase => ({
      id: purchase._id,
      type: 'storage_purchase',
      description: purchase.description,
      amount: purchase.totalPrice,
      currency: purchase.currency,
      status: purchase.paymentStatus,
      paymentMethod: purchase.paymentMethod,
      date: purchase.createdAt,
      storageAmount: purchase.storageAmount,
      pricePerMB: purchase.pricePerMB,
      invoice: null, // TODO: Generate invoice URLs
      purchasedBy: purchase.purchasedBy,
    }));

    return c.json({
      success: true,
      data: billingHistory,
    });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

// Download receipt for a transaction
app.get('/receipt/:transactionId', async c => {
  try {
    const user = c.get('user');
    if (!user || !user.organizationId) {
      return c.json({ error: 'User not authenticated or no organization' }, 401);
    }

    const transactionId = c.req.param('transactionId');

    // Get transaction details
    const transaction = await StoragePurchase.findOne({
      _id: transactionId,
      organizationId: user.organizationId,
    })
      .populate('purchasedBy', 'name email')
      .populate('organizationId', 'name email');

    if (!transaction) {
      return c.json({ error: 'Transaction not found' }, 404);
    }

    // Generate receipt data
    const receiptData = {
      receiptNumber: `RCP-${transaction._id.toString().slice(-8).toUpperCase()}`,
      transactionId: transaction._id,
      date: transaction.createdAt,
      organization: {
        name: transaction.organizationId.name,
        email: transaction.organizationId.email,
      },
      customer: {
        name: transaction.purchasedBy.name,
        email: transaction.purchasedBy.email,
      },
      items: [
        {
          description: transaction.description,
          quantity: Math.round(transaction.storageAmount / (1024 * 1024)), // Convert bytes to MB
          unit: 'MB',
          pricePerUnit: transaction.pricePerMB,
          amount: transaction.totalPrice,
        },
      ],
      payment: {
        method: transaction.paymentMethod,
        status: transaction.paymentStatus,
        paymentId: transaction.paymentId,
        orderId: transaction.orderId,
      },
      totals: {
        subtotal: transaction.totalPrice,
        tax: 0, // No tax for now
        total: transaction.totalPrice,
      },
      currency: transaction.currency,
    };

    return c.json({
      success: true,
      data: receiptData,
    });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

export const GET = handle(app);
export const POST = handle(app);
export const PUT = handle(app);
export const DELETE = handle(app);
