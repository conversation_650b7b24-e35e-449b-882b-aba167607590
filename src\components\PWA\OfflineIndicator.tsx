'use client';

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Wifi, WifiOff, RefreshCw, CheckCircle, AlertCircle, Clock } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';

interface OfflineIndicatorProps {
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
  variant?: 'minimal' | 'detailed' | 'compact';
  showQueue?: boolean;
  autoHide?: boolean;
  autoHideDelay?: number;
  className?: string;
}

interface QueueStatus {
  pending: number;
  failed: number;
  completed: number;
}

type SyncState = 'idle' | 'syncing' | 'success' | 'error';

const OfflineIndicator: React.FC<OfflineIndicatorProps> = ({
  position = 'bottom-right',
  variant = 'detailed',
  showQueue = true,
  autoHide = true,
  autoHideDelay = 5000,
  className = '',
}) => {
  const [isOnline, setIsOnline] = useState(true);
  const [isVisible, setIsVisible] = useState(false);
  const [queueStatus, setQueueStatus] = useState<QueueStatus>({
    pending: 0,
    failed: 0,
    completed: 0,
  });
  const [syncProgress, setSyncProgress] = useState(0);
  const [syncState, setSyncState] = useState<SyncState>('idle');
  const [lastSyncTime, setLastSyncTime] = useState<Date | null>(null);
  const [retryCount, setRetryCount] = useState(0);

  const hideTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  const syncTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  // Position classes mapping
  const positionClasses = {
    'top-left': 'top-4 left-4',
    'top-right': 'top-4 right-4',
    'bottom-left': 'bottom-4 left-4',
    'bottom-right': 'bottom-4 right-4',
  };

  // Initialize online status correctly
  useEffect(() => {
    setIsOnline(navigator.onLine);
    setIsVisible(!navigator.onLine || !autoHide);
  }, [autoHide]);

  // Update queue status from localStorage
  const updateQueueStatus = useCallback(() => {
    if (!showQueue) return;

    try {
      const pending = JSON.parse(localStorage.getItem('offline_queue') || '[]').length;
      const failed = JSON.parse(localStorage.getItem('failed_queue') || '[]').length;
      const completed = JSON.parse(localStorage.getItem('completed_queue') || '[]').length;

      setQueueStatus({ pending, failed, completed });
    } catch (error) {
      toast.error('Failed to read queue status');
    }
  }, [showQueue]);

  // Simulate sync process
  const performSync = useCallback(async () => {
    if (!isOnline || queueStatus.pending === 0) {
      setSyncState('success');
      setLastSyncTime(new Date());
      setTimeout(() => setSyncState('idle'), 2000);
      return;
    }

    setSyncState('syncing');
    setSyncProgress(0);

    try {
      // Simulate progressive sync
      const totalItems = queueStatus.pending;
      let completed = 0;

      const syncInterval = setInterval(() => {
        completed += Math.ceil(Math.random() * 2);
        const progress = Math.min((completed / totalItems) * 100, 100);
        setSyncProgress(progress);

        if (progress >= 100) {
          clearInterval(syncInterval);
          setSyncState('success');
          setLastSyncTime(new Date());
          setRetryCount(0);
          updateQueueStatus();

          setTimeout(() => setSyncState('idle'), 2000);
        }
      }, 300);

      syncTimeoutRef.current = syncInterval;
    } catch (error) {
      setSyncState('error');
      setTimeout(() => setSyncState('idle'), 3000);
    }
  }, [isOnline, queueStatus.pending, updateQueueStatus]);

  // Handle retry with exponential backoff
  const handleRetry = useCallback(() => {
    setRetryCount(prev => prev + 1);
    const delay = Math.min(1000 * Math.pow(2, retryCount), 10000);

    setTimeout(() => {
      if (navigator.onLine) {
        performSync();
      }
    }, delay);
  }, [retryCount, performSync]);

  // Auto-hide functionality
  useEffect(() => {
    if (hideTimeoutRef.current) {
      clearTimeout(hideTimeoutRef.current);
    }

    if (autoHide && isOnline && syncState === 'idle') {
      hideTimeoutRef.current = setTimeout(() => {
        setIsVisible(false);
      }, autoHideDelay);
    }

    return () => {
      if (hideTimeoutRef.current) {
        clearTimeout(hideTimeoutRef.current);
      }
    };
  }, [autoHide, isOnline, syncState, autoHideDelay]);

  // Network event handlers
  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      setIsVisible(true);
      setSyncState('syncing');
      setTimeout(() => performSync(), 500);
    };

    const handleOffline = () => {
      setIsOnline(false);
      setIsVisible(true);
      setSyncState('idle');
      setSyncProgress(0);

      if (syncTimeoutRef.current) {
        clearInterval(syncTimeoutRef.current);
      }
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Initial queue status
    updateQueueStatus();

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
      if (syncTimeoutRef.current) {
        clearInterval(syncTimeoutRef.current);
      }
    };
  }, [performSync, updateQueueStatus]);

  // Periodic queue updates
  useEffect(() => {
    if (!showQueue) return;

    const interval = setInterval(updateQueueStatus, 30000);
    return () => clearInterval(interval);
  }, [showQueue, updateQueueStatus]);

  // Status helpers
  const getStatusIcon = () => {
    switch (syncState) {
      case 'syncing':
        return <RefreshCw className="w-4 h-4 animate-spin text-blue-500" />;
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      default:
        return isOnline ? (
          <Wifi className="w-4 h-4 text-green-500" />
        ) : (
          <WifiOff className="w-4 h-4 text-red-500" />
        );
    }
  };

  const getStatusText = () => {
    switch (syncState) {
      case 'syncing':
        return 'Syncing...';
      case 'success':
        return 'Sync complete';
      case 'error':
        return 'Sync failed';
      default:
        return isOnline ? 'Online' : 'Offline';
    }
  };

  if (!isVisible) return null;

  // Minimal variant
  if (variant === 'minimal') {
    return (
      <div className={cn('fixed z-50 p-2', positionClasses[position], className)}>
        <div
          className={cn(
            'flex items-center justify-center w-8 h-8 rounded-full transition-all duration-300',
            isOnline ? 'bg-green-100 dark:bg-green-900' : 'bg-red-100 dark:bg-red-900'
          )}
        >
          {getStatusIcon()}
        </div>
      </div>
    );
  }

  // Compact variant
  if (variant === 'compact') {
    return (
      <div className={cn('fixed z-50', positionClasses[position], className)}>
        <Card className="shadow-lg border-0 bg-white/90 dark:bg-gray-900/90 backdrop-blur-sm">
          <CardContent className="p-3">
            <div className="flex items-center gap-2">
              {getStatusIcon()}
              <span className="text-sm font-medium">{getStatusText()}</span>
              {showQueue && queueStatus.pending > 0 && (
                <Badge variant="secondary" className="text-xs">
                  {queueStatus.pending}
                </Badge>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Detailed variant
  return (
    <div className={cn('fixed z-50 w-80', positionClasses[position], className)}>
      <Card className="shadow-xl border-0 bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm">
        <CardContent className="p-4 space-y-4">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              {getStatusIcon()}
              <div>
                <div className="font-medium text-sm">{getStatusText()}</div>
                {lastSyncTime && (
                  <div className="text-xs text-gray-600 dark:text-gray-400">
                    Last sync: {lastSyncTime.toLocaleTimeString()}
                  </div>
                )}
              </div>
            </div>

            {!isOnline && (
              <Button
                onClick={handleRetry}
                size="sm"
                variant="outline"
                className="h-6 px-2 text-xs"
              >
                Retry
              </Button>
            )}
          </div>

          {/* Sync Progress */}
          {syncState === 'syncing' && (
            <div className="space-y-2">
              <div className="flex items-center justify-between text-xs">
                <span className="text-gray-600 dark:text-gray-400">
                  Syncing {queueStatus.pending} items...
                </span>
                <span className="font-medium">{Math.round(syncProgress)}%</span>
              </div>
              <Progress value={syncProgress} className="h-1.5" />
            </div>
          )}

          {/* Queue Status */}
          {showQueue && (queueStatus.pending > 0 || queueStatus.failed > 0) && (
            <div className="space-y-2">
              <div className="text-xs font-medium text-gray-700 dark:text-gray-300">Sync Queue</div>
              <div className="flex items-center gap-4 text-xs">
                {queueStatus.pending > 0 && (
                  <div className="flex items-center gap-1">
                    <Clock className="w-3 h-3 text-yellow-500" />
                    <span>{queueStatus.pending} pending</span>
                  </div>
                )}
                {queueStatus.failed > 0 && (
                  <div className="flex items-center gap-1">
                    <AlertCircle className="w-3 h-3 text-red-500" />
                    <span>{queueStatus.failed} failed</span>
                  </div>
                )}
                {queueStatus.completed > 0 && (
                  <div className="flex items-center gap-1">
                    <CheckCircle className="w-3 h-3 text-green-500" />
                    <span>{queueStatus.completed} synced</span>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Retry Information */}
          {retryCount > 0 && (
            <div className="text-xs text-gray-600 dark:text-gray-400">
              Retry attempts: {retryCount}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default OfflineIndicator;
