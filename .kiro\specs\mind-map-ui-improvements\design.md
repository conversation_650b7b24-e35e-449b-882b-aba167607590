# Design Document

## Overview

This design document outlines the improvements to the mind-map user interface, focusing on three key areas: enhanced node form UI, delete node functionality implementation, and removal of unnecessary UI elements. The design emphasizes consistency with the existing design system, improved user experience, and maintainable code architecture.

## Architecture

### Component Structure

The mind-map UI improvements will follow the existing component architecture:

```
src/components/MindMap/
├── MindMapCanvas.tsx (updated)
├── node-edit-modal.tsx (enhanced)
├── nodes/ (individual node components - enhanced styling)
└── contexts/ (shared state management)
```

### State Management

The improvements will leverage the existing `useMindMapEditor` hook pattern for:
- Node CRUD operations
- UI state management (modals, context menus)
- Form validation and error handling
- Undo/redo functionality

### Design System Integration

All improvements will use the existing theme system with classes like:
- `theme-surface`, `theme-surface-elevated`
- `theme-text-primary`, `theme-text-secondary`
- `theme-button-*`, `theme-input`, `theme-focus`
- `theme-transition`, `theme-shadow-*`

## Components and Interfaces

### Enhanced Node Edit Modal

#### Form Layout Structure
```typescript
interface EnhancedFormLayout {
  sections: FormSection[];
  validation: ValidationRules;
  accessibility: AccessibilityConfig;
}

interface FormSection {
  id: string;
  title: string;
  fields: FormField[];
  collapsible?: boolean;
}

interface FormField {
  key: string;
  type: 'text' | 'textarea' | 'select' | 'date' | 'number' | 'boolean' | 'array';
  label: string;
  placeholder?: string;
  validation?: FieldValidation;
  conditional?: ConditionalDisplay;
}
```

#### Visual Design Improvements

1. **Form Sections**: Group related fields with visual separators and optional collapsible sections
2. **Field Spacing**: Consistent 16px spacing between fields, 24px between sections
3. **Visual Hierarchy**: Clear typography scale (14px body, 12px labels, 16px section headers)
4. **Input States**: Enhanced focus, error, and disabled states with proper color contrast
5. **Loading States**: Skeleton loaders for form fields during data fetching

#### Form Field Enhancements

```typescript
// Enhanced field rendering with improved UX
const renderEnhancedField = (field: FormField, value: any) => {
  return (
    <div className="form-field-container">
      <Label className="form-label-enhanced">{field.label}</Label>
      {field.type === 'textarea' && (
        <Textarea 
          className="form-textarea-enhanced"
          rows={4}
          placeholder={field.placeholder}
          // Auto-resize functionality
        />
      )}
      {field.validation?.error && (
        <div className="form-error-message" role="alert">
          {field.validation.error}
        </div>
      )}
      {field.helpText && (
        <div className="form-help-text">{field.helpText}</div>
      )}
    </div>
  );
};
```

### Delete Node Functionality

#### Context Menu Enhancement
```typescript
interface ContextMenuAction {
  id: string;
  label: string;
  icon: React.ComponentType;
  action: () => void;
  variant?: 'default' | 'destructive';
  shortcut?: string;
}

const contextMenuActions: ContextMenuAction[] = [
  { id: 'edit', label: 'Edit', icon: Edit3, action: handleEdit },
  { id: 'duplicate', label: 'Duplicate', icon: Copy, action: handleDuplicate, shortcut: 'Ctrl+D' },
  { id: 'connect', label: 'Connect', icon: Link, action: handleConnect },
  { id: 'delete', label: 'Delete', icon: Trash2, action: handleDelete, variant: 'destructive', shortcut: 'Del' }
];
```

#### Confirmation Dialog Design
```typescript
interface DeleteConfirmationProps {
  nodeTitle: string;
  nodeType: string;
  connectionCount: number;
  onConfirm: () => void;
  onCancel: () => void;
}
```

The confirmation dialog will:
- Display node title and type for context
- Show connection count if applicable
- Use destructive styling (red accent)
- Include keyboard shortcuts (Enter to confirm, Escape to cancel)
- Show loading state during deletion

#### Deletion Process Flow
```mermaid
graph TD
    A[User triggers delete] --> B{Delete method?}
    B -->|Context menu| C[Show context menu]
    B -->|Keyboard| D[Direct to confirmation]
    C --> E[User clicks delete]
    D --> F[Show confirmation dialog]
    E --> F
    F --> G{User confirms?}
    G -->|Yes| H[Execute deletion]
    G -->|No| I[Cancel operation]
    H --> J[Remove node from state]
    J --> K[Remove connections]
    K --> L[Mark as unsaved]
    L --> M[Show success feedback]
    I --> N[Return to canvas]
```

### UI Simplification

#### Toolbar Redesign
The current toolbar will be simplified by removing the connect button:

**Before:**
```typescript
<Panel position="top-right">
  <div className="flex gap-2">
    <Button>Text</Button>
    <Button>Connect</Button> // This will be removed
  </div>
</Panel>
```

**After:**
```typescript
<Panel position="top-right">
  <div className="flex gap-2">
    <Button>Text</Button>
    // Connect functionality moved to context menu and drag-and-drop
  </div>
</Panel>
```

#### Alternative Connection Methods
1. **Context Menu**: Add "Connect" option to node context menu
2. **Drag and Drop**: Enable connection creation by dragging from node handles
3. **Keyboard Shortcut**: Implement Ctrl+L for connection mode

## Data Models

### Enhanced Node Data Structure
```typescript
interface EnhancedNodeData {
  // Existing fields
  id: string;
  type: string;
  position: { x: number; y: number };
  content: Record<string, any>;
  
  // Enhanced fields for better UX
  metadata: {
    createdAt: Date;
    updatedAt: Date;
    version: number;
  };
  
  // UI state
  ui: {
    isEditing: boolean;
    hasValidationErrors: boolean;
    isDirty: boolean;
  };
}
```

### Form Validation Schema
```typescript
interface ValidationSchema {
  [nodeType: string]: {
    [fieldKey: string]: ValidationRule[];
  };
}

interface ValidationRule {
  type: 'required' | 'minLength' | 'maxLength' | 'pattern' | 'custom';
  value?: any;
  message: string;
  validator?: (value: any) => boolean;
}
```

## Error Handling

### Form Validation Errors
- **Real-time validation**: Validate fields on blur and form submission
- **Error aggregation**: Collect and display all errors at once
- **Field-level errors**: Show errors directly below relevant fields
- **Accessibility**: Proper ARIA attributes for screen readers

### Deletion Error Handling
```typescript
const handleDeleteError = (error: Error, nodeId: string) => {
  // Log error for debugging
  console.error('Node deletion failed:', error);
  
  // Show user-friendly error message
  toast.error('Failed to delete node. Please try again.');
  
  // Revert any optimistic updates
  revertNodeDeletion(nodeId);
  
  // Re-enable UI interactions
  setIsDeleting(false);
};
```

### Network and State Errors
- **Optimistic updates**: Update UI immediately, revert on failure
- **Retry mechanisms**: Allow users to retry failed operations
- **Graceful degradation**: Maintain functionality when possible

## Testing Strategy

### Unit Testing
- **Form validation logic**: Test all validation rules and edge cases
- **Node deletion logic**: Test deletion with various node states and connections
- **UI state management**: Test modal states, form states, and error states

### Integration Testing
- **Form submission flow**: Test complete edit and save process
- **Delete confirmation flow**: Test full deletion process including confirmation
- **Context menu interactions**: Test all context menu actions

### Accessibility Testing
- **Keyboard navigation**: Test tab order and keyboard shortcuts
- **Screen reader compatibility**: Test with NVDA/JAWS
- **Color contrast**: Verify WCAG AA compliance
- **Focus management**: Test focus trapping in modals

### Visual Regression Testing
- **Form layouts**: Test form appearance across different node types
- **Modal responsiveness**: Test modal behavior on different screen sizes
- **Theme consistency**: Test both light and dark themes

## Performance Considerations

### Form Rendering Optimization
- **Memoization**: Use React.memo for form field components
- **Lazy loading**: Load complex form fields only when needed
- **Debounced validation**: Prevent excessive validation calls

### State Management Optimization
- **Selective updates**: Update only changed form fields
- **Batch operations**: Group related state updates
- **Memory management**: Clean up form state on modal close

### Bundle Size Impact
- **Tree shaking**: Ensure unused form components are not bundled
- **Code splitting**: Consider lazy loading for complex form types
- **Icon optimization**: Use optimized icon components

## Accessibility Features

### Keyboard Navigation
- **Tab order**: Logical tab sequence through form fields
- **Shortcuts**: Implement standard shortcuts (Ctrl+S for save, Escape to cancel)
- **Focus indicators**: Clear visual focus indicators

### Screen Reader Support
- **ARIA labels**: Proper labeling for all interactive elements
- **Error announcements**: Announce validation errors to screen readers
- **Form structure**: Use proper heading hierarchy and landmarks

### Visual Accessibility
- **Color contrast**: Minimum 4.5:1 contrast ratio for text
- **Focus indicators**: High contrast focus rings
- **Error styling**: Don't rely solely on color for error indication

## Migration Strategy

### Backward Compatibility
- **Existing data**: Ensure all existing node data remains compatible
- **API compatibility**: Maintain existing hook interfaces where possible
- **Gradual rollout**: Implement changes incrementally

### Deployment Approach
1. **Phase 1**: Enhanced form UI (non-breaking changes)
2. **Phase 2**: Delete functionality implementation
3. **Phase 3**: UI simplification (remove connect button)

### Rollback Plan
- **Feature flags**: Use feature flags for new functionality
- **Database migrations**: Ensure reversible data changes
- **Component versioning**: Maintain old components during transition