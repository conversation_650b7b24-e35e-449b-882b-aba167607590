import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  SubscriptionService,
  SubscriptionInfo,
  SubscriptionPlan,
  StorageTier,
  StorageQuotaInfo,
  ProratedBillingInfo,
} from '@/services/Subscription.service';
import { toast } from 'sonner';
import { useCallback } from 'react';
import { StorageForecast } from '@/hooks/useStorage';
import axios, { AxiosError } from 'axios';

interface UpgradePlanParams {
  newPlan: string;
  billingCycle: 'monthly' | 'yearly';
}

interface DowngradePlanParams {
  newPlan: string;
}

interface CancelSubscriptionParams {
  reason?: string;
}

interface UpgradeStorageTierParams {
  tierId: string;
  billingCycle: 'monthly' | 'yearly';
}

interface CalculateProratedBillingParams {
  targetPlanId: string;
  billingCycle: 'monthly' | 'yearly';
}

interface RequestQuotaExtensionParams {
  additionalDays: number;
  reason: string;
}

interface ProcessOverageBillingParams {
  paymentMethod?: string;
}

interface StorageReportData {
  currentUsage: number;
  totalQuota: number;
  usagePercentage: number;
  growthRate: number;
  projectedUsage: number;
  recommendedTier: StorageTier | null;
  analytics: any;
}

interface ApiErrorResponse {
  message: string;
  code?: string;
  details?: unknown;
}

const isApiError = (error: unknown): error is AxiosError<ApiErrorResponse> => {
  return axios.isAxiosError(error) && error.response?.data?.message !== undefined;
};

const getErrorMessage = (error: unknown): string => {
  if (isApiError(error)) {
    return error.response?.data?.message || 'An error occurred';
  }
  if (error instanceof Error) {
    return error.message;
  }
  return 'An unexpected error occurred';
};
function formatBytes(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

export const useSubscription = (organizationId?: string) => {
  const queryClient = useQueryClient();

  // Get subscription information
  const {
    data: subscriptionInfo,
    isLoading: isLoadingInfo,
    error: infoError,
    refetch: refetchSubscriptionInfo,
  } = useQuery<SubscriptionInfo>({
    queryKey: ['subscription', 'info', organizationId],
    queryFn: async () => {
      if (!organizationId) throw new Error('Organization ID is required');
      return SubscriptionService.getSubscriptionInfo(organizationId);
    },
    enabled: !!organizationId,
    staleTime: 1000 * 60 * 5, // 5 minutes
  });

  // Get billing history
  const {
    data: billingHistory,
    isLoading: isLoadingHistory,
    refetch: refetchBillingHistory,
  } = useQuery({
    queryKey: ['subscription', 'billing-history', organizationId],
    queryFn: async () => {
      if (!organizationId) throw new Error('Organization ID is required');
      return SubscriptionService.getBillingHistory(organizationId);
    },
    enabled: !!organizationId,
    staleTime: 1000 * 60 * 10, // 10 minutes
  });

  // Get storage quota information
  const {
    data: storageQuotaInfo,
    isLoading: isLoadingStorageQuota,
    error: storageQuotaError,
    refetch: refetchStorageQuotaInfo,
  } = useQuery<StorageQuotaInfo>({
    queryKey: ['subscription', 'storage-quota', organizationId],
    queryFn: async () => {
      if (!organizationId) throw new Error('Organization ID is required');
      return SubscriptionService.getStorageQuotaInfo(organizationId);
    },
    enabled: !!organizationId,
    staleTime: 1000 * 60 * 5, // 5 minutes
  });

  // Get storage forecast
  const {
    data: storageForecast,
    isLoading: isLoadingStorageForecast,
    error: storageForecastError,
    refetch: refetchStorageForecast,
  } = useQuery<StorageForecast>({
    queryKey: ['subscription', 'storage-forecast', organizationId],
    queryFn: async () => {
      if (!organizationId) throw new Error('Organization ID is required');
      const { forecastedUsage, recommendedTier, projectedOverageCosts } =
        await SubscriptionService.forecastStorageNeeds(organizationId, 6);

      // Calculate growth rate between first and last projection
      const growthRate =
        forecastedUsage.length > 1
          ? (forecastedUsage[forecastedUsage.length - 1].projectedBytes -
              forecastedUsage[0].projectedBytes) /
            forecastedUsage[0].projectedBytes
          : 0;

      return {
        growthRate,
        forecastData: forecastedUsage.map(usage => ({
          date: usage.date,
          projectedUsage: usage.projectedBytes,
          projectedUsageFormatted: formatBytes(usage.projectedBytes),
          recommendedTier,
        })),
        recommendations: {
          shortTerm: recommendedTier,
          longTerm:
            projectedOverageCosts > 0 ? 'Consider enterprise plan' : 'Current tier adequate',
        },
      };
    },
    enabled: !!organizationId,
    staleTime: 1000 * 60 * 60, // 1 hour
  });

  // Upgrade plan mutation
  const upgradePlanMutation = useMutation({
    mutationFn: async ({ newPlan, billingCycle }: UpgradePlanParams) => {
      if (!organizationId) throw new Error('Organization ID is required');
      return SubscriptionService.upgradePlan(organizationId, newPlan, billingCycle);
    },
    onSuccess: data => {
      toast.success(data.message);
      void queryClient.invalidateQueries({ queryKey: ['subscription'] });
    },
    onError: (error: unknown) => {
      toast.error(getErrorMessage(error));
    },
  });

  // Downgrade plan mutation
  const downgradePlanMutation = useMutation({
    mutationFn: async ({ newPlan }: DowngradePlanParams) => {
      if (!organizationId) throw new Error('Organization ID is required');
      return SubscriptionService.downgradePlan(organizationId, newPlan);
    },
    onSuccess: data => {
      toast.success(data.message);
      void queryClient.invalidateQueries({ queryKey: ['subscription'] });
    },
    onError: (error: unknown) => {
      toast.error(getErrorMessage(error));
    },
  });

  // Cancel subscription mutation
  const cancelSubscriptionMutation = useMutation({
    mutationFn: async ({ reason }: CancelSubscriptionParams) => {
      if (!organizationId) throw new Error('Organization ID is required');
      return SubscriptionService.cancelSubscription(organizationId, reason);
    },
    onSuccess: data => {
      toast.success(data.message);
      void queryClient.invalidateQueries({ queryKey: ['subscription'] });
    },
    onError: (error: unknown) => {
      toast.error(getErrorMessage(error));
    },
  });

  // Storage tier upgrade mutation
  const upgradeStorageTierMutation = useMutation({
    mutationFn: async ({ tierId, billingCycle }: UpgradeStorageTierParams) => {
      if (!organizationId) throw new Error('Organization ID is required');
      return SubscriptionService.upgradeStorageTier(organizationId, tierId, billingCycle);
    },
    onSuccess: () => {
      toast.success('Storage tier upgraded successfully!');
      void queryClient.invalidateQueries({ queryKey: ['subscription'] });
      void queryClient.invalidateQueries({ queryKey: ['storage'] });
    },
    onError: (error: unknown) => {
      toast.error(getErrorMessage(error));
    },
  });

  // Calculate prorated billing mutation
  const calculateProratedBillingMutation = useMutation({
    mutationFn: async ({
      targetPlanId,
      billingCycle,
    }: CalculateProratedBillingParams): Promise<ProratedBillingInfo> => {
      if (!organizationId) throw new Error('Organization ID is required');
      return SubscriptionService.calculateProratedBilling(
        organizationId,
        targetPlanId,
        billingCycle
      );
    },
  });

  // Request quota extension mutation
  const requestQuotaExtensionMutation = useMutation({
    mutationFn: async ({ additionalDays, reason }: RequestQuotaExtensionParams) => {
      if (!organizationId) throw new Error('Organization ID is required');
      return SubscriptionService.requestQuotaExtension(organizationId, additionalDays, reason);
    },
    onSuccess: data => {
      toast.success(
        `Grace period extended until ${new Date(data.newGracePeriodEnd).toLocaleDateString()}`
      );
      void queryClient.invalidateQueries({ queryKey: ['subscription'] });
      void queryClient.invalidateQueries({ queryKey: ['storage'] });
    },
    onError: (error: unknown) => {
      toast.error(getErrorMessage(error));
    },
  });

  // Process storage overage billing mutation
  const processOverageBillingMutation = useMutation({
    mutationFn: async ({ paymentMethod }: ProcessOverageBillingParams) => {
      if (!organizationId) throw new Error('Organization ID is required');
      return SubscriptionService.processStorageOverageBilling(organizationId, paymentMethod);
    },
    onSuccess: () => {
      toast.success('Storage overage payment processed successfully');
      void queryClient.invalidateQueries({ queryKey: ['subscription'] });
      void queryClient.invalidateQueries({ queryKey: ['storage'] });
    },
    onError: (error: unknown) => {
      toast.error(getErrorMessage(error));
    },
  });

  // Utility functions
  const getCurrentPlan = useCallback((): SubscriptionPlan | undefined => {
    if (!subscriptionInfo) return undefined;
    return subscriptionInfo.plan;
  }, [subscriptionInfo]);

  const isFeatureAvailable = useCallback(
    (feature: keyof SubscriptionPlan['features']): boolean => {
      if (!subscriptionInfo) return false;
      return subscriptionInfo.plan.features[feature] as boolean;
    },
    [subscriptionInfo]
  );

  const getUsagePercentage = useCallback(
    (type: 'users' | 'projects' | 'storage' | 'integrations'): number => {
      if (!subscriptionInfo) return 0;

      const usage = subscriptionInfo.usage;
      const plan = subscriptionInfo.plan;

      switch (type) {
        case 'users': {
          const userLimit = typeof plan.features.users === 'number' ? plan.features.users : 999999;
          return userLimit > 0 ? (usage.users / userLimit) * 100 : 0;
        }
        case 'projects': {
          const projectLimit =
            typeof plan.features.projects === 'number' ? plan.features.projects : 999999;
          return projectLimit > 0 ? (usage.projects / projectLimit) * 100 : 0;
        }
        case 'storage': {
          return usage.storage.percentage;
        }
        case 'integrations': {
          const integrationLimit =
            typeof plan.features.integrations === 'number' ? plan.features.integrations : 999999;
          return integrationLimit > 0 ? (usage.integrations / integrationLimit) * 100 : 0;
        }
        default: {
          return 0;
        }
      }
    },
    [subscriptionInfo]
  );

  const isLimitReached = useCallback(
    (type: 'users' | 'projects' | 'integrations'): boolean => {
      return getUsagePercentage(type) >= 100;
    },
    [getUsagePercentage]
  );

  const canUpgradeTo = useCallback(
    (planId: string): boolean => {
      if (!subscriptionInfo) return false;
      return SubscriptionService.canUpgradeToplan(subscriptionInfo.subscription.plan, planId);
    },
    [subscriptionInfo]
  );

  const canDowngradeTo = useCallback(
    (planId: string): boolean => {
      if (!subscriptionInfo) return false;
      return SubscriptionService.canDowngradeToplan(subscriptionInfo.subscription.plan, planId);
    },
    [subscriptionInfo]
  );

  const formatPrice = useCallback((amount: number): string => {
    return SubscriptionService.formatPrice(amount);
  }, []);

  const calculateYearlyDiscount = useCallback((plan: SubscriptionPlan): number => {
    return SubscriptionService.calculateYearlyDiscount(plan);
  }, []);

  const getNextBillingDate = useCallback((): Date | null => {
    return subscriptionInfo?.billing.nextPaymentDate || null;
  }, [subscriptionInfo]);

  const getDaysUntilBilling = useCallback((): number | null => {
    const nextBilling = getNextBillingDate();
    if (!nextBilling) return null;

    const now = new Date();
    const diffTime = nextBilling.getTime() - now.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }, [getNextBillingDate]);

  // Storage tier utility functions
  const getCurrentStorageTier = useCallback((): StorageTier | null => {
    return storageQuotaInfo?.currentTier || null;
  }, [storageQuotaInfo]);

  const getRecommendedStorageTier = useCallback((): StorageTier | null => {
    return storageQuotaInfo?.recommendedTier || null;
  }, [storageQuotaInfo]);

  const calculateProratedBilling = useCallback(
    async (
      targetTierId: string,
      billingCycle: 'monthly' | 'yearly'
    ): Promise<ProratedBillingInfo | null> => {
      try {
        const result = await calculateProratedBillingMutation.mutateAsync({
          targetPlanId: targetTierId,
          billingCycle,
        });
        return result;
      } catch (error) {
        toast.error('Failed to calculate prorated billing');
        return null;
      }
    },
    [calculateProratedBillingMutation]
  );

  const isStorageOverQuota = useCallback((): boolean => {
    return storageQuotaInfo?.isOverQuota || false;
  }, [storageQuotaInfo]);

  const getStorageGracePeriodDays = useCallback((): number | null => {
    return storageQuotaInfo?.daysInGracePeriod || null;
  }, [storageQuotaInfo]);

  const getStorageGracePeriodEnd = useCallback((): Date | null => {
    return storageQuotaInfo?.gracePeriodEnd || null;
  }, [storageQuotaInfo]);

  const isStorageTierUpgradable = useCallback((): boolean => {
    return storageQuotaInfo?.canUpgrade || false;
  }, [storageQuotaInfo]);

  const isStorageTierDowngradable = useCallback((): boolean => {
    return storageQuotaInfo?.canDowngrade || false;
  }, [storageQuotaInfo]);

  const upgradeStorageTier = useCallback(
    async (tierId: string, billingCycle: 'monthly' | 'yearly'): Promise<boolean> => {
      try {
        await upgradeStorageTierMutation.mutateAsync({ tierId, billingCycle });
        return true;
      } catch (error) {
        return false;
      }
    },
    [upgradeStorageTierMutation]
  );

  const requestQuotaExtension = useCallback(
    async (additionalDays: number, reason: string): Promise<boolean> => {
      try {
        await requestQuotaExtensionMutation.mutateAsync({ additionalDays, reason });
        return true;
      } catch (error) {
        toast.error('Failed to request quota extension');
        return false;
      }
    },
    [requestQuotaExtensionMutation]
  );

  const processOverageBilling = useCallback(
    async (paymentMethod?: string): Promise<boolean> => {
      try {
        await processOverageBillingMutation.mutateAsync({ paymentMethod });
        return true;
      } catch (error) {
        toast.error('Failed to process overage payment');
        return false;
      }
    },
    [processOverageBillingMutation]
  );

  const getSubscriptionAnalytics = useCallback(
    async (period: 'week' | 'month' | 'year' = 'month') => {
      if (!organizationId) throw new Error('Organization ID is required');
      try {
        return await SubscriptionService.getSubscriptionAnalytics(organizationId, period);
      } catch (error) {
        toast.error('Failed to fetch subscription analytics');
        return null;
      }
    },
    [organizationId]
  );

  const getStorageUsageAnalytics = useCallback(
    async (period: 'week' | 'month' | 'year' = 'month') => {
      if (!organizationId) throw new Error('Organization ID is required');
      try {
        return await SubscriptionService.getStorageUsageAnalytics(organizationId, period);
      } catch (error) {
        toast.error('Failed to fetch storage usage analytics');
        return null;
      }
    },
    [organizationId]
  );

  const scheduleStorageTierChange = useCallback(
    async (tierId: string, effectiveDate: Date): Promise<boolean> => {
      if (!organizationId) throw new Error('Organization ID is required');
      try {
        await axios.put('/api/subscription/storage-tiers', {
          action: 'schedule_upgrade',
          organizationId,
          tierId,
          effectiveDate,
        });
        toast.success('Storage tier change scheduled successfully');
        void queryClient.invalidateQueries({ queryKey: ['subscription'] });
        return true;
      } catch (error) {
        toast.error('Failed to schedule storage tier change');
        return false;
      }
    },
    [organizationId, queryClient]
  );

  const enableAutoStorageUpgrade = useCallback(
    async (autoUpgrade: boolean): Promise<boolean> => {
      if (!organizationId) throw new Error('Organization ID is required');
      try {
        await axios.put('/api/subscription/storage-tiers', {
          action: 'enable_auto_upgrade',
          organizationId,
          autoUpgrade,
        });
        toast.success(
          autoUpgrade ? 'Automatic storage upgrade enabled' : 'Automatic storage upgrade disabled'
        );
        void queryClient.invalidateQueries({ queryKey: ['subscription'] });
        return true;
      } catch (error) {
        toast.error('Failed to update automatic storage upgrade settings');
        return false;
      }
    },
    [organizationId, queryClient]
  );

  const generateStorageReport = useCallback(async (): Promise<StorageReportData | null> => {
    if (!organizationId) throw new Error('Organization ID is required');
    try {
      const analytics = await getStorageUsageAnalytics('month');
      const forecast = await refetchStorageForecast();
      const quotaInfo = await refetchStorageQuotaInfo();

      return {
        currentUsage: quotaInfo.data?.usedStorage || 0,
        totalQuota: quotaInfo.data?.totalAvailableStorage || 0,
        usagePercentage: quotaInfo.data?.usagePercentage || 0,
        growthRate: (forecast.data as StorageForecast)?.growthRate || 0,
        projectedUsage: (forecast.data as StorageForecast)?.forecastData[2]?.projectedUsage || 0,
        recommendedTier:
          (forecast.data as StorageForecast)?.forecastData[2]?.recommendedTier || null,
        analytics,
      };
    } catch (error) {
      toast.error('Failed to generate storage report');
      return null;
    }
  }, [organizationId, getStorageUsageAnalytics, refetchStorageForecast, refetchStorageQuotaInfo]);

  return {
    // Data
    subscriptionInfo,
    billingHistory,
    storageQuotaInfo,
    storageForecast,

    // Loading states
    isLoadingInfo,
    isLoadingHistory,
    isLoadingStorageQuota,
    isLoadingStorageForecast,

    // Errors
    infoError,
    storageQuotaError,
    storageForecastError,

    // Mutations
    upgradePlan: upgradePlanMutation.mutate,
    downgradePlan: downgradePlanMutation.mutate,
    cancelSubscription: cancelSubscriptionMutation.mutate,
    upgradeStorageTier,
    calculateProratedBilling,
    requestQuotaExtension,
    processOverageBilling,
    scheduleStorageTierChange,
    enableAutoStorageUpgrade,

    // Mutation states
    isUpgrading: upgradePlanMutation.isPending,
    isDowngrading: downgradePlanMutation.isPending,
    isCancelling: cancelSubscriptionMutation.isPending,
    isUpgradingStorageTier: upgradeStorageTierMutation.isPending,
    isCalculatingBilling: calculateProratedBillingMutation.isPending,
    isRequestingExtension: requestQuotaExtensionMutation.isPending,
    isProcessingOverage: processOverageBillingMutation.isPending,

    // Utility functions
    getCurrentPlan,
    isFeatureAvailable,
    getUsagePercentage,
    isLimitReached,
    canUpgradeTo,
    canDowngradeTo,
    formatPrice,
    calculateYearlyDiscount,
    getNextBillingDate,
    getDaysUntilBilling,

    // Storage tier utilities
    getCurrentStorageTier,
    getRecommendedStorageTier,
    isStorageOverQuota,
    getStorageGracePeriodDays,
    getStorageGracePeriodEnd,
    isStorageTierUpgradable,
    isStorageTierDowngradable,
    getSubscriptionAnalytics,
    getStorageUsageAnalytics,
    generateStorageReport,

    // Refetch functions
    refetchSubscriptionInfo,
    refetchBillingHistory,
    refetchStorageQuotaInfo,
    refetchStorageForecast,
  };
};

export default useSubscription;
