import { google, calendar_v3 } from 'googleapis';
import { OAuth2Client } from 'google-auth-library';
import { Integration } from '@/models/Integration';
import { connectDB } from '@/Utility/db';

export interface GoogleCalendarConfig {
  clientId: string;
  clientSecret: string;
  redirectUri: string;
}

export interface CalendarEvent {
  id?: string;
  summary: string;
  description?: string;
  start: {
    dateTime?: string;
    date?: string;
    timeZone?: string;
  };
  end: {
    dateTime?: string;
    date?: string;
    timeZone?: string;
  };
  attendees?: Array<{
    email: string;
    displayName?: string;
    responseStatus?: 'needsAction' | 'declined' | 'tentative' | 'accepted';
  }>;
  location?: string;
  reminders?: {
    useDefault?: boolean;
    overrides?: Array<{
      method: 'email' | 'popup';
      minutes: number;
    }>;
  };
  colorId?: string;
  visibility?: 'default' | 'public' | 'private' | 'confidential';
  extendedProperties?: {
    private?: Record<string, string>;
    shared?: Record<string, string>;
  };
}

export interface TaskSyncData {
  taskId: string;
  title: string;
  description?: string;
  dueDate?: Date;
  priority: 'Low' | 'Medium' | 'High';
  projectName?: string;
  assigneeName?: string;
}

export interface SyncResult {
  success: boolean;
  eventsCreated: number;
  eventsUpdated: number;
  eventsDeleted: number;
  errors: string[];
}

class GoogleCalendarService {
  private config: GoogleCalendarConfig;
  private oauth2Client: OAuth2Client;
  private calendar: calendar_v3.Calendar;

  constructor() {
    this.config = {
      clientId: process.env.GOOGLE_CLIENT_ID || '',
      clientSecret: process.env.GOOGLE_CLIENT_SECRET || '',
      redirectUri:
        process.env.GOOGLE_REDIRECT_URI || `${process.env.NEXTAUTH_URL}/api/auth/callback/google`,
    };

    this.oauth2Client = new google.auth.OAuth2(
      this.config.clientId,
      this.config.clientSecret,
      this.config.redirectUri
    );

    this.calendar = google.calendar({ version: 'v3', auth: this.oauth2Client });
  }

  // OAuth2 Authentication Methods
  generateAuthUrl(userId: string, state?: string): string {
    const scopes = [
      'https://www.googleapis.com/auth/calendar.events',
      'https://www.googleapis.com/auth/userinfo.email',
      'https://www.googleapis.com/auth/userinfo.profile',
    ];

    return this.oauth2Client.generateAuthUrl({
      access_type: 'offline',
      scope: scopes,
      state: state || userId,
      prompt: 'consent',
    });
  }

  async handleAuthCallback(
    code: string,
    userId: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      await connectDB();
      // Use getToken instead of getAccessToken for OAuth2
      const { tokens } = await this.oauth2Client.getToken(code);
      if (!tokens.access_token) {
        return { success: false, error: 'No access token received' };
      }
      this.oauth2Client.setCredentials(tokens);
      // Get user info
      const oauth2 = google.oauth2({ version: 'v2', auth: this.oauth2Client });
      const userInfo = await oauth2.userinfo.get();
      // Save integration to database
      const integrationData = {
        userId,
        provider: 'google-calendar',
        providerUserId: userInfo.data.id,
        accessToken: tokens.access_token,
        refreshToken: tokens.refresh_token,
        expiresAt: tokens.expiry_date ? new Date(tokens.expiry_date) : undefined,
        scopes: ['https://www.googleapis.com/auth/calendar.events'],
        metadata: {
          email: userInfo.data.email,
          name: userInfo.data.name,
          picture: userInfo.data.picture,
        },
        lastSyncedAt: new Date(),
      };
      await Integration.findOneAndUpdate({ userId, provider: 'google-calendar' }, integrationData, {
        upsert: true,
        new: true,
      });
      return { success: true };
    } catch (error: any) {
      console.error('Google Calendar auth callback error:', error);
      return { success: false, error: error.message || 'Authentication failed' };
    }
  }

  async getIntegration(userId: string): Promise<any | null> {
    try {
      await connectDB();
      return await Integration.findOne({ userId, provider: 'google-calendar' });
    } catch (error: any) {
      console.error('Error getting Google Calendar integration:', error);
      return null;
    }
  }

  async setupAuth(userId: string): Promise<boolean> {
    try {
      const integration = await this.getIntegration(userId);
      if (!integration || !integration.accessToken) {
        return false;
      }
      // Check if token needs refresh
      if (integration.expiresAt && new Date() >= integration.expiresAt) {
        const refreshed = await this.refreshAccessToken(userId);
        if (!refreshed) return false;
      }
      this.oauth2Client.setCredentials({
        access_token: integration.accessToken,
        refresh_token: integration.refreshToken,
      });
      return true;
    } catch (error: any) {
      console.error('Error setting up Google Calendar auth:', error);
      return false;
    }
  }

  async refreshAccessToken(userId: string): Promise<boolean> {
    try {
      const integration = await this.getIntegration(userId);
      if (!integration || !integration.refreshToken) {
        return false;
      }
      this.oauth2Client.setCredentials({
        refresh_token: integration.refreshToken,
      });
      const { credentials } = await this.oauth2Client.refreshAccessToken();
      const refreshedCredentials = credentials;
      if (refreshedCredentials.access_token) {
        await Integration.findOneAndUpdate(
          { userId, provider: 'google-calendar' },
          {
            accessToken: refreshedCredentials.access_token,
            expiresAt: refreshedCredentials.expiry_date
              ? new Date(refreshedCredentials.expiry_date)
              : undefined,
          }
        );
        return true;
      }
      return false;
    } catch (error: any) {
      console.error('Error refreshing Google Calendar token:', error);
      return false;
    }
  }

  // Calendar Operations
  async getCalendars(userId: string): Promise<calendar_v3.Schema$CalendarListEntry[] | null> {
    try {
      const authSetup = await this.setupAuth(userId);
      if (!authSetup) return null;
      const response = await this.calendar.calendarList.list();
      return response.data.items || [];
    } catch (error: any) {
      console.error('Error getting calendars:', error);
      return null;
    }
  }

  async getPrimaryCalendar(userId: string): Promise<string | null> {
    try {
      const calendars = await this.getCalendars(userId);
      if (!calendars) return null;
      const primaryCalendar = calendars.find(cal => cal.primary);
      return (primaryCalendar?.id as string) || 'primary';
    } catch (error: any) {
      console.error('Error getting primary calendar:', error);
      return null;
    }
  }

  // Event CRUD Operations
  async createEvent(
    userId: string,
    eventData: CalendarEvent,
    calendarId?: string
  ): Promise<calendar_v3.Schema$Event | null> {
    try {
      const authSetup = await this.setupAuth(userId);
      if (!authSetup) return null;
      const calendar = calendarId || (await this.getPrimaryCalendar(userId));
      if (!calendar) return null;
      const response = await this.calendar.events.insert({
        calendarId: calendar,
        requestBody: eventData,
      });
      return response.data;
    } catch (error: any) {
      console.error('Error creating calendar event:', error);
      return null;
    }
  }

  async updateEvent(
    userId: string,
    eventId: string,
    eventData: Partial<CalendarEvent>,
    calendarId?: string
  ): Promise<calendar_v3.Schema$Event | null> {
    try {
      const authSetup = await this.setupAuth(userId);
      if (!authSetup) return null;
      const calendar = calendarId || (await this.getPrimaryCalendar(userId));
      if (!calendar) return null;
      const response = await this.calendar.events.update({
        calendarId: calendar,
        eventId,
        requestBody: eventData,
      });
      return response.data;
    } catch (error: any) {
      console.error('Error updating calendar event:', error);
      return null;
    }
  }

  async deleteEvent(userId: string, eventId: string, calendarId?: string): Promise<boolean> {
    try {
      const authSetup = await this.setupAuth(userId);
      if (!authSetup) return false;
      const calendar = calendarId || (await this.getPrimaryCalendar(userId));
      if (!calendar) return false;
      await this.calendar.events.delete({
        calendarId: calendar,
        eventId,
      });
      return true;
    } catch (error: any) {
      console.error('Error deleting calendar event:', error);
      return false;
    }
  }

  async getEvent(
    userId: string,
    eventId: string,
    calendarId?: string
  ): Promise<calendar_v3.Schema$Event | null> {
    try {
      const authSetup = await this.setupAuth(userId);
      if (!authSetup) return null;
      const calendar = calendarId || (await this.getPrimaryCalendar(userId));
      if (!calendar) return null;
      const response = await this.calendar.events.get({
        calendarId: calendar,
        eventId,
      });
      return response.data;
    } catch (error: any) {
      console.error('Error getting calendar event:', error);
      return null;
    }
  }

  async listEvents(
    userId: string,
    options?: {
      calendarId?: string;
      timeMin?: string;
      timeMax?: string;
      maxResults?: number;
      orderBy?: 'startTime' | 'updated';
      singleEvents?: boolean;
    }
  ): Promise<calendar_v3.Schema$Event[] | null> {
    try {
      const authSetup = await this.setupAuth(userId);
      if (!authSetup) return null;
      const calendar = options?.calendarId || (await this.getPrimaryCalendar(userId));
      if (!calendar) return null;
      const response = await this.calendar.events.list({
        calendarId: calendar,
        timeMin: options?.timeMin,
        timeMax: options?.timeMax,
        maxResults: options?.maxResults || 250,
        orderBy: options?.orderBy || 'startTime',
        singleEvents: options?.singleEvents !== false,
      });
      return response.data.items || [];
    } catch (error: any) {
      console.error('Error listing calendar events:', error);
      return null;
    }
  }

  // Task Synchronization
  async syncTaskToCalendar(
    userId: string,
    taskData: TaskSyncData
  ): Promise<calendar_v3.Schema$Event | null> {
    try {
      if (!taskData.dueDate) return null;
      const eventData: CalendarEvent = {
        summary: `[Task] ${taskData.title}`,
        description: this.generateTaskDescription(taskData),
        start: {
          dateTime: taskData.dueDate.toISOString(),
          timeZone: 'UTC',
        },
        end: {
          dateTime: new Date(taskData.dueDate.getTime() + 60 * 60 * 1000).toISOString(), // 1 hour duration
          timeZone: 'UTC',
        },
        colorId: this.getPriorityColorId(taskData.priority),
        extendedProperties: {
          private: {
            taskId: taskData.taskId,
            source: 'taskmantra',
            priority: taskData.priority,
          },
        },
        reminders: {
          useDefault: false,
          overrides: [
            { method: 'popup', minutes: 60 },
            { method: 'email', minutes: 120 },
          ],
        },
      };
      return await this.createEvent(userId, eventData);
    } catch (error: any) {
      console.error('Error syncing task to calendar:', error);
      return null;
    }
  }

  async syncTasksToCalendar(userId: string, tasks: TaskSyncData[]): Promise<SyncResult> {
    const result: SyncResult = {
      success: true,
      eventsCreated: 0,
      eventsUpdated: 0,
      eventsDeleted: 0,
      errors: [],
    };
    try {
      for (const task of tasks) {
        const event = await this.syncTaskToCalendar(userId, task);
        if (event) {
          result.eventsCreated++;
        } else {
          result.errors.push(`Failed to sync task: ${task.title}`);
        }
      }
    } catch (error: any) {
      result.success = false;
      result.errors.push(error.message || 'Unknown error during sync');
    }
    return result;
  }

  async removeTaskFromCalendar(userId: string, taskId: string): Promise<boolean> {
    try {
      // Find events with the taskId in extended properties
      const events = await this.listEvents(userId, {
        timeMin: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000).toISOString(), // 1 year back
        timeMax: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(), // 1 year forward
      });
      if (!events) return false;
      const taskEvents = events.filter(
        event =>
          event.extendedProperties?.private?.taskId === taskId &&
          event.extendedProperties?.private?.source === 'taskmantra'
      );
      for (const event of taskEvents) {
        if (event.id) {
          await this.deleteEvent(userId, event.id);
        }
      }
      return true;
    } catch (error: any) {
      console.error('Error removing task from calendar:', error);
      return false;
    }
  }

  async updateTaskInCalendar(userId: string, taskData: TaskSyncData): Promise<boolean> {
    try {
      // First remove existing events for this task
      await this.removeTaskFromCalendar(userId, taskData.taskId);
      // Then create new event if task has due date
      if (taskData.dueDate) {
        const event = await this.syncTaskToCalendar(userId, taskData);
        return event !== null;
      }
      return true;
    } catch (error: any) {
      console.error('Error updating task in calendar:', error);
      return false;
    }
  }

  // Utility Methods
  private generateTaskDescription(taskData: TaskSyncData): string {
    let description = `Task: ${taskData.title}\n\n`;

    if (taskData.description) {
      description += `Description: ${taskData.description}\n\n`;
    }

    if (taskData.projectName) {
      description += `Project: ${taskData.projectName}\n`;
    }

    if (taskData.assigneeName) {
      description += `Assigned to: ${taskData.assigneeName}\n`;
    }

    description += `Priority: ${taskData.priority}\n`;
    description += `\nManaged by TaskMantra`;

    return description;
  }

  private getPriorityColorId(priority: string): string {
    switch (priority.toLowerCase()) {
      case 'high':
        return '11'; // Red
      case 'medium':
        return '5'; // Yellow
      case 'low':
        return '2'; // Green
      default:
        return '1'; // Blue (default)
    }
  }

  async getConnectionStatus(userId: string): Promise<{
    connected: boolean;
    email?: string;
    name?: string;
    lastSync?: Date;
    error?: string;
  }> {
    try {
      const integration = await this.getIntegration(userId);
      if (!integration) {
        return { connected: false, error: 'No integration found' };
      }
      // Test the connection
      const authSetup = await this.setupAuth(userId);
      if (!authSetup) {
        return {
          connected: false,
          error: 'Authentication failed',
          email: integration.metadata?.email,
          name: integration.metadata?.name,
        };
      }
      return {
        connected: true,
        email: integration.metadata?.email,
        name: integration.metadata?.name,
        lastSync: integration.lastSyncedAt,
      };
    } catch (error: any) {
      return {
        connected: false,
        error: error.message || 'Connection check failed',
      };
    }
  }

  async disconnect(userId: string): Promise<boolean> {
    try {
      await connectDB();
      await Integration.findOneAndDelete({ userId, provider: 'google-calendar' });
      return true;
    } catch (error: any) {
      console.error('Error disconnecting Google Calendar:', error);
      return false;
    }
  }

  // Webhook Support (for real-time updates)
  async setupWebhook(
    userId: string,
    calendarId?: string
  ): Promise<{ success: boolean; channelId?: string; error?: string }> {
    try {
      const authSetup = await this.setupAuth(userId);
      if (!authSetup) return { success: false, error: 'Authentication failed' };
      const calendar = calendarId || (await this.getPrimaryCalendar(userId));
      if (!calendar) return { success: false, error: 'Calendar not found' };
      const channelId = `taskmantra-${userId}-${Date.now()}`;
      const webhookUrl = `${process.env.NEXTAUTH_URL}/api/webhooks/google-calendar`;
      const response = await this.calendar.events.watch({
        calendarId: calendar,
        requestBody: {
          id: channelId,
          type: 'web_hook',
          address: webhookUrl,
          token: userId, // Use userId as verification token
        },
      });
      return { success: true, channelId: response.data.id || undefined };
    } catch (error: any) {
      console.error('Error setting up Google Calendar webhook:', error);
      return { success: false, error: error.message || 'Webhook setup failed' };
    }
  }

  async handleWebhookNotification(
    _channelId: string,
    _resourceId: string,
    userId: string
  ): Promise<void> {
    try {
      console.log(`Received Google Calendar webhook notification for user ${userId}`);
      // Update last sync time
      await Integration.findOneAndUpdate(
        { userId, provider: 'google-calendar' },
        { lastSyncedAt: new Date() }
      );
      // You can implement additional logic here to sync changes back to your tasks
      // For example, detect when calendar events are modified and update corresponding tasks
    } catch (error: any) {
      console.error('Error handling Google Calendar webhook:', error);
    }
  }
}

const googleCalendarServiceInstance = new GoogleCalendarService();
export default googleCalendarServiceInstance;
