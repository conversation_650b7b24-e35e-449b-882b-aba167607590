interface BeforeInstallPromptEvent extends Event {
  readonly platforms: string[];
  readonly userChoice: Promise<{
    outcome: 'accepted' | 'dismissed';
    platform: string;
  }>;
  prompt(): Promise<void>;
}

interface PWACapabilities {
  hasServiceWorker: boolean;
  hasPushNotifications: boolean;
  hasBackgroundSync: boolean;
  hasPeriodicSync: boolean;
  hasWebShare: boolean;
  hasBeforeInstallPrompt: boolean;
  hasNotificationAPI: boolean;
  hasStorageAPI: boolean;
  isStandalone: boolean;
  isDisplayModeStandalone: boolean;
}

interface PushSubscriptionOptions {
  applicationServerKey: ArrayBuffer | string;
  userVisibleOnly: boolean;
}

interface CachingStrategy {
  name: string;
  patterns: RegExp[];
  strategy: 'cacheFirst' | 'networkFirst' | 'staleWhileRevalidate' | 'networkOnly' | 'cacheOnly';
  maxEntries?: number;
  maxAgeSeconds?: number;
}

interface AnalyticsEvent {
  event: string;
  data: Record<string, any>;
  timestamp: string;
}

// Add missing type definitions for NotificationPermission and PermissionState
// These are standard web types, but if not available, define them for TS

type NotificationPermission = 'default' | 'denied' | 'granted';
type PermissionState = 'granted' | 'denied' | 'prompt';

class PWAService {
  private static instance: PWAService;
  private serviceWorkerRegistration: ServiceWorkerRegistration | null = null;
  private deferredPrompt: BeforeInstallPromptEvent | null = null;
  private capabilities: PWACapabilities | null = null;
  private pushSubscription: PushSubscription | null = null;
  private analyticsQueue: AnalyticsEvent[] = [];
  private cachingStrategies: CachingStrategy[] = [];
  private isInitialized = false;

  // Private constructor for singleton pattern
  private constructor() {
    // Initialize default caching strategies
    this.cachingStrategies = [
      {
        name: 'api',
        patterns: [/^\/api\//],
        strategy: 'networkFirst',
        maxAgeSeconds: 60 * 60, // 1 hour
      },
      {
        name: 'static',
        patterns: [/\.(js|css|png|jpg|jpeg|svg|json)$/],
        strategy: 'cacheFirst',
        maxAgeSeconds: 7 * 24 * 60 * 60, // 7 days
      },
      {
        name: 'html',
        patterns: [/\/$/],
        strategy: 'networkFirst',
      },
    ];
  }

  // Singleton access
  public static getInstance(): PWAService {
    if (!PWAService.instance) {
      PWAService.instance = new PWAService();
    }
    return PWAService.instance;
  }

  // Initialize the PWA service
  public async initialize(): Promise<boolean> {
    if (this.isInitialized) {
      return true;
    }
    try {
      // Detect capabilities
      await this.detectCapabilities();
      // Register service worker if available
      if (this.capabilities?.hasServiceWorker) {
        await this.registerServiceWorker();
      }
      // Set up event listeners
      this.setupEventListeners();
      // Try to restore push subscription if user was already subscribed
      if (this.capabilities?.hasPushNotifications && this.serviceWorkerRegistration) {
        this.pushSubscription = await this.serviceWorkerRegistration.pushManager.getSubscription();
      }
      // Start analytics sync mechanism
      this.loadAnalyticsFromStorage();
      this.syncAnalytics();
      this.isInitialized = true;
      return true;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to initialize PWA service:', error);
      return false;
    }
  }

  // Service Worker Registration
  public async registerServiceWorker(path = '/sw.js'): Promise<ServiceWorkerRegistration | null> {
    if (!('serviceWorker' in navigator)) {
      return null;
    }
    try {
      const registration = await navigator.serviceWorker.register(path, { scope: '/' });
      this.serviceWorkerRegistration = registration;
      // Set up communication with the service worker
      this.setupServiceWorkerCommunication();
      return registration;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Service worker registration failed:', error);
      this.trackEvent('sw_registration_failed', { error: String(error) });
      return null;
    }
  }

  // Service Worker Communication
  private setupServiceWorkerCommunication(): void {
    if (!navigator.serviceWorker) {
      return;
    }

    navigator.serviceWorker.addEventListener('message', event => {
      const data = event.data;

      if (data?.type === 'SW_UPDATED') {
        this.trackEvent('sw_updated', data);
        this.dispatchEvent('pwaUpdate', { detail: data });
      } else if (data?.type === 'SYNC_COMPLETE') {
        this.trackEvent('sync_complete', data);
        this.dispatchEvent('syncComplete', { detail: data });
      } else if (data?.type === 'SYNC_FAILED') {
        this.trackEvent('sync_failed', data);
        this.dispatchEvent('syncFailed', { detail: data });
      } else if (data?.type === 'OFFLINE_QUEUE_UPDATED') {
        this.dispatchEvent('offlineQueueUpdated', { detail: data });
      } else if (data?.type === 'CACHE_UPDATED') {
        this.dispatchEvent('cacheUpdated', { detail: data });
      }
    });
  }

  // Send message to Service Worker
  public async sendMessageToSW(message: any): Promise<any> {
    if (!navigator.serviceWorker || !navigator.serviceWorker.controller) {
      return false;
    }
    return new Promise((resolve, reject) => {
      const messageChannel = new MessageChannel();
      messageChannel.port1.onmessage = event => {
        if (event.data.error) {
          reject(event.data.error);
        } else {
          resolve(event.data);
        }
      };
      // navigator.serviceWorker.controller is checked above
      (navigator.serviceWorker.controller as ServiceWorker).postMessage(message, [
        messageChannel.port2,
      ]);
    });
  }

  // Events handling
  private setupEventListeners(): void {
    // Handle install prompt
    window.addEventListener('beforeinstallprompt', event => {
      // Prevent Chrome 76+ from automatically showing the prompt
      event.preventDefault();
      // Stash the event so it can be triggered later
      this.deferredPrompt = event as BeforeInstallPromptEvent;

      this.trackEvent('install_prompt_available', {
        platforms: (event as BeforeInstallPromptEvent).platforms,
      });

      this.dispatchEvent('installPromptAvailable', {});
    });

    // Handle successful installation
    window.addEventListener('appinstalled', () => {
      this.deferredPrompt = null;
      this.trackEvent('app_installed', {});
      this.dispatchEvent('appInstalled', {});
    });

    // Handle online/offline status
    window.addEventListener('online', () => {
      this.trackEvent('device_online', {});
      this.syncOfflineData();
      this.dispatchEvent('onlineStatusChanged', { detail: { online: true } });
    });

    window.addEventListener('offline', () => {
      this.trackEvent('device_offline', {});
      this.dispatchEvent('onlineStatusChanged', { detail: { online: false } });
    });
  }

  // Feature and capability detection
  private async detectCapabilities(): Promise<PWACapabilities> {
    this.capabilities = {
      hasServiceWorker: 'serviceWorker' in navigator,
      hasPushNotifications: 'PushManager' in window,
      hasBackgroundSync: 'SyncManager' in window,
      hasPeriodicSync: 'PeriodicSyncManager' in window,
      hasWebShare: 'share' in navigator,
      hasBeforeInstallPrompt: true, // Can't really detect this beforehand
      hasNotificationAPI: 'Notification' in window,
      hasStorageAPI: 'storage' in navigator && 'estimate' in navigator.storage,
      isStandalone: window.matchMedia('(display-mode: standalone)').matches,
      isDisplayModeStandalone:
        window.matchMedia('(display-mode: standalone)').matches ||
        window.matchMedia('(display-mode: fullscreen)').matches ||
        window.matchMedia('(display-mode: minimal-ui)').matches ||
        (navigator as any).standalone === true, // iOS Safari
    };

    this.trackEvent('capabilities_detected', this.capabilities);

    return this.capabilities;
  }

  // Installation management
  public async installPWA(): Promise<boolean> {
    if (!this.deferredPrompt) {
      return false;
    }

    try {
      // Show the installation prompt
      this.deferredPrompt.prompt();

      // Wait for the user to respond to the prompt
      const choiceResult = await this.deferredPrompt.userChoice;

      // Clear the deferred prompt reference
      this.deferredPrompt = null;

      if (choiceResult.outcome === 'accepted') {
        this.trackEvent('install_accepted', { platform: choiceResult.platform });
        return true;
      } else {
        this.trackEvent('install_dismissed', { platform: choiceResult.platform });
        return false;
      }
    } catch (error) {
      this.trackEvent('install_error', { error: String(error) });
      return false;
    }
  }

  // Check if PWA is installed
  public async isPWAInstalled(): Promise<boolean> {
    // iOS Safari specific check
    if ((navigator as any).standalone) {
      return true;
    }

    // Check for display mode
    if (window.matchMedia('(display-mode: standalone)').matches) {
      return true;
    }

    // Check if install prompt is NOT available (might be already installed)
    if (!this.deferredPrompt && this.capabilities?.isDisplayModeStandalone) {
      return true;
    }

    return false;
  }

  // Update checking and management
  public async checkForUpdates(): Promise<boolean> {
    if (!this.serviceWorkerRegistration) {
      return false;
    }
    try {
      await this.serviceWorkerRegistration.update();
      return this.serviceWorkerRegistration.waiting !== null;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to check for updates:', error);
      return false;
    }
  }

  public async applyUpdate(): Promise<boolean> {
    if (!this.serviceWorkerRegistration || !this.serviceWorkerRegistration.waiting) {
      return false;
    }
    try {
      this.serviceWorkerRegistration.waiting.postMessage({ type: 'SKIP_WAITING' });
      this.trackEvent('update_applied', {});
      return true;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to apply update:', error);
      return false;
    }
  }

  // Push notification management
  public async subscribeToPushNotifications(
    options?: Partial<PushSubscriptionOptions>
  ): Promise<PushSubscription | null> {
    if (!this.serviceWorkerRegistration || !this.capabilities?.hasPushNotifications) {
      return null;
    }
    try {
      // Request permission if needed
      const permission = await this.requestNotificationPermission();
      if (permission !== 'granted') {
        return null;
      }
      // Get server's public key
      const serverKeyResponse = await fetch('/api/push/vapid-key');
      const { publicKey } = await serverKeyResponse.json();
      // Subscribe user
      const subscription = await this.serviceWorkerRegistration.pushManager.subscribe({
        userVisibleOnly: true, // Required for Chrome
        applicationServerKey: publicKey,
        ...options,
      });
      // Save subscription to server
      await fetch('/api/push/subscribe', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(subscription),
      });
      this.pushSubscription = subscription;
      this.trackEvent('push_subscribed', { endpoint: subscription.endpoint });
      return subscription;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to subscribe to push notifications:', error);
      this.trackEvent('push_subscription_failed', { error: String(error) });
      return null;
    }
  }

  public async unsubscribeFromPushNotifications(): Promise<boolean> {
    if (!this.pushSubscription) {
      return true;
    }
    try {
      const successful = await this.pushSubscription.unsubscribe();
      if (successful) {
        // Notify server
        await fetch('/api/push/unsubscribe', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            endpoint: this.pushSubscription.endpoint,
          }),
        });
        this.pushSubscription = null;
        this.trackEvent('push_unsubscribed', {});
        return true;
      }
      return false;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to unsubscribe from push notifications:', error);
      return false;
    }
  }

  public async requestNotificationPermission(): Promise<NotificationPermission> {
    if (!('Notification' in window)) {
      return 'denied';
    }
    try {
      const permission = await Notification.requestPermission();
      this.trackEvent('notification_permission_changed', { permission });
      return permission as NotificationPermission;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to request notification permission:', error);
      return 'denied';
    }
  }

  // Background sync
  public async registerBackgroundSync(tag: string): Promise<boolean> {
    if (!this.serviceWorkerRegistration || !this.capabilities?.hasBackgroundSync) {
      return false;
    }
    try {
      // Use 'as any' for ServiceWorkerRegistration to access sync
      await (this.serviceWorkerRegistration as any).sync.register(tag);
      this.trackEvent('background_sync_registered', { tag });
      return true;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to register background sync:', error);
      this.trackEvent('background_sync_failed', { tag, error: String(error) });
      return false;
    }
  }

  // Periodic background sync
  public async registerPeriodicSync(
    tag: string,
    options: { minInterval: number }
  ): Promise<boolean> {
    if (
      !this.serviceWorkerRegistration ||
      !this.capabilities?.hasPeriodicSync ||
      !('periodicSync' in this.serviceWorkerRegistration)
    ) {
      return false;
    }
    try {
      const periodicSyncManager = (this.serviceWorkerRegistration as any).periodicSync;
      await periodicSyncManager.register(tag, options);
      this.trackEvent('periodic_sync_registered', { tag, interval: options.minInterval });
      return true;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to register periodic sync:', error);
      return false;
    }
  }

  // Cache management
  public async clearCache(cacheName?: string): Promise<boolean> {
    if (!('caches' in window)) {
      return false;
    }
    try {
      if (cacheName) {
        await caches.delete(cacheName);
      } else {
        const keys = await caches.keys();
        await Promise.all(keys.map(key => caches.delete(key)));
      }
      this.trackEvent('cache_cleared', { cacheName: cacheName || 'all' });
      return true;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to clear cache:', error);
      return false;
    }
  }

  public async getCacheSize(): Promise<{ usage: number; quota: number }> {
    if (!('storage' in navigator) || !('estimate' in navigator.storage)) {
      return { usage: 0, quota: 0 };
    }
    try {
      const { usage, quota } = await navigator.storage.estimate();
      return {
        usage: usage || 0,
        quota: quota || 0,
      };
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to get cache size:', error);
      return { usage: 0, quota: 0 };
    }
  }

  public async prefetchAndCache(urls: string[], cacheName = 'prefetch'): Promise<boolean> {
    if (!('caches' in window)) {
      return false;
    }
    try {
      const cache = await caches.open(cacheName);
      await Promise.all(urls.map(url => cache.add(url)));
      return true;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to prefetch and cache URLs:', error);
      return false;
    }
  }

  public async setCachingStrategy(strategy: CachingStrategy): Promise<void> {
    // Remove any existing strategy with the same name
    this.cachingStrategies = this.cachingStrategies.filter(s => s.name !== strategy.name);
    // Add new strategy
    this.cachingStrategies.push(strategy);

    // Inform service worker about the new strategy
    if (navigator.serviceWorker && navigator.serviceWorker.controller) {
      await this.sendMessageToSW({
        type: 'UPDATE_CACHING_STRATEGIES',
        strategies: this.cachingStrategies,
      });
    }
  }

  // Analytics and telemetry
  public trackEvent(event: string, data: Record<string, any> = {}): void {
    const analyticsEvent = {
      event,
      data: {
        ...data,
        url: window.location.href,
        timestamp: new Date().toISOString(),
      },
      timestamp: new Date().toISOString(),
    };

    this.analyticsQueue.push(analyticsEvent);
    this.saveAnalyticsToStorage();

    if (navigator.onLine) {
      this.syncAnalytics();
    }
  }

  private saveAnalyticsToStorage(): void {
    try {
      localStorage.setItem('pwa_analytics_queue', JSON.stringify(this.analyticsQueue));
    } catch (error) {
      // eslint-disable-next-line no-console
      console.warn('Failed to save analytics to storage:', error);
    }
  }
  private loadAnalyticsFromStorage(): void {
    try {
      const storedAnalytics = localStorage.getItem('pwa_analytics_queue');
      if (storedAnalytics) {
        this.analyticsQueue = JSON.parse(storedAnalytics);
      }
    } catch (error) {
      // eslint-disable-next-line no-console
      console.warn('Failed to load analytics from storage:', error);
    }
  }
  private async syncAnalytics(): Promise<void> {
    if (this.analyticsQueue.length === 0 || !navigator.onLine) {
      return;
    }
    try {
      const eventsToSync = [...this.analyticsQueue];
      const response = await fetch('/api/analytics/pwa', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ events: eventsToSync }),
      });
      if (response.ok) {
        // Remove synced events from queue
        this.analyticsQueue = this.analyticsQueue.filter(event => !eventsToSync.includes(event));
        this.saveAnalyticsToStorage();
      }
    } catch (error) {
      // eslint-disable-next-line no-console
      console.warn('Failed to sync analytics:', error);
    }
  }

  // Offline data synchronization
  public async syncOfflineData(): Promise<boolean> {
    if (!navigator.onLine || !navigator.serviceWorker) {
      return false;
    }
    try {
      // Run background sync for offline data
      await this.registerBackgroundSync('offline-sync');
      // Directly request service worker to process any pending items
      await this.sendMessageToSW({ type: 'SYNC_OFFLINE_DATA' });
      return true;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to sync offline data:', error);
      return false;
    }
  }
  public async addToOfflineQueue(
    request: { url: string; method: string; body?: any; headers?: Record<string, string> },
    options: { priority?: string; tag?: string } = {}
  ): Promise<boolean> {
    try {
      await this.sendMessageToSW({
        type: 'ADD_TO_OFFLINE_QUEUE',
        request,
        options,
      });
      return true;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to add to offline queue:', error);
      return false;
    }
  }
  public async getOfflineQueueStatus(): Promise<{
    pending: number;
    completed: number;
    failed: number;
  }> {
    try {
      const status = await this.sendMessageToSW({ type: 'GET_OFFLINE_QUEUE_STATUS' });
      return status;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to get offline queue status:', error);
      return { pending: 0, completed: 0, failed: 0 };
    }
  }

  // Security and privacy
  public async checkPermissions(): Promise<Record<string, PermissionState>> {
    const permissions: Record<string, PermissionState> = {};
    // Check Notification permission
    if ('Notification' in window) {
      permissions.notifications = Notification.permission as PermissionState;
    }
    // Use Permissions API for other permissions
    if ('permissions' in navigator) {
      const permissionsToCheck = ['geolocation', 'camera', 'microphone'];
      await Promise.all(
        permissionsToCheck.map(async name => {
          try {
            const status = await (navigator as any).permissions.query({ name });
            permissions[name] = status.state;
          } catch (error) {
            // eslint-disable-next-line no-console
            console.warn(`Permission ${name} check failed:`, error);
          }
        })
      );
    }
    return permissions;
  }

  public async requestPersistentStorage(): Promise<boolean> {
    if (!('storage' in navigator) || !('persist' in navigator.storage)) {
      return false;
    }
    try {
      return await navigator.storage.persist();
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to request persistent storage:', error);
      return false;
    }
  }
  public async isStoragePersisted(): Promise<boolean> {
    if (!('storage' in navigator) || !('persisted' in navigator.storage)) {
      return false;
    }
    try {
      return await navigator.storage.persisted();
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to check if storage is persisted:', error);
      return false;
    }
  }

  // Helper methods
  private dispatchEvent(name: string, detail: any = {}): void {
    window.dispatchEvent(new CustomEvent(`pwa:${name}`, detail));
  }

  // Utility methods
  public static isServiceWorkerSupported(): boolean {
    return 'serviceWorker' in navigator;
  }

  public static isPushNotificationsSupported(): boolean {
    return 'serviceWorker' in navigator && 'PushManager' in window;
  }

  public static isBackgroundSyncSupported(): boolean {
    return 'serviceWorker' in navigator && 'SyncManager' in window;
  }

  public static isPeriodicSyncSupported(): boolean {
    return 'serviceWorker' in navigator && 'PeriodicSyncManager' in window;
  }

  public static isWebShareSupported(): boolean {
    return 'share' in navigator;
  }

  public static isStandalone(): boolean {
    return (
      window.matchMedia('(display-mode: standalone)').matches ||
      window.matchMedia('(display-mode: fullscreen)').matches ||
      (navigator as any).standalone === true
    );
  }

  public static async shareContent(data: {
    title?: string;
    text?: string;
    url?: string;
    files?: File[];
  }): Promise<boolean> {
    if (!('share' in navigator)) {
      return false;
    }
    try {
      await navigator.share(data as any);
      return true;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to share content:', error);
      return false;
    }
  }
}

export default PWAService;
