import mongoose from 'mongoose';
import { StorageUsage } from '@/models/StorageUsage';
import { Subscription } from '@/models/Subscription';
import { Notification } from '@/models/Notification';
import { connectDB } from '@/Utility/db';
import { SubscriptionService, StorageTier } from './Subscription.service';

// Quota alert types
export interface QuotaAlert {
  id: string;
  organizationId: string;
  type: 'warning' | 'critical' | 'over_quota' | 'grace_period';
  threshold: number;
  currentUsage: number;
  message: string;
  sentAt: Date;
  acknowledged: boolean;
}

// Storage cleanup recommendation
export interface CleanupRecommendation {
  type: 'duplicate' | 'old_files' | 'large_files' | 'unused_files';
  files: {
    fileId: string;
    fileName: string;
    fileSize: number;
    lastAccessed: Date;
    resourceType: string;
    potentialSavings: number;
  }[];
  totalPotentialSavings: number;
  priority: 'low' | 'medium' | 'high';
  description: string;
}

// Usage analytics interface
export interface UsageAnalytics {
  organizationId: string;
  period: 'day' | 'week' | 'month' | 'year';
  startDate: Date;
  endDate: Date;
  totalUsage: number;
  averageDailyUsage: number;
  growthRate: number;
  topFileTypes: { type: string; size: number; count: number }[];
  topUsers: { userId: string; userName: string; usage: number }[];
  usageByResource: { resourceType: string; usage: number }[];
  predictions: {
    nextMonth: number;
    nextQuarter: number;
    nextYear: number;
  };
}

// Quota status interface
export interface QuotaStatus {
  organizationId: string;
  currentUsage: number;
  totalQuota: number;
  usagePercentage: number;
  status: 'healthy' | 'warning' | 'critical' | 'over_quota' | 'grace_period';
  gracePeriodEnd: Date | null;
  daysInGracePeriod: number | null;
  recommendedAction: string;
  nextBillingDate: Date | null;
  estimatedOverageCost: number;
}

export class StorageQuotaManager {
  private static instance: StorageQuotaManager;
  private alertThresholds = {
    warning: 75, // 75% usage
    critical: 90, // 90% usage
    overQuota: 100, // 100% usage
  };

  private gracePeriodDays = 7; // Default grace period
  private overageRatePerGB = 50; // ₹50 per GB overage

  private constructor() {}

  public static getInstance(): StorageQuotaManager {
    if (!StorageQuotaManager.instance) {
      StorageQuotaManager.instance = new StorageQuotaManager();
    }
    return StorageQuotaManager.instance;
  }

  // Get current quota status for an organization
  public async getQuotaStatus(organizationId: string): Promise<QuotaStatus> {
    await connectDB();

    const [subscription, usage] = await Promise.all([
      Subscription.findOne({ organizationId }),
      this.getCurrentUsage(organizationId),
    ]);

    if (!subscription) {
      throw new Error('Subscription not found');
    }

    const totalQuota = subscription.storageLimit + subscription.totalStoragePurchased;
    const usagePercentage = (usage / totalQuota) * 100;

    let status: QuotaStatus['status'] = 'healthy';
    let gracePeriodEnd: Date | null = null;
    let daysInGracePeriod: number | null = null;
    let recommendedAction = 'Continue using normally';
    let estimatedOverageCost = 0;

    if (usagePercentage >= this.alertThresholds.overQuota) {
      status = 'over_quota';
      gracePeriodEnd = new Date(Date.now() + this.gracePeriodDays * 24 * 60 * 60 * 1000);
      daysInGracePeriod = this.gracePeriodDays;
      recommendedAction = 'Upgrade storage plan or clean up files immediately';

      const overageBytes = usage - totalQuota;
      estimatedOverageCost = Math.ceil(overageBytes / (1024 * 1024 * 1024)) * this.overageRatePerGB;
    } else if (usagePercentage >= this.alertThresholds.critical) {
      status = 'critical';
      recommendedAction = 'Consider upgrading storage plan or cleaning up files';
    } else if (usagePercentage >= this.alertThresholds.warning) {
      status = 'warning';
      recommendedAction = 'Monitor usage and consider storage optimization';
    }

    return {
      organizationId,
      currentUsage: usage,
      totalQuota,
      usagePercentage,
      status,
      gracePeriodEnd,
      daysInGracePeriod,
      recommendedAction,
      nextBillingDate: subscription.nextPaymentDate || null,
      estimatedOverageCost,
    };
  }

  // Monitor and enforce quota limits
  public async enforceQuotaLimits(
    organizationId: string
  ): Promise<{ allowed: boolean; reason?: string }> {
    const quotaStatus = await this.getQuotaStatus(organizationId);

    if (quotaStatus.status === 'over_quota') {
      // Check if grace period has expired
      if (quotaStatus.gracePeriodEnd && new Date() > quotaStatus.gracePeriodEnd) {
        return {
          allowed: false,
          reason: 'Storage quota exceeded and grace period expired. Please upgrade your plan.',
        };
      } else {
        // Still in grace period
        return {
          allowed: true,
          reason: `Grace period active until ${quotaStatus.gracePeriodEnd?.toLocaleDateString()}`,
        };
      }
    }

    return { allowed: true };
  }

  // Check if a file upload is allowed
  public async canUploadFile(
    organizationId: string,
    fileSize: number
  ): Promise<{ allowed: boolean; reason?: string }> {
    const quotaStatus = await this.getQuotaStatus(organizationId);
    const newUsage = quotaStatus.currentUsage + fileSize;
    const newPercentage = (newUsage / quotaStatus.totalQuota) * 100;

    if (newPercentage > this.alertThresholds.overQuota) {
      const enforcement = await this.enforceQuotaLimits(organizationId);
      if (!enforcement.allowed) {
        return enforcement;
      }
    }

    return { allowed: true };
  }

  // Get current storage usage for an organization
  private async getCurrentUsage(organizationId: string): Promise<number> {
    const result = await StorageUsage.aggregate([
      {
        $match: {
          organizationId: new mongoose.Types.ObjectId(organizationId),
          isDeleted: false,
        },
      },
      {
        $group: {
          _id: null,
          totalSize: { $sum: '$fileSize' },
        },
      },
    ]);

    return result[0]?.totalSize || 0;
  }

  // Send quota alerts and notifications
  public async sendQuotaAlerts(organizationId: string): Promise<void> {
    const quotaStatus = await this.getQuotaStatus(organizationId);

    let shouldAlert = false;
    let alertType: QuotaAlert['type'] = 'warning';
    let message = '';

    switch (quotaStatus.status) {
      case 'warning':
        shouldAlert = true;
        alertType = 'warning';
        message = `Storage usage is at ${quotaStatus.usagePercentage.toFixed(1)}%. Consider cleaning up files or upgrading your plan.`;
        break;
      case 'critical':
        shouldAlert = true;
        alertType = 'critical';
        message = `Storage usage is at ${quotaStatus.usagePercentage.toFixed(1)}%. Immediate action required to avoid service disruption.`;
        break;
      case 'over_quota':
        shouldAlert = true;
        alertType = 'over_quota';
        message = `Storage quota exceeded! Grace period ends on ${quotaStatus.gracePeriodEnd?.toLocaleDateString()}. Upgrade or clean up files immediately.`;
        break;
      case 'grace_period':
        shouldAlert = true;
        alertType = 'grace_period';
        message = `Grace period active. ${quotaStatus.daysInGracePeriod} days remaining to resolve storage overage.`;
        break;
    }

    if (shouldAlert) {
      await this.createNotification(organizationId, alertType, message);
    }
  }

  // Create notification for quota alerts
  private async createNotification(
    organizationId: string,
    type: QuotaAlert['type'],
    message: string
  ): Promise<void> {
    const notification = new Notification({
      organizationId,
      type: 'storage_quota',
      title: 'Storage Quota Alert',
      message,
      priority: type === 'over_quota' || type === 'critical' ? 'high' : 'medium',
      metadata: {
        alertType: type,
        timestamp: new Date(),
      },
    });

    await notification.save();
  }

  // Get storage cleanup recommendations
  public async getCleanupRecommendations(organizationId: string): Promise<CleanupRecommendation[]> {
    await connectDB();

    const recommendations: CleanupRecommendation[] = [];

    // Find duplicate files
    const duplicates = await this.findDuplicateFiles(organizationId);
    if (duplicates.length > 0) {
      recommendations.push({
        type: 'duplicate',
        files: duplicates,
        totalPotentialSavings: duplicates.reduce((sum, file) => sum + file.potentialSavings, 0),
        priority: 'high',
        description: 'Remove duplicate files to free up storage space',
      });
    }

    // Find old files (older than 6 months and not accessed recently)
    const oldFiles = await this.findOldFiles(organizationId);
    if (oldFiles.length > 0) {
      recommendations.push({
        type: 'old_files',
        files: oldFiles,
        totalPotentialSavings: oldFiles.reduce((sum, file) => sum + file.fileSize, 0),
        priority: 'medium',
        description: "Archive or delete old files that haven't been accessed recently",
      });
    }

    // Find large files (>10MB)
    const largeFiles = await this.findLargeFiles(organizationId);
    if (largeFiles.length > 0) {
      recommendations.push({
        type: 'large_files',
        files: largeFiles,
        totalPotentialSavings: largeFiles.reduce((sum, file) => sum + file.fileSize, 0),
        priority: 'low',
        description: 'Review large files and consider compression or alternative storage',
      });
    }

    return recommendations;
  }

  // Find duplicate files
  private async findDuplicateFiles(
    organizationId: string
  ): Promise<CleanupRecommendation['files']> {
    const duplicates = await StorageUsage.aggregate([
      {
        $match: {
          organizationId: new mongoose.Types.ObjectId(organizationId),
          isDeleted: false,
        },
      },
      {
        $group: {
          _id: { fileName: '$fileName', fileSize: '$fileSize' },
          files: { $push: '$$ROOT' },
          count: { $sum: 1 },
        },
      },
      {
        $match: { count: { $gt: 1 } },
      },
    ]);

    const result: CleanupRecommendation['files'] = [];

    for (const group of duplicates) {
      // Keep the first file, mark others as potential for deletion
      for (let i = 1; i < group.files.length; i++) {
        const file = group.files[i];
        result.push({
          fileId: file.fileId,
          fileName: file.fileName,
          fileSize: file.fileSize,
          lastAccessed: file.updatedAt,
          resourceType: file.resourceType,
          potentialSavings: file.fileSize,
        });
      }
    }

    return result;
  }

  // Find old files
  private async findOldFiles(organizationId: string): Promise<CleanupRecommendation['files']> {
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

    const oldFiles = await StorageUsage.find({
      organizationId: new mongoose.Types.ObjectId(organizationId),
      isDeleted: false,
      updatedAt: { $lt: sixMonthsAgo },
    }).limit(50);

    return oldFiles.map(file => ({
      fileId: file.fileId,
      fileName: file.fileName,
      fileSize: file.fileSize,
      lastAccessed: file.updatedAt,
      resourceType: file.resourceType,
      potentialSavings: file.fileSize,
    }));
  }

  // Find large files
  private async findLargeFiles(organizationId: string): Promise<CleanupRecommendation['files']> {
    const tenMB = 10 * 1024 * 1024; // 10MB in bytes

    const largeFiles = await StorageUsage.find({
      organizationId: new mongoose.Types.ObjectId(organizationId),
      isDeleted: false,
      fileSize: { $gt: tenMB },
    }).limit(20);

    return largeFiles.map(file => ({
      fileId: file.fileId,
      fileName: file.fileName,
      fileSize: file.fileSize,
      lastAccessed: file.updatedAt,
      resourceType: file.resourceType,
      potentialSavings: 0, // No direct savings, just optimization
    }));
  }

  // Get usage analytics
  public async getUsageAnalytics(
    organizationId: string,
    period: 'day' | 'week' | 'month' | 'year' = 'month'
  ): Promise<UsageAnalytics> {
    await connectDB();

    const now = new Date();
    let startDate: Date;
    const endDate = now;

    switch (period) {
      case 'day':
        startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        break;
      case 'week':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'month':
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        break;
      case 'year':
        startDate = new Date(now.getFullYear(), 0, 1);
        break;
    }

    const [totalUsage, fileTypeBreakdown, userBreakdown, resourceBreakdown] = await Promise.all([
      this.getCurrentUsage(organizationId),
      this.getFileTypeBreakdown(organizationId, startDate, endDate),
      this.getUserBreakdown(organizationId, startDate, endDate),
      this.getResourceBreakdown(organizationId, startDate, endDate),
    ]);

    const daysDiff = Math.max(
      1,
      Math.ceil((endDate.getTime() - startDate.getTime()) / (24 * 60 * 60 * 1000))
    );
    const averageDailyUsage = totalUsage / daysDiff;

    // Calculate growth rate (simplified)
    const growthRate = await this.calculateGrowthRate(organizationId, period);

    // Generate predictions
    const predictions = {
      nextMonth: totalUsage + averageDailyUsage * 30 * (1 + growthRate),
      nextQuarter: totalUsage + averageDailyUsage * 90 * (1 + growthRate),
      nextYear: totalUsage + averageDailyUsage * 365 * (1 + growthRate),
    };

    return {
      organizationId,
      period,
      startDate,
      endDate,
      totalUsage,
      averageDailyUsage,
      growthRate,
      topFileTypes: fileTypeBreakdown,
      topUsers: userBreakdown,
      usageByResource: resourceBreakdown,
      predictions,
    };
  }

  // Get file type breakdown
  private async getFileTypeBreakdown(
    organizationId: string,
    startDate: Date,
    endDate: Date
  ): Promise<{ type: string; size: number; count: number }[]> {
    const result = await StorageUsage.aggregate([
      {
        $match: {
          organizationId: new mongoose.Types.ObjectId(organizationId),
          isDeleted: false,
          createdAt: { $gte: startDate, $lte: endDate },
        },
      },
      {
        $group: {
          _id: '$fileType',
          size: { $sum: '$fileSize' },
          count: { $sum: 1 },
        },
      },
      {
        $sort: { size: -1 },
      },
      {
        $limit: 10,
      },
    ]);

    return result.map(item => ({
      type: item._id,
      size: item.size,
      count: item.count,
    }));
  }

  // Get user breakdown
  private async getUserBreakdown(
    organizationId: string,
    startDate: Date,
    endDate: Date
  ): Promise<{ userId: string; userName: string; usage: number }[]> {
    const result = await StorageUsage.aggregate([
      {
        $match: {
          organizationId: new mongoose.Types.ObjectId(organizationId),
          isDeleted: false,
          createdAt: { $gte: startDate, $lte: endDate },
        },
      },
      {
        $group: {
          _id: '$uploadedBy',
          usage: { $sum: '$fileSize' },
        },
      },
      {
        $lookup: {
          from: 'users',
          localField: '_id',
          foreignField: '_id',
          as: 'user',
        },
      },
      {
        $unwind: '$user',
      },
      {
        $sort: { usage: -1 },
      },
      {
        $limit: 10,
      },
    ]);

    return result.map(item => ({
      userId: item._id.toString(),
      userName: item.user.name || 'Unknown User',
      usage: item.usage,
    }));
  }

  // Get resource breakdown
  private async getResourceBreakdown(
    organizationId: string,
    startDate: Date,
    endDate: Date
  ): Promise<{ resourceType: string; usage: number }[]> {
    const result = await StorageUsage.aggregate([
      {
        $match: {
          organizationId: new mongoose.Types.ObjectId(organizationId),
          isDeleted: false,
          createdAt: { $gte: startDate, $lte: endDate },
        },
      },
      {
        $group: {
          _id: '$resourceType',
          usage: { $sum: '$fileSize' },
        },
      },
      {
        $sort: { usage: -1 },
      },
    ]);

    return result.map(item => ({
      resourceType: item._id,
      usage: item.usage,
    }));
  }

  // Calculate growth rate
  private async calculateGrowthRate(
    organizationId: string,
    period: 'day' | 'week' | 'month' | 'year'
  ): Promise<number> {
    const now = new Date();
    let previousPeriodStart: Date;
    let previousPeriodEnd: Date;

    switch (period) {
      case 'day':
        previousPeriodStart = new Date(now.getTime() - 48 * 60 * 60 * 1000);
        previousPeriodEnd = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        break;
      case 'week':
        previousPeriodStart = new Date(now.getTime() - 14 * 24 * 60 * 60 * 1000);
        previousPeriodEnd = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'month':
        previousPeriodStart = new Date(now.getFullYear(), now.getMonth() - 1, 1);
        previousPeriodEnd = new Date(now.getFullYear(), now.getMonth(), 0);
        break;
      case 'year':
        previousPeriodStart = new Date(now.getFullYear() - 1, 0, 1);
        previousPeriodEnd = new Date(now.getFullYear() - 1, 11, 31);
        break;
    }

    const [currentUsage, previousUsage] = await Promise.all([
      this.getCurrentUsage(organizationId),
      this.getUsageForPeriod(organizationId, previousPeriodStart, previousPeriodEnd),
    ]);

    if (previousUsage === 0) return 0;
    return (currentUsage - previousUsage) / previousUsage;
  }

  // Get usage for a specific period
  private async getUsageForPeriod(
    organizationId: string,
    startDate: Date,
    endDate: Date
  ): Promise<number> {
    const result = await StorageUsage.aggregate([
      {
        $match: {
          organizationId: new mongoose.Types.ObjectId(organizationId),
          isDeleted: false,
          createdAt: { $gte: startDate, $lte: endDate },
        },
      },
      {
        $group: {
          _id: null,
          totalSize: { $sum: '$fileSize' },
        },
      },
    ]);

    return result[0]?.totalSize || 0;
  }

  // Calculate tiered storage pricing
  public calculateTieredPricing(usageBytes: number): {
    tier: StorageTier;
    monthlyCost: number;
    yearlyCost: number;
  } {
    const recommendedTier = SubscriptionService.calculateRecommendedTier(usageBytes);

    return {
      tier: recommendedTier,
      monthlyCost: recommendedTier.priceMonthly,
      yearlyCost: recommendedTier.priceYearly,
    };
  }

  // Perform automatic cleanup of expired files
  public async performAutomaticCleanup(
    organizationId: string,
    dryRun: boolean = true
  ): Promise<{
    filesMarkedForDeletion: number;
    spaceSaved: number;
    errors: string[];
  }> {
    await connectDB();

    const errors: string[] = [];
    let filesMarkedForDeletion = 0;
    let spaceSaved = 0;

    try {
      // Find files that are candidates for cleanup
      const oneYearAgo = new Date();
      oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);

      const candidateFiles = await StorageUsage.find({
        organizationId: new mongoose.Types.ObjectId(organizationId),
        isDeleted: false,
        updatedAt: { $lt: oneYearAgo },
      });

      for (const file of candidateFiles) {
        try {
          if (!dryRun) {
            // Mark file as deleted
            file.isDeleted = true;
            file.deletedAt = new Date();
            await file.save();
          }

          filesMarkedForDeletion++;
          spaceSaved += file.fileSize;
        } catch (error) {
          errors.push(`Failed to process file ${file.fileName}: ${error}`);
        }
      }
    } catch (error) {
      errors.push(`Cleanup process failed: ${error}`);
    }

    return {
      filesMarkedForDeletion,
      spaceSaved,
      errors,
    };
  }

  // Get storage optimization recommendations
  public async getOptimizationRecommendations(organizationId: string): Promise<{
    recommendations: string[];
    potentialSavings: number;
    currentTier: StorageTier;
    recommendedTier: StorageTier;
  }> {
    const quotaStatus = await this.getQuotaStatus(organizationId);
    const analytics = await this.getUsageAnalytics(organizationId);
    const cleanupRecommendations = await this.getCleanupRecommendations(organizationId);

    const recommendations: string[] = [];
    let potentialSavings = 0;

    // Add cleanup recommendations
    for (const cleanup of cleanupRecommendations) {
      recommendations.push(cleanup.description);
      potentialSavings += cleanup.totalPotentialSavings;
    }

    // Add usage-based recommendations
    if (analytics.topFileTypes[0]?.type.includes('image')) {
      recommendations.push('Consider compressing images to reduce storage usage');
    }

    if (analytics.growthRate > 0.5) {
      recommendations.push('High growth rate detected - consider upgrading storage plan');
    }

    const currentTier = SubscriptionService.calculateRecommendedTier(quotaStatus.currentUsage);
    const recommendedTier = SubscriptionService.calculateRecommendedTier(
      analytics.predictions.nextQuarter
    );

    return {
      recommendations,
      potentialSavings,
      currentTier,
      recommendedTier,
    };
  }
}

// Export singleton instance
export const storageQuotaManager = StorageQuotaManager.getInstance();
