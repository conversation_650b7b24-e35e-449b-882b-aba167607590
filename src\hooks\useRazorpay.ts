import { useState, useCallback } from 'react';
import { toast } from 'sonner';

declare global {
  interface Window {
    Razorpay: any;
  }
}

export interface RazorpayOptions {
  key: string;
  amount: number;
  currency: string;
  name: string;
  description: string;
  order_id: string;
  handler: (response: RazorpayResponse) => void;
  prefill?: {
    name?: string;
    email?: string;
    contact?: string;
  };
  theme?: {
    color?: string;
  };
  modal?: {
    ondismiss?: () => void;
    confirm_close?: boolean;
    animation?: boolean;
  };
  retry?: {
    enabled?: boolean;
    max_count?: number;
  };
}

export interface RazorpayResponse {
  razorpay_payment_id: string;
  razorpay_order_id: string;
  razorpay_signature: string;
}

export interface CreateOrderData {
  storageMB: number;
}

export interface OrderResponse {
  success: boolean;
  order: {
    id: string;
    amount: number;
    currency: string;
    receipt: string;
    status: string;
    created_at: number;
    notes: Record<string, string>;
  };
  priceInfo: {
    storageMB: number;
    pricePerMB: number;
    totalPrice: number;
    storageBytes: number;
  };
  user: {
    name: string;
    email: string;
  };
}

export const useRazorpay = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [isScriptLoaded, setIsScriptLoaded] = useState(false);

  // Load Razorpay script
  const loadRazorpayScript = useCallback((): Promise<boolean> => {
    return new Promise(resolve => {
      if (window.Razorpay) {
        setIsScriptLoaded(true);
        resolve(true);
        return;
      }

      const script = document.createElement('script');
      script.src = 'https://checkout.razorpay.com/v1/checkout.js';
      script.onload = () => {
        setIsScriptLoaded(true);
        resolve(true);
      };
      script.onerror = () => {
        toast.error('Failed to load payment gateway');
        resolve(false);
      };
      document.body.appendChild(script);
    });
  }, []);

  // Create Razorpay order
  const createOrder = useCallback(async (orderData: CreateOrderData): Promise<OrderResponse> => {
    const response = await fetch('/api/payment/create-order', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(orderData),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to create order');
    }

    return response.json();
  }, []);

  // Verify payment
  const verifyPayment = useCallback(async (paymentData: RazorpayResponse): Promise<boolean> => {
    try {
      const response = await fetch('/api/payment/verify', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(paymentData),
      });

      if (!response.ok) {
        return false;
      }

      const result = await response.json();
      return result.verified === true;
    } catch (error) {
      return false;
    }
  }, []);

  // Open Razorpay checkout popup
  const openCheckout = useCallback(
    async (options: RazorpayOptions): Promise<void> => {
      const isLoaded = await loadRazorpayScript();

      if (!isLoaded) {
        throw new Error('Failed to load payment gateway');
      }

      const rzp = new window.Razorpay(options);
      rzp.open();
    },
    [loadRazorpayScript]
  );

  // Complete payment flow for storage purchase
  const initiateStoragePayment = useCallback(
    async ({
      storageMB,
      onSuccess,
      onFailure,
    }: {
      storageMB: number;
      onSuccess: (response: RazorpayResponse, orderData: OrderResponse) => void;
      onFailure: (error: Error) => void;
    }) => {
      setIsLoading(true);

      try {
        // Step 1: Create order
        const orderData = await createOrder({ storageMB });

        // Step 2: Get Razorpay key
        const razorpayKey = process.env.NEXT_PUBLIC_RAZORPAY_KEY_ID;
        if (!razorpayKey) {
          throw new Error('Payment gateway not configured');
        }

        // Step 3: Prepare Razorpay options
        const options: RazorpayOptions = {
          key: razorpayKey,
          amount: orderData.order.amount,
          currency: orderData.order.currency,
          name: 'TaskMantra',
          description: `${storageMB}MB Storage Purchase`,
          order_id: orderData.order.id,
          handler: async (response: RazorpayResponse) => {
            try {
              // Verify payment
              const isVerified = await verifyPayment(response);
              if (!isVerified) {
                throw new Error('Payment verification failed');
              }

              onSuccess(response, orderData);
            } catch (error) {
              onFailure(error as Error);
            }
          },
          prefill: {
            name: orderData.user.name,
            email: orderData.user.email,
          },
          theme: {
            color: '#3399cc',
          },
          modal: {
            ondismiss: () => {
              onFailure(new Error('Payment cancelled by user'));
            },
            confirm_close: true,
            animation: true,
          },
          retry: {
            enabled: true,
            max_count: 3,
          },
        };

        // Step 4: Open checkout popup
        await openCheckout(options);
      } catch (error) {
        onFailure(error as Error);
      } finally {
        setIsLoading(false);
      }
    },
    [createOrder, verifyPayment, openCheckout]
  );

  // Test payment (for development)
  const initiateTestPayment = useCallback(
    async ({
      amount,
      description,
      onSuccess,
      onFailure,
    }: {
      amount: number;
      description: string;
      onSuccess: (response: RazorpayResponse) => void;
      onFailure: (error: Error) => void;
    }) => {
      setIsLoading(true);

      try {
        const razorpayKey = process.env.NEXT_PUBLIC_RAZORPAY_KEY_ID;
        if (!razorpayKey) {
          throw new Error('Payment gateway not configured');
        }

        const options: RazorpayOptions = {
          key: razorpayKey,
          amount: amount * 100, // Convert to paise
          currency: 'INR',
          name: 'TaskMantra',
          description,
          order_id: `test_${Date.now()}`,
          handler: onSuccess,
          prefill: {
            name: 'Test User',
            email: '<EMAIL>',
          },
          theme: {
            color: '#3399cc',
          },
          modal: {
            ondismiss: () => {
              onFailure(new Error('Payment cancelled by user'));
            },
          },
        };

        await openCheckout(options);
      } catch (error) {
        onFailure(error as Error);
      } finally {
        setIsLoading(false);
      }
    },
    [openCheckout]
  );

  return {
    isLoading,
    isScriptLoaded,
    initiateStoragePayment,
    initiateTestPayment,
    createOrder,
    verifyPayment,
    loadRazorpayScript,
  };
};
