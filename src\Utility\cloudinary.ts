import { StorageService } from '@/app/api/storage/[[...route]]/route';
import { v2 as cloudinary } from 'cloudinary';
// Configure Cloudinary
cloudinary.config({
  cloud_name: process.env.CLOUDINARY_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
});

export interface UploadResult {
  secure_url: string;
  public_id: string;
  bytes?: number;
  error?: string;
}

export interface UploadOptions {
  organizationId?: string;
  fileInfo;
  checkStorageLimit?: boolean;
}

/**
 * Uploads a file to Cloudinary with storage tracking
 * @param file - The file to upload (base64 data)
 * @param folder - Optional folder name in Cloudinary
 * @param options - Upload options including storage tracking
 * @returns Promise<UploadResult>
 */
export const uploadToCloudinary = async (
  file: string, // file should contain proper base64 data
  folder: string = 'taskmantra',
  options?: UploadOptions
): Promise<UploadResult> => {
  try {
    // Check storage limit if required
    if (options?.checkStorageLimit && options?.organizationId) {
      // Estimate file size from base64 (rough calculation)
      const estimatedSize = Math.floor((file.length * 3) / 4);

      const storageCheck = await StorageService.canUploadFile(
        options.organizationId,
        estimatedSize
      );

      if (!storageCheck.canUpload) {
        return {
          secure_url: '',
          public_id: '',
          error: storageCheck.reason || 'Storage limit exceeded',
        };
      }
    }

    // Upload to Cloudinary with timeout
    const result = await Promise.race([
      new Promise<any>((resolve, reject) => {
        cloudinary.uploader.upload(
          file,
          {
            folder,
            resource_type: 'auto',
            timeout: 30000, // 30 second timeout
          },
          (error, result) => {
            if (error) reject(error);
            resolve(result);
          }
        );
      }),
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Upload timeout after 30 seconds')), 30000)
      ),
    ]);

    // Record file upload in storage tracking
    if (options?.organizationId && options?.fileInfo) {
      await StorageService.recordFileUpload(options.organizationId, result, options.fileInfo);
    }

    return {
      secure_url: result.secure_url,
      public_id: result.public_id,
      bytes: result.bytes,
    };
  } catch (error) {
    return {
      secure_url: '',
      public_id: '',
      error: 'Failed to upload file',
    };
  }
};

/**
 * Deletes a file from Cloudinary and updates storage tracking
 * @param publicId - The public_id of the file to delete
 * @param organizationId - Optional organization ID for storage tracking
 * @returns Promise<boolean>
 */
export const deleteFromCloudinary = async (
  publicId: string,
  organizationId?: string
): Promise<boolean> => {
  try {
    const result = await cloudinary.uploader.destroy(publicId);
    const success = result.result === 'ok';

    // Update storage tracking if organization ID is provided
    if (success && organizationId) {
      await StorageService.recordFileDeletion(organizationId, publicId);
    }

    return success;
  } catch (error) {
    return false;
  }
};
