'use client';

import { memo, useState } from 'react';
import { <PERSON>le, Position, type NodeProps } from 'reactflow';
import { Card } from '@/components/ui/card';
import { MessageSquare, Edit3 } from 'lucide-react';
import { useMindMapStore } from '@/stores/mindMapStore';
import { AnnotationNodeForm } from '../node-forms/annotation-node-form';
import ReactMarkdown from 'react-markdown';
import { useNodeEdit } from '../contexts/node-edit-context';

interface AnnotationNodeData {
  title: string;
  content: string;
}

export const AnnotationNode = memo(({ id, data, selected }: NodeProps<AnnotationNodeData>) => {
  const { isEditing, startEditing, stopEditing } = useNodeEdit();
  const { updateNode } = useMindMapStore();
  const [isHovered, setIsHovered] = useState(false);

  const handleDoubleClick = () => {
    startEditing(id, data);
  };

  const handleSave = (updatedData: Partial<AnnotationNodeData>) => {
    updateNode(id, {
      content: {
        text: updatedData.title || 'New Annotation',
        ...updatedData,
      },
    });
    stopEditing();
  };

  if (isEditing(id)) {
    return <AnnotationNodeForm data={data} onSave={handleSave} onCancel={stopEditing} />;
  }

  return (
    <Card
      className={`min-w-[250px] max-w-[350px] bg-gradient-to-br from-teal-50 to-emerald-50 border-teal-200 shadow-lg transition-all duration-200 ${
        selected ? 'ring-2 ring-teal-500 shadow-xl scale-105' : 'hover:shadow-xl hover:scale-102'
      }`}
      onDoubleClick={handleDoubleClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <Handle
        type="target"
        position={Position.Top}
        className="w-3 h-3 bg-teal-500 border-2 border-white"
      />

      <div className="p-4">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-2">
            <MessageSquare className="h-5 w-5 text-teal-600" />
            <h3 className="font-semibold text-gray-900 text-sm">{data.title}</h3>
          </div>
          {isHovered && (
            <Edit3
              className="h-3 w-3 text-gray-400 cursor-pointer hover:text-gray-600"
              onClick={() => startEditing(id, data)}
            />
          )}
        </div>

        <div className="bg-white/70 rounded-md p-3 border border-teal-100 prose prose-sm max-w-none">
          <ReactMarkdown>{data.content}</ReactMarkdown>
        </div>
      </div>

      <Handle
        type="source"
        position={Position.Bottom}
        className="w-3 h-3 bg-teal-500 border-2 border-white"
      />
    </Card>
  );
});

AnnotationNode.displayName = 'AnnotationNode';
