import axios from 'axios';

export class EventService {
  static async getEvents(params?: any) {
    try {
      const response = await axios.get('/api/events', { params });
      return response.data;
    } catch (error: any) {
      if (error.response && error.response.data && error.response.data.error) {
        throw new Error(error.response.data.error);
      }
      throw new Error('Failed to fetch events');
    }
  }

  static async createEvent(eventData: any) {
    try {
      const response = await axios.post('/api/events', eventData);
      return response.data;
    } catch (error: any) {
      if (error.response && error.response.data && error.response.data.error) {
        throw new Error(error.response.data.error);
      }
      throw new Error('Failed to create event');
    }
  }

  static async updateEvent(eventId: string, updates: any) {
    try {
      const response = await axios.put(`/api/events/${eventId}`, updates);
      return response.data;
    } catch (error: any) {
      if (error.response && error.response.data && error.response.data.error) {
        throw new Error(error.response.data.error);
      }
      throw new Error('Failed to update event');
    }
  }

  static async deleteEvent(eventId: string) {
    try {
      const response = await axios.delete(`/api/events/${eventId}`);
      return response.data;
    } catch (error: any) {
      if (error.response && error.response.data && error.response.data.error) {
        throw new Error(error.response.data.error);
      }
      throw new Error('Failed to delete event');
    }
  }

  static async detectConflicts(eventData: any, excludeEventId?: string) {
    try {
      const response = await axios.post('/api/events/conflicts', {
        ...eventData,
        excludeEventId,
      });
      return response.data;
    } catch (error: any) {
      if (error.response && error.response.data && error.response.data.error) {
        throw new Error(error.response.data.error);
      }
      throw new Error('Failed to detect conflicts');
    }
  }

  static async syncGoogleCalendar() {
    try {
      const response = await axios.post('/api/events/google-sync');
      return response.data;
    } catch (error: any) {
      if (error.response && error.response.data && error.response.data.error) {
        throw new Error(error.response.data.error);
      }
      throw new Error('Failed to sync with Google Calendar');
    }
  }

  static async scheduleTaskDeadlines(projectId: string) {
    try {
      const response = await axios.post('/api/events/schedule-tasks', { projectId });
      return response.data;
    } catch (error: any) {
      if (error.response && error.response.data && error.response.data.error) {
        throw new Error(error.response.data.error);
      }
      throw new Error('Failed to schedule task deadlines');
    }
  }
}

export default EventService;
