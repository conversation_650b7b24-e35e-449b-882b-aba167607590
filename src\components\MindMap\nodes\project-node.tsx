'use client';

import { memo } from 'react';
import { <PERSON><PERSON>, Position, type NodeProps } from 'reactflow';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Folder, Edit3, ChevronDown, ChevronUp } from 'lucide-react';
import { Progress } from '@/components/ui/progress';
import { useNodeEdit } from '../contexts/node-edit-context';
import { useMindMapStore } from '@/stores/mindMapStore';
import { ProjectNodeForm } from '../node-forms/project-node-form';
import { useState } from 'react';

interface ProjectNodeData {
  title: string;
  description: string;
  tasks: Array<{ id: string; title: string; completed: boolean }>;
  progress: number;
  status: 'active' | 'completed' | 'on-hold';
}

export const ProjectNode = memo(({ id, data, selected }: NodeProps<ProjectNodeData>) => {
  const { isEditing, startEditing, stopEditing } = useNodeEdit();
  const { updateNode } = useMindMapStore();
  const [isExpanded, setIsExpanded] = useState(true);
  const [isHovered, setIsHovered] = useState(false);

  const statusColors = {
    active: 'bg-blue-100 text-blue-800',
    completed: 'bg-green-100 text-green-800',
    'on-hold': 'bg-yellow-100 text-yellow-800',
  };

  const handleDoubleClick = () => {
    startEditing(id, data);
  };

  const handleSave = (updatedData: Partial<ProjectNodeData>) => {
    updateNode(id, {
      content: {
        text: updatedData.title || 'New Project',
        ...updatedData,
      },
    });
    stopEditing();
  };

  if (isEditing(id)) {
    return <ProjectNodeForm data={data} onSave={handleSave} onCancel={stopEditing} />;
  }

  return (
    <Card
      className={`min-w-[300px] max-w-[400px] bg-gradient-to-br from-blue-50 to-cyan-50 border-blue-200 shadow-lg transition-all duration-200 ${
        selected ? 'ring-2 ring-blue-500 shadow-xl scale-105' : 'hover:shadow-xl hover:scale-102'
      }`}
      onDoubleClick={handleDoubleClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <Handle
        type="target"
        position={Position.Top}
        className="w-3 h-3 bg-blue-500 border-2 border-white"
      />

      <div className="p-4">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center space-x-2">
            <Folder className="h-5 w-5 text-blue-600" />
            <h3 className="font-semibold text-gray-900">{data.title}</h3>
          </div>
          <div className="flex items-center space-x-2">
            {isHovered && (
              <Edit3
                className="h-4 w-4 text-gray-400 cursor-pointer hover:text-gray-600"
                onClick={() => startEditing(id, data)}
              />
            )}
            <button
              onClick={e => {
                e.stopPropagation();
                setIsExpanded(!isExpanded);
              }}
              className="p-1 rounded-full hover:bg-gray-100"
            >
              {isExpanded ? (
                <ChevronUp className="h-4 w-4 text-gray-600" />
              ) : (
                <ChevronDown className="h-4 w-4 text-gray-600" />
              )}
            </button>
          </div>
        </div>

        <div className="flex items-center justify-between mb-3">
          <Badge variant="secondary" className={`text-xs ${statusColors[data.status]}`}>
            {data.status}
          </Badge>
          <span className="text-xs text-gray-500">{data.progress}% complete</span>
        </div>

        <Progress value={data.progress} className="h-2 mb-3" />

        {isExpanded && (
          <>
            <p className="text-sm text-gray-600 mb-3">{data.description}</p>

            {data.tasks.length > 0 && (
              <div className="space-y-1 mt-2">
                <h4 className="text-xs font-medium text-gray-700">Tasks</h4>
                <div className="bg-white/70 rounded-md p-2 border border-blue-100">
                  {data.tasks.map((task, index) => (
                    <div key={task.id || index} className="flex items-center py-1">
                      <div
                        className={`w-3 h-3 rounded-full mr-2 ${task.completed ? 'bg-green-500' : 'bg-gray-300'}`}
                      />
                      <span
                        className={`text-xs ${task.completed ? 'line-through text-gray-500' : 'text-gray-700'}`}
                      >
                        {task.title}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </>
        )}
      </div>

      <Handle
        type="source"
        position={Position.Bottom}
        className="w-3 h-3 bg-blue-500 border-2 border-white"
      />
    </Card>
  );
});

ProjectNode.displayName = 'ProjectNode';
