import { Hono } from 'hono';
import { handle } from 'hono/vercel';
import { logger } from 'hono/logger';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/options';
import { connectDB } from '@/Utility/db';
import { Subscription } from '@/models/Subscription';
import { StorageUsage } from '@/models/StorageUsage';
import {
  SubscriptionService,
  STORAGE_TIERS,
  StorageTier,
  ProratedBillingInfo,
} from '@/services/Subscription.service';
import { storageQuotaManager } from '@/services/StorageQuotaManager.service';
import mongoose from 'mongoose';
type Variables = {
  user?: {
    id: string;
    name?: string;
    email?: string;
    image?: string;
    organizationId?: string;
  };
};

const app = new Hono<{ Variables: Variables }>().basePath('/api/subscription/storage-tiers');

app.use('*', logger());

app.use('*', async (c, next) => {
  try {
    const session = await getServerSession(authOptions);

    if (session?.user) {
      const userData = {
        id: session.user.id || '',
        name: session.user.name || '',
        email: session.user.email || '',
        image: session.user.image || '',
        organizationId: session.user.organizationId || '',
      };
      c.set('user', userData);
    }
  } catch (error: any) {
    throw new Error(error.message);
  }
  await next();
});

// GET: Get available storage tiers and recommendations
app.get('/', async c => {
  const user = c.get('user');
  if (!user?.organizationId) {
    return c.json({ error: 'Unauthorized' }, 401);
  }
  const organizationId = user.organizationId;
  const url = new URL(c.req.url);
  const includeRecommendations = url.searchParams.get('recommendations') === 'true';
  const includeForecast = url.searchParams.get('forecast') === 'true';
  await connectDB();
  // Get current usage and subscription
  const [subscription, currentUsage] = await Promise.all([
    Subscription.findOne({ organizationId }),
    getCurrentStorageUsage(organizationId),
  ]);
  if (!subscription) {
    return c.json({ error: 'Subscription not found' }, 404);
  }
  let recommendedTier: StorageTier | null = null;
  let forecast: any = null;
  let currentTier: StorageTier | null = null;
  currentTier =
    STORAGE_TIERS.find(tier => tier.storageBytes === subscription.storageLimit) || STORAGE_TIERS[0];
  if (includeRecommendations) {
    const analytics = await storageQuotaManager.getUsageAnalytics(organizationId, 'month');
    const projectedUsage = analytics.predictions.nextQuarter;
    recommendedTier = SubscriptionService.calculateRecommendedTier(projectedUsage);
  }
  if (includeForecast) {
    forecast = await generateStorageForecast(organizationId);
  }
  return c.json({
    success: true,
    data: {
      availableTiers: STORAGE_TIERS,
      currentTier,
      recommendedTier,
      currentUsage,
      forecast,
      canUpgrade: currentTier
        ? STORAGE_TIERS.some(tier => tier.storageBytes > currentTier.storageBytes)
        : true,
      canDowngrade: currentTier
        ? STORAGE_TIERS.some(
            tier =>
              tier.storageBytes < currentTier.storageBytes && tier.storageBytes >= currentUsage
          )
        : false,
    },
  });
});

// POST: Upgrade or change storage tier
app.post('/', async c => {
  const user = c.get('user');
  if (!user?.organizationId) {
    return c.json({ error: 'Unauthorized' }, 401);
  }
  const organizationId = user.organizationId;
  const { tierId, billingCycle, paymentMethod } = await c.req.json();
  if (!tierId || !billingCycle) {
    return c.json({ error: 'Missing required fields: tierId, billingCycle' }, 400);
  }
  if (!['monthly', 'yearly'].includes(billingCycle)) {
    return c.json({ error: 'Invalid billing cycle. Must be monthly or yearly' }, 400);
  }
  const targetTier = STORAGE_TIERS.find(tier => tier.id === tierId);
  if (!targetTier) {
    return c.json({ error: 'Invalid tier ID' }, 400);
  }
  await connectDB();
  const [subscription, currentUsage] = await Promise.all([
    Subscription.findOne({ organizationId }),
    getCurrentStorageUsage(organizationId),
  ]);
  if (!subscription) {
    return c.json({ error: 'Subscription not found' }, 404);
  }
  const validationResult = await validateTierChange(subscription, targetTier, currentUsage);
  if (!validationResult.isValid) {
    return c.json({ error: validationResult.reason }, 400);
  }
  const proratedBilling = await calculateProratedBilling(subscription, targetTier, billingCycle);
  const upgradeResult = await processTierUpgrade(
    organizationId,
    subscription,
    targetTier,
    billingCycle,
    proratedBilling,
    paymentMethod
  );
  return c.json({
    success: true,
    data: {
      message: 'Storage tier updated successfully',
      newTier: targetTier,
      billingInfo: proratedBilling,
      effectiveDate: new Date(),
      transactionId: upgradeResult.transactionId,
    },
  });
});

// PUT: Update tier preferences or scheduling
app.put('/', async c => {
  const user = c.get('user');
  if (!user?.organizationId) {
    return c.json({ error: 'Unauthorized' }, 401);
  }
  const organizationId = user.organizationId;
  const { action, tierId, effectiveDate, autoUpgrade } = await c.req.json();
  await connectDB();
  const subscription = await Subscription.findOne({ organizationId });
  if (!subscription) {
    return c.json({ error: 'Subscription not found' }, 404);
  }
  switch (action) {
    case 'schedule_upgrade':
      await scheduleStorageTierUpgrade(organizationId, tierId, new Date(effectiveDate));
      break;
    case 'enable_auto_upgrade':
      await enableAutoStorageUpgrade(organizationId, autoUpgrade);
      break;
    case 'cancel_scheduled':
      await cancelScheduledTierChange(organizationId);
      break;
    default:
      return c.json({ error: 'Invalid action' }, 400);
  }
  return c.json({
    success: true,
    message: 'Storage tier preferences updated successfully',
  });
});

// --- Helper functions (same as before, adapted for this file) ---
async function getCurrentStorageUsage(organizationId: string): Promise<number> {
  const result = await StorageUsage.aggregate([
    {
      $match: {
        organizationId: new mongoose.Types.ObjectId(organizationId),
        isDeleted: false,
      },
    },
    {
      $group: {
        _id: null,
        totalSize: { $sum: '$fileSize' },
      },
    },
  ]);
  return result[0]?.totalSize || 0;
}

async function generateStorageForecast(organizationId: string) {
  const analytics = await storageQuotaManager.getUsageAnalytics(organizationId, 'month');
  const growthRate = analytics.growthRate;
  const currentUsage = analytics.totalUsage;
  const forecast: {
    date: Date;
    projectedUsage: number;
    projectedUsageFormatted: string;
    recommendedTier: StorageTier;
  }[] = [];
  const baseDate = new Date();
  for (let i = 1; i <= 12; i++) {
    const futureDate = new Date(baseDate);
    futureDate.setMonth(futureDate.getMonth() + i);
    const projectedUsage = currentUsage * Math.pow(1 + growthRate, i);
    forecast.push({
      date: futureDate,
      projectedUsage,
      projectedUsageFormatted: formatBytes(projectedUsage),
      recommendedTier: SubscriptionService.calculateRecommendedTier(projectedUsage),
    });
  }
  return {
    growthRate: growthRate * 100,
    forecastData: forecast,
    recommendations: {
      shortTerm: forecast[2],
      longTerm: forecast[11],
    },
  };
}

interface ValidationResult {
  isValid: boolean;
  reason?: string;
}

async function validateTierChange(
  subscription: any,
  targetTier: StorageTier,
  currentUsage: number
): Promise<ValidationResult> {
  console.log(`[Storage] Validating tier change for subscription:`, subscription);
  if (targetTier.storageBytes < currentUsage) {
    return {
      isValid: false,
      reason: `Cannot downgrade to ${targetTier.name} as current usage (${formatBytes(currentUsage)}) exceeds the tier limit (${targetTier.storageFormatted})`,
    };
  }
  if (subscription.storageLimit === targetTier.storageBytes) {
    return {
      isValid: false,
      reason: 'Already on the selected storage tier',
    };
  }
  const quotaStatus = await storageQuotaManager.getQuotaStatus(subscription.organizationId);
  if (quotaStatus.status === 'over_quota' && targetTier.storageBytes < currentUsage) {
    return {
      isValid: false,
      reason: 'Cannot change tier while over quota. Please clean up storage first.',
    };
  }
  return { isValid: true };
}

async function calculateProratedBilling(
  subscription: any,
  targetTier: StorageTier,
  billingCycle: 'monthly' | 'yearly'
): Promise<ProratedBillingInfo> {
  console.log(`[Storage] Calculating prorated billing for subscription:`, subscription);
  const now = new Date();
  const currentPlanEndDate =
    subscription.nextPaymentDate || new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
  const daysRemaining = Math.max(
    0,
    Math.ceil((currentPlanEndDate.getTime() - now.getTime()) / (24 * 60 * 60 * 1000))
  );
  const totalDaysInBillingCycle = billingCycle === 'yearly' ? 365 : 30;
  const currentPlanPrice =
    billingCycle === 'yearly' ? subscription.yearlyPrice || 0 : subscription.monthlyPrice || 0;
  const currentPlanRefundAmount = (currentPlanPrice * daysRemaining) / totalDaysInBillingCycle;
  const newPlanFullPrice =
    billingCycle === 'yearly' ? targetTier.priceYearly : targetTier.priceMonthly;
  const newPlanProRatedAmount = (newPlanFullPrice * daysRemaining) / totalDaysInBillingCycle;
  const totalDue = Math.max(0, newPlanProRatedAmount - currentPlanRefundAmount);
  const savingsAmount =
    billingCycle === 'yearly' ? targetTier.priceMonthly * 12 - targetTier.priceYearly : null;
  return {
    currentPlanEndDate,
    daysRemaining,
    totalDaysInBillingCycle,
    currentPlanRefundAmount,
    newPlanProRatedAmount,
    totalDue,
    savingsAmount,
    effectiveDate: now,
  };
}

async function processTierUpgrade(
  organizationId: string,
  subscription: any,
  targetTier: StorageTier,
  billingCycle: 'monthly' | 'yearly',
  proratedBilling: ProratedBillingInfo,
  paymentMethod?: string
) {
  console.log(`[Storage] Prorated billing info:`, proratedBilling);
  console.log(
    `[Storage] Processing tier upgrade for subscription:`,
    subscription,
    `with paymentMethod: ${paymentMethod}`
  );
  const updateFields: any = {
    storageLimit: targetTier.storageBytes,
    billingCycle,
    updatedAt: new Date(),
  };
  if (billingCycle === 'yearly') {
    updateFields.yearlyPrice = targetTier.priceYearly;
    updateFields.monthlyPrice = 0;
  } else {
    updateFields.monthlyPrice = targetTier.priceMonthly;
    updateFields.yearlyPrice = 0;
  }
  await Subscription.findOneAndUpdate({ organizationId }, updateFields, { new: true });
  const transactionId = `tier_upgrade_${Date.now()}`;
  console.log(`Storage tier upgraded for organization ${organizationId} to ${targetTier.name}`);
  return {
    transactionId,
    success: true,
  };
}

async function scheduleStorageTierUpgrade(
  organizationId: string,
  tierId: string,
  effectiveDate: Date
) {
  await Subscription.findOneAndUpdate(
    { organizationId },
    {
      scheduledStorageTierChange: {
        tierId,
        effectiveDate,
        scheduledAt: new Date(),
      },
    }
  );
}

async function enableAutoStorageUpgrade(organizationId: string, enabled: boolean) {
  await Subscription.findOneAndUpdate({ organizationId }, { autoStorageUpgrade: enabled });
}

async function cancelScheduledTierChange(organizationId: string) {
  await Subscription.findOneAndUpdate(
    { organizationId },
    { $unset: { scheduledStorageTierChange: 1 } }
  );
}

function formatBytes(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

export const GET = handle(app);
export const POST = handle(app);
export const PUT = handle(app);
