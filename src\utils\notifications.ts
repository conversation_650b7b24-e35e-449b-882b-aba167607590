import { NotificationType } from '@/hooks/useNotifications';
export function formatNotificationTime(timestamp: string): string {
  const date = new Date(timestamp);
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const diffSec = Math.floor(diffMs / 1000);
  const diffMin = Math.floor(diffSec / 60);
  const diffHour = Math.floor(diffMin / 60);
  const diffDay = Math.floor(diffHour / 24);

  if (diffSec < 60) {
    return 'just now';
  } else if (diffMin < 60) {
    return `${diffMin} minute${diffMin > 1 ? 's' : ''} ago`;
  } else if (diffHour < 24) {
    return `${diffHour} hour${diffHour > 1 ? 's' : ''} ago`;
  } else if (diffDay < 7) {
    return `${diffDay} day${diffDay > 1 ? 's' : ''} ago`;
  } else {
    return date.toLocaleDateString();
  }
}

export function getNotificationTitle(notification: NotificationType): string {
  const { type, metadata, title } = notification;

  if (title) return title;

  switch (type) {
    case 'task':
      return 'New Task Assignment';
    case 'mention':
      return 'You were mentioned';
    case 'team':
      return 'Team Update';
    case 'onboarding':
      return `Welcome to ${metadata?.organizationName || 'TaskMantra'}`;
    case 'system':
    default:
      return 'System Notification';
  }
}

export function getNotificationDescription(notification: NotificationType): string {
  const { type, metadata, description } = notification;

  if (description) return description;

  switch (type) {
    case 'task':
      return `You have been assigned to "${metadata?.taskName || 'a task'}" by ${metadata?.assignedBy || 'a team member'}`;
    case 'mention':
      return `${metadata?.mentionedBy || 'Someone'} mentioned you in ${metadata?.context || 'a comment'}`;
    case 'team':
      return `${metadata?.action || 'An update'} in your team ${metadata?.teamName || ''}`;
    case 'onboarding':
      return `You have been successfully onboarded to ${metadata?.organizationName || 'TaskMantra'}`;
    case 'system':
    default:
      return 'You have a new system notification';
  }
}

export function getNotificationLink(notification: NotificationType): string {
  const { type, metadata, link } = notification;

  if (link) return link;

  switch (type) {
    case 'task':
      return `/tasks/${metadata?.taskId || ''}`;
    case 'mention':
      return metadata?.link || '/';
    case 'team':
      return `/team/${metadata?.teamId || ''}`;
    case 'onboarding':
      return '/getting-started';
    case 'system':
    default:
      return '/';
  }
}

export function areBrowserNotificationsSupported(): boolean {
  return typeof window !== 'undefined' && 'Notification' in window;
}

export async function requestNotificationPermission(): Promise<boolean> {
  if (!areBrowserNotificationsSupported()) {
    return false;
  }

  if (Notification.permission === 'granted') {
    return true;
  }

  if (Notification.permission !== 'denied') {
    const permission = await Notification.requestPermission();
    return permission === 'granted';
  }

  return false;
}

export function showBrowserNotification(
  title: string,
  options?: globalThis.NotificationOptions
): void {
  if (areBrowserNotificationsSupported() && Notification.permission === 'granted') {
    new Notification(title, options);
  }
}
