{"version": 2, "functions": {"src/app/api/notifications/sse/route.ts": {"maxDuration": 30}, "src/app/api/webhooks/github/route.ts": {"maxDuration": 30}, "src/app/api/notes/[[...route]]/route.ts": {"maxDuration": 30}, "src/app/api/storage/[[...route]]/route.ts": {"maxDuration": 20}, "src/app/api/[[...route]]/route.ts": {"maxDuration": 20}}, "headers": [{"source": "/api/notifications/sse", "headers": [{"key": "Cache-Control", "value": "no-store, no-transform"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Content-Type", "value": "text/event-stream"}, {"key": "X-Accel-Buffering", "value": "no"}]}]}