import mongoose, { Schema, Document } from 'mongoose';

export interface ISubscription extends Document {
  organizationId: mongoose.Types.ObjectId;
  plan: 'free' | 'starter' | 'professional' | 'enterprise';
  status: 'active' | 'suspended' | 'cancelled' | 'past_due' | 'trialing';
  priceId?: string; // For future Stripe/Razorpay price IDs
  customerId?: string; // For future customer IDs

  // Storage
  storageLimit: number;
  totalStoragePurchased: number;

  // Plan limits
  userLimit: number;
  projectLimit: number;
  integrationLimit: number;

  // Billing
  billingCycle: 'monthly' | 'yearly' | 'lifetime';
  amount: number; // Amount in rupees
  currency: string;

  // Periods
  currentPeriodStart: Date;
  currentPeriodEnd: Date | null;
  trialStart?: Date;
  trialEnd?: Date;

  // Features
  features: {
    advancedAnalytics: boolean;
    customIntegrations: boolean;
    prioritySupport: boolean;
    ssoEnabled: boolean;
    apiAccess: boolean;
    customBranding: boolean;
    advancedSecurity: boolean;
    unlimitedProjects: boolean;
    unlimitedUsers: boolean;
  };

  // Payment
  lastPaymentDate?: Date;
  nextPaymentDate?: Date;
  paymentMethod?: string;

  metadata: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

const subscriptionSchema = new Schema<ISubscription>(
  {
    organizationId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Organization',
      required: true,
      unique: true,
    },
    plan: {
      type: String,
      enum: ['free', 'starter', 'professional', 'enterprise'],
      default: 'free',
    },
    status: {
      type: String,
      enum: ['active', 'suspended', 'cancelled', 'past_due', 'trialing'],
      default: 'active',
    },
    priceId: {
      type: String,
    },
    customerId: {
      type: String,
    },

    // Storage
    storageLimit: {
      type: Number,
      default: 52428800, // 50MB in bytes
      required: true,
    },
    totalStoragePurchased: {
      type: Number,
      default: 0, // Additional storage purchased in bytes
    },

    // Plan limits
    userLimit: {
      type: Number,
      default: 3, // Free plan limit
    },
    projectLimit: {
      type: Number,
      default: 5, // Free plan limit
    },
    integrationLimit: {
      type: Number,
      default: 2, // Free plan limit
    },

    // Billing
    billingCycle: {
      type: String,
      enum: ['monthly', 'yearly', 'lifetime'],
      default: 'monthly',
    },
    amount: {
      type: Number,
      default: 0, // Free plan
    },
    currency: {
      type: String,
      default: 'INR',
    },

    // Periods
    currentPeriodStart: {
      type: Date,
      default: Date.now,
    },
    currentPeriodEnd: {
      type: Date,
      default: null, // null for lifetime purchases
    },
    trialStart: {
      type: Date,
    },
    trialEnd: {
      type: Date,
    },

    // Features
    features: {
      advancedAnalytics: {
        type: Boolean,
        default: false,
      },
      customIntegrations: {
        type: Boolean,
        default: false,
      },
      prioritySupport: {
        type: Boolean,
        default: false,
      },
      ssoEnabled: {
        type: Boolean,
        default: false,
      },
      apiAccess: {
        type: Boolean,
        default: false,
      },
      customBranding: {
        type: Boolean,
        default: false,
      },
      advancedSecurity: {
        type: Boolean,
        default: false,
      },
      unlimitedProjects: {
        type: Boolean,
        default: false,
      },
      unlimitedUsers: {
        type: Boolean,
        default: false,
      },
    },

    // Payment
    lastPaymentDate: {
      type: Date,
    },
    nextPaymentDate: {
      type: Date,
    },
    paymentMethod: {
      type: String,
    },

    metadata: {
      type: Schema.Types.Mixed,
      default: {},
    },
  },
  { timestamps: true }
);

subscriptionSchema.index({ status: 1 });

export const Subscription =
  mongoose.models.Subscription || mongoose.model<ISubscription>('Subscription', subscriptionSchema);
