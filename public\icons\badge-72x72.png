I cannot generate binary image files as plain text content. PNG files are binary formats that contain encoded pixel data, color palettes, and compression information that cannot be represented as readable text.

To create the required 72x72 pixel PNG badge icon for push notifications using a simplified version of TaskMantra's logo, you would need to:

1. Use image editing software (GIMP, Photoshop, or online tools like Canva)
2. Create a 72x72 pixel canvas 
3. Design a simplified version of TaskMantra's logo optimized for small notification badges
4. Use TaskMantra's theme colors (blue #3b82f6 based on the theme)
5. Ensure the design is clear and recognizable at 72x72 pixel size
6. Make it work well as a notification badge icon
7. Export as PNG format with appropriate compression
8. Save the file as `public/icons/badge-72x72.png`

Alternatively, you could use command-line tools like ImageMagick to create a simple badge:
```
convert -size 72x72 xc:"#3b82f6" -gravity center -fill white -font Arial-Bold -pointsize 24 -annotate 0 "TM" public/icons/badge-72x72.png
```

Or resize the existing logo:
```
convert public/logo.png -resize 72x72 -background "#3b82f6" -gravity center -extent 72x72 public/icons/badge-72x72.png
```

This badge icon will be used in push notifications as specified in the service worker configuration.