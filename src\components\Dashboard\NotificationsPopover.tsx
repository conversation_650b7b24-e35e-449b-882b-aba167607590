import * as React from 'react';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import {
  Bell,
  Clock,
  MailIcon,
  UserPlus,
  AlertCircle,
  Loader2,
  WifiOff,
  CheckCheck,
  Trash2,
  <PERSON>tings,
  Users,
  RefreshCw,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import Link from 'next/link';
import { useInfiniteQuery, useQueryClient } from '@tanstack/react-query';
import { useInView } from 'react-intersection-observer';
import { NotificationClientService } from '@/services/NotificationClient.service';
import { formatDistanceToNow } from 'date-fns';
import { toast } from 'sonner';
import { motion, AnimatePresence } from 'framer-motion';
import { useNotifications } from '@/hooks/useNotifications';

export type NotificationType = {
  _id: string;
  userId: string;
  title: string;
  description: string;
  type: 'mention' | 'task' | 'team' | 'system' | 'onboarding';
  read: boolean;
  link: string;
  metadata?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
};

export function NotificationsPopover() {
  const [open, setOpen] = React.useState(false);
  const { ref, inView } = useInView({ threshold: 0.1 });
  const queryClient = useQueryClient();
  const {
    notifications: sseNotifications,
    unreadCount,
    isConnected,
    markAsRead,
    markAllAsRead,
    clearAllNotifications,
  } = useNotifications();

  const {
    data: paginatedData,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    status,
    refetch,
  } = useInfiniteQuery({
    queryKey: ['notifications-infinite-popover'],
    queryFn: async ({ pageParam = 0 }) => {
      const response = await NotificationClientService.getNotificationsClient(pageParam);
      return {
        notifications: response.notifications || [],
        hasMore: response.pagination
          ? response.pagination.page < response.pagination.pages - 1
          : false,
        nextPage: response.pagination ? response.pagination.page + 1 : undefined,
      };
    },
    getNextPageParam: lastPage => (lastPage.hasMore ? lastPage.nextPage : undefined),
    initialPageParam: 0,
    enabled: open,
    staleTime: 10 * 60 * 1000,
    gcTime: 15 * 60 * 1000,
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
    retry: 1,
  });

  const fetchNextPageTimeoutRef = React.useRef<ReturnType<typeof setTimeout> | null>(null);

  React.useEffect(() => {
    if (inView && hasNextPage && !isFetchingNextPage && open) {
      if (fetchNextPageTimeoutRef.current) {
        clearTimeout(fetchNextPageTimeoutRef.current);
      }

      fetchNextPageTimeoutRef.current = setTimeout(() => {
        fetchNextPage();
      }, 500);
    }

    return () => {
      if (fetchNextPageTimeoutRef.current) {
        clearTimeout(fetchNextPageTimeoutRef.current);
      }
    };
  }, [inView, fetchNextPage, hasNextPage, isFetchingNextPage, open]);

  const allNotifications = React.useMemo(() => {
    if (!open) return [];

    const notificationMap = new Map<string, NotificationType>();

    sseNotifications.forEach(notification => {
      notificationMap.set(notification._id, notification);
    });

    if (paginatedData?.pages) {
      paginatedData.pages.forEach(page => {
        page.notifications.forEach(notification => {
          if (!notificationMap.has(notification._id)) {
            notificationMap.set(notification._id, notification);
          }
        });
      });
    }

    return Array.from(notificationMap.values()).sort(
      (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    );
  }, [sseNotifications, paginatedData, open]);

  const formatTimestampCache = React.useRef<Map<string, { value: string; timestamp: number }>>(
    new Map()
  );

  const formatTimestamp = React.useCallback((timestamp: string) => {
    const now = Date.now();
    const cached = formatTimestampCache.current.get(timestamp);
    if (cached && now - cached.timestamp < 60000) {
      return cached.value;
    }

    try {
      const formatted = formatDistanceToNow(new Date(timestamp), { addSuffix: true });
      formatTimestampCache.current.set(timestamp, { value: formatted, timestamp: now });
      if (formatTimestampCache.current.size > 100) {
        const entries = Array.from(formatTimestampCache.current.entries());
        entries.sort((a, b) => b[1].timestamp - a[1].timestamp);
        formatTimestampCache.current = new Map(entries.slice(0, 100));
      }

      return formatted;
    } catch {
      return timestamp;
    }
  }, []);

  const getNotificationIcon = React.useCallback((type: NotificationType['type']) => {
    const iconStyles = 'h-4 w-4';

    const iconConfig = {
      mention: {
        colors:
          'bg-gradient-to-br from-blue-50 to-sky-50 dark:from-blue-900/20 dark:to-sky-900/20 ring-1 ring-blue-200 dark:ring-blue-800',
        icon: <Bell className={`${iconStyles} text-blue-600 dark:text-blue-400`} />,
      },
      task: {
        colors:
          'bg-gradient-to-br from-purple-50 to-violet-50 dark:from-purple-900/20 dark:to-violet-900/20 ring-1 ring-purple-200 dark:ring-purple-800',
        icon: <Clock className={`${iconStyles} text-purple-600 dark:text-purple-400`} />,
      },
      team: {
        colors:
          'bg-gradient-to-br from-emerald-50 to-green-50 dark:from-emerald-900/20 dark:to-green-900/20 ring-1 ring-emerald-200 dark:ring-emerald-800',
        icon: <Users className={`${iconStyles} text-emerald-600 dark:text-emerald-400`} />,
      },
      onboarding: {
        colors:
          'bg-gradient-to-br from-indigo-50 to-violet-50 dark:from-indigo-900/20 dark:to-violet-900/20 ring-1 ring-indigo-200 dark:ring-indigo-800',
        icon: <UserPlus className={`${iconStyles} text-indigo-600 dark:text-indigo-400`} />,
      },
      system: {
        colors:
          'bg-gradient-to-br from-amber-50 to-yellow-50 dark:from-amber-900/20 dark:to-yellow-900/20 ring-1 ring-amber-200 dark:ring-amber-800',
        icon: <Settings className={`${iconStyles} text-amber-600 dark:text-amber-400`} />,
      },
    };

    const config = iconConfig[type] || {
      colors:
        'bg-gradient-to-br from-slate-50 to-gray-50 dark:from-slate-900/20 dark:to-gray-900/20 ring-1 ring-slate-200 dark:ring-slate-800',
      icon: <MailIcon className={`${iconStyles} text-slate-600 dark:text-slate-400`} />,
    };

    return <div className={`p-2 rounded-lg shadow-sm ${config.colors}`}>{config.icon}</div>;
  }, []);

  const handleMarkAllAsRead = React.useCallback(async () => {
    try {
      const success = await markAllAsRead();
      if (!success) {
        toast.error('Failed to mark notifications as read');
      }
    } catch {
      toast.error('Failed to mark notifications as read');
    }
  }, [markAllAsRead]);

  const handleClearNotifications = React.useCallback(async () => {
    try {
      const success = await clearAllNotifications();

      if (success) {
        queryClient.setQueryData(['notifications-infinite-popover'], {
          pages: [{ notifications: [], hasMore: false }],
          pageParams: [0],
        });
        toast.success('Notifications cleared');
      } else {
        toast.error('Failed to clear notifications');
      }
    } catch {
      toast.error('Failed to clear notifications');
    }
  }, [clearAllNotifications, queryClient]);

  const handleNotificationClick = React.useCallback(
    async (notificationId: string) => {
      try {
        const success = await markAsRead(notificationId);
        if (success) {
          setOpen(false);
        } else {
          toast.error('Failed to mark notification as read');
        }
      } catch {
        toast.error('Failed to mark notification as read');
      }
    },
    [markAsRead]
  );

  const refreshTimeoutRef = React.useRef<ReturnType<typeof setTimeout> | null>(null);
  const [isRefreshing, setIsRefreshing] = React.useState(false);

  const handleRefreshNotifications = React.useCallback(() => {
    if (isRefreshing) return;

    setIsRefreshing(true);

    if (refreshTimeoutRef.current) {
      clearTimeout(refreshTimeoutRef.current);
    }

    refetch();
    toast.success('Notifications refreshed');

    refreshTimeoutRef.current = setTimeout(() => {
      setIsRefreshing(false);
    }, 3000);
  }, [isRefreshing, refetch]);

  React.useEffect(() => {
    return () => {
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current);
      }
      if (fetchNextPageTimeoutRef.current) {
        clearTimeout(fetchNextPageTimeoutRef.current);
      }
    };
  }, []);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="relative theme-button-secondary theme-shadow-sm overflow-visible"
        >
          <span className="relative inline-flex">
            <Bell className="h-6 w-6 group-hover:text-primary transition-colors duration-300" />
            {unreadCount > 0 && <span className="absolute inset-0 z-0 animate-ping-slow" />}
          </span>

          {unreadCount > 0 && (
            <motion.div
              initial={{ scale: 0, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ type: 'spring', stiffness: 500, damping: 25 }}
              className="absolute -top-1.5 -right-1.5 h-4 w-4 rounded-full bg-gradient-to-br from-red-500 via-rose-500 to-pink-500 text-[10px] font-medium text-white flex items-center justify-center shadow-md ring-2 ring-background"
            >
              {unreadCount > 99 ? '99+' : unreadCount}
            </motion.div>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent
        className="w-[420px] p-0 theme-shadow-lg theme-border backdrop-blur-xl theme-surface-elevated rounded-xl overflow-hidden"
        align="end"
        sideOffset={10}
        asChild
      >
        <motion.div
          initial={{ opacity: 0, y: 10, scale: 0.95 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          transition={{ duration: 0.25, ease: [0.22, 1, 0.36, 1] }}
        >
          <div className="flex flex-col theme-divider">
            <div className="flex items-center justify-between px-5 py-3.5">
              <div className="flex items-center gap-2">
                <h3 className="text-base font-semibold theme-text-primary">Notifications</h3>
                {unreadCount > 0 && (
                  <Badge
                    variant="destructive"
                    className="text-xs px-2.5 py-0.5 h-5 font-medium rounded-full bg-gradient-to-br from-red-500 via-rose-500 to-pink-500 shadow-sm"
                  >
                    {unreadCount} new
                  </Badge>
                )}
              </div>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8 rounded-full theme-button-ghost"
                onClick={handleRefreshNotifications}
                disabled={isRefreshing}
                title="Refresh notifications"
              >
                <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
              </Button>
            </div>

            {/* Action buttons */}
            <div className="flex items-center justify-between px-5 py-2.5 theme-surface">
              <div className="text-xs theme-text-secondary">
                {allNotifications.length > 0
                  ? `${allNotifications.length} notification${allNotifications.length !== 1 ? 's' : ''}`
                  : 'No notifications'}
              </div>
              <div className="flex gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-xs theme-hover-primary h-7 rounded-md"
                  onClick={handleMarkAllAsRead}
                  disabled={allNotifications.length === 0 || allNotifications.every(n => n.read)}
                >
                  <CheckCheck className="h-3.5 w-3.5 mr-1.5" />
                  Mark all read
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-xs hover:bg-destructive/10 hover:text-destructive theme-transition h-7 rounded-md"
                  onClick={handleClearNotifications}
                  disabled={allNotifications.length === 0}
                >
                  <Trash2 className="h-3.5 w-3.5 mr-1.5" />
                  Clear all
                </Button>
              </div>
            </div>
          </div>
          <ScrollArea className="h-[500px] overflow-y-auto">
            {!isConnected && (
              <div className="p-3 bg-amber-50/30 dark:bg-amber-950/20 border-b border-amber-100/50 dark:border-amber-800/30 flex items-center justify-center gap-2 backdrop-blur-sm">
                <WifiOff className="h-4 w-4 text-amber-500 dark:text-amber-400 animate-pulse" />
                <p className="text-xs text-amber-700 dark:text-amber-400">
                  Reconnecting to notification server...
                </p>
              </div>
            )}
            {status === 'pending' ? (
              <div className="flex h-[300px] items-center justify-center">
                <div className="flex flex-col items-center gap-4">
                  <div className="relative">
                    <div className="absolute inset-0 rounded-full bg-primary/20 animate-ping opacity-75"></div>
                    <div className="relative p-3 rounded-full bg-primary/10 backdrop-blur-sm">
                      <Loader2 className="h-6 w-6 animate-spin text-primary" />
                    </div>
                  </div>
                  <p className="text-sm theme-text-secondary">Loading your notifications...</p>
                </div>
              </div>
            ) : status === 'error' ? (
              <div className="flex h-[300px] items-center justify-center">
                <div className="flex flex-col items-center gap-4 max-w-[250px] text-center">
                  <div className="p-4 rounded-full bg-destructive/10 ring-1 ring-destructive/20">
                    <AlertCircle className="h-6 w-6 text-destructive" />
                  </div>
                  <div>
                    <p className="text-sm font-medium mb-1 theme-text-primary">
                      Failed to load notifications
                    </p>
                    <p className="text-xs theme-text-secondary mb-4">
                      There was a problem connecting to the notification service
                    </p>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    className="rounded-md theme-hover-primary theme-border"
                    onClick={handleRefreshNotifications}
                  >
                    <RefreshCw className="h-3.5 w-3.5 mr-2" />
                    Refresh
                  </Button>
                </div>
              </div>
            ) : allNotifications.length > 0 ? (
              <div className="divide-y divide-border/30">
                <AnimatePresence>
                  {/* Limit the number of notifications to improve performance */}
                  {allNotifications.slice(0, 50).map((notification, index) => (
                    <motion.div
                      key={notification._id}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{
                        duration: 0.2,
                        // Reduce animation delay for better performance
                        delay: Math.min(index * 0.03, 0.3),
                      }}
                      // Add layout=true for smoother list animations when items are removed
                      layout={true}
                    >
                      <Link
                        href={notification.link || '/notifications'}
                        onClick={() => handleNotificationClick(notification._id)}
                        className={cn(
                          'flex items-start gap-3 px-5 py-4 theme-hover-surface relative group',
                          !notification.read ? 'bg-primary/5' : ''
                        )}
                      >
                        {!notification.read && (
                          <div className="absolute left-0 top-0 bottom-0 w-0.5 bg-gradient-to-b from-primary/80 to-primary/40" />
                        )}

                        <div className="shrink-0 mt-0.5">
                          {getNotificationIcon(notification.type)}
                        </div>

                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between gap-2 mb-1">
                            <p
                              className={cn(
                                'text-sm leading-tight truncate',
                                !notification.read
                                  ? 'font-semibold theme-text-primary'
                                  : 'font-medium theme-text-primary'
                              )}
                            >
                              {notification.title}
                            </p>
                            <p className="text-xs theme-text-secondary whitespace-nowrap flex items-center gap-1.5">
                              {!notification.read && (
                                <span className="h-2 w-2 rounded-full bg-primary animate-pulse" />
                              )}
                              {formatTimestamp(notification.createdAt)}
                            </p>
                          </div>
                          <p className="text-sm theme-text-secondary line-clamp-2 mt-0.5 group-hover:text-foreground/80 theme-transition">
                            {notification.description}
                          </p>
                        </div>

                        <div className="shrink-0 opacity-0 group-hover:opacity-100 transition-opacity duration-200 self-center">
                          <div className="h-6 w-6 rounded-full bg-primary/10 flex items-center justify-center">
                            <CheckCheck className="h-3 w-3 text-primary" />
                          </div>
                        </div>
                      </Link>
                    </motion.div>
                  ))}
                </AnimatePresence>
                <div ref={ref} className="py-4 flex items-center justify-center">
                  {isFetchingNextPage && (
                    <Loader2 className="h-5 w-5 animate-spin text-muted-foreground" />
                  )}
                </div>
              </div>
            ) : (
              <div className="flex h-[300px] items-center justify-center px-6">
                <div className="flex flex-col items-center justify-center text-center space-y-5">
                  <div className="relative">
                    {/* Decorative elements */}
                    <div className="absolute -top-6 -left-6 h-4 w-4 rounded-full bg-primary/20 animate-pulse-slow"></div>
                    <div className="absolute -bottom-8 -right-8 h-6 w-6 rounded-full bg-primary/10 animate-pulse-slow animation-delay-700"></div>
                    <div className="absolute top-1/2 -right-10 h-3 w-3 rounded-full bg-primary/15 animate-pulse-slow animation-delay-1000"></div>

                    {/* Main icon */}
                    <div className="p-5 rounded-full bg-gradient-to-br from-muted/80 to-muted/30 backdrop-blur-sm ring-1 ring-border/50 shadow-sm">
                      <Bell className="h-8 w-8 text-muted-foreground" />
                    </div>
                  </div>
                  <div>
                    <h3 className="text-base font-medium mb-1.5 theme-text-primary">
                      All caught up!
                    </h3>
                    <p className="text-sm theme-text-secondary max-w-[260px]">
                      You don&apos;t have any notifications right now. We&apos;ll notify you when
                      something arrives.
                    </p>
                  </div>
                </div>
              </div>
            )}
          </ScrollArea>
          <div className="p-4 theme-divider theme-surface">
            <Link href="/home/<USER>" onClick={() => setOpen(false)}>
              <Button
                size="sm"
                className="w-full font-medium rounded-md theme-button-primary theme-shadow-sm"
              >
                View all notifications
              </Button>
            </Link>
          </div>
        </motion.div>
      </PopoverContent>
    </Popover>
  );
}
