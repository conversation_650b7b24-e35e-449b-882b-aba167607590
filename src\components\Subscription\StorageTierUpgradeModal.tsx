'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Modal from '../Global/Modal';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useSubscription } from '@/hooks/useSubscription';
import { useStorage } from '@/hooks/useStorage';
import { useRazorpay } from '@/hooks/useRazorpay';
import {
  SubscriptionService,
  STORAGE_TIERS,
  StorageTier,
  ProratedBillingInfo,
} from '@/services/Subscription.service';
import {
  CreditCard,
  CheckCircle,
  TrendingUp,
  Clock,
  Calculator,
  Zap,
  Shield,
  Star,
  ArrowRight,
  HardDrive,
  BarChart3,
} from 'lucide-react';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';
import {
  Line<PERSON><PERSON>,
  Line,
  XAxis,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from 'recharts';

interface StorageTierUpgradeModalProps {
  isOpen: boolean;
  onClose: () => void;
  organizationId: string;
  currentTierId: string;
  targetTierId: string;
}

interface ForecastData {
  month: string;
  projectedUsage: number;
  currentTierLimit: number;
  recommendedTierLimit: number;
}

export const StorageTierUpgradeModal: React.FC<StorageTierUpgradeModalProps> = ({
  isOpen,
  onClose,
  organizationId,
  currentTierId,
  targetTierId,
}) => {
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'yearly'>('monthly');
  const [isCalculatingProrated, setIsCalculatingProrated] = useState(false);
  const [proratedBilling, setProratedBilling] = useState<ProratedBillingInfo | null>(null);
  const [forecastData, setForecastData] = useState<ForecastData[]>([]);
  const [isLoadingForecast, setIsLoadingForecast] = useState(false);
  const [selectedTier, setSelectedTier] = useState<string>(targetTierId);

  const { isLoadingInfo } = useSubscription(organizationId);
  const { storageInfo, getStorageUsageAnalytics, storageUsageAnalytics } = useStorage();
  const { initiateStoragePayment, isLoading: isPaymentLoading } = useRazorpay();

  const currentTier = STORAGE_TIERS.find(tier => tier.id === currentTierId);
  const targetTier = STORAGE_TIERS.find(tier => tier.id === selectedTier);
  const availableTiers = STORAGE_TIERS.filter(
    tier => tier.storageBytes > (currentTier?.storageBytes || 0)
  );

  // Calculate prorated billing when tier or billing cycle changes
  useEffect(() => {
    if (isOpen && targetTier && organizationId) {
      calculateProratedBilling();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOpen, selectedTier, billingCycle, organizationId]);

  // Generate usage forecast
  useEffect(() => {
    if (isOpen && organizationId) {
      generateUsageForecast();
      if (storageInfo?.organizationId) {
        getStorageUsageAnalytics(storageInfo.organizationId);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOpen, organizationId, storageInfo?.organizationId]);

  const calculateProratedBilling = async () => {
    if (!targetTier) return;

    setIsCalculatingProrated(true);
    try {
      const billing = await SubscriptionService.calculateProratedBilling(
        organizationId,
        targetTier.id,
        billingCycle
      );
      setProratedBilling(billing);
    } catch (error) {
      toast.error('Failed to calculate pricing');
    } finally {
      setIsCalculatingProrated(false);
    }
  };

  const generateUsageForecast = async () => {
    setIsLoadingForecast(true);
    try {
      const forecast = await SubscriptionService.forecastStorageNeeds(organizationId, 6);

      const chartData: ForecastData[] = forecast.forecastedUsage.map(item => ({
        month: new Date(item.date).toLocaleDateString('en-US', { month: 'short' }),
        projectedUsage: item.projectedBytes / (1024 * 1024 * 1024), // Convert to GB
        currentTierLimit: (currentTier?.storageBytes || 0) / (1024 * 1024 * 1024),
        recommendedTierLimit: (targetTier?.storageBytes || 0) / (1024 * 1024 * 1024),
      }));

      setForecastData(chartData);
    } catch (error: any) {
      toast.error('Failed to generate forecast:', error);
    } finally {
      setIsLoadingForecast(false);
    }
  };

  const handleUpgrade = async () => {
    if (!targetTier || !proratedBilling) {
      toast.error('Please select a valid tier');
      return;
    }

    try {
      await initiateStoragePayment({
        storageMB: targetTier.storageBytes / (1024 * 1024), // Convert to MB for payment
        onSuccess: async (_response, _orderData) => {
          try {
            await SubscriptionService.upgradeStorageTier(
              organizationId,
              targetTier.id,
              billingCycle
            );

            toast.success('Storage tier upgraded successfully!');
            onClose();
          } catch (error) {
            toast.error('Payment successful but failed to upgrade tier. Please contact support.');
          }
        },
        onFailure: (error: Error) => {
          if (error.message === 'Payment cancelled by user') {
            toast.info('Payment cancelled');
          } else {
            toast.error(error.message || 'Payment failed. Please try again.');
          }
        },
      });
    } catch (error) {
      toast.error('Failed to initiate upgrade');
    }
  };

  const calculateYearlySavings = (tier: StorageTier) => {
    const monthlyTotal = tier.priceMonthly * 12;
    const yearlyTotal = tier.priceYearly;
    return monthlyTotal - yearlyTotal;
  };

  const getRecommendationReason = (_tier: StorageTier): string => {
    if (!storageUsageAnalytics) return '';

    const growthRate = storageUsageAnalytics.growthRate;
    if (growthRate > 0.5) {
      return 'High growth rate - perfect for expanding teams';
    } else if (growthRate > 0.2) {
      return 'Steady growth - good buffer for future needs';
    } else {
      return 'Stable usage - provides comfortable headroom';
    }
  };

  if (isLoadingInfo) {
    return (
      <Modal isOpen={isOpen} onClose={onClose} size="xl">
        <div className="space-y-4">
          <div className="loading-skeleton h-8 rounded-lg"></div>
          <div className="loading-skeleton h-64 rounded-lg"></div>
          <div className="loading-skeleton h-32 rounded-lg"></div>
        </div>
      </Modal>
    );
  }

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="xl">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-3 pb-4 border-b border-border/50">
          <div className="bg-primary/10 p-2 rounded-lg">
            <TrendingUp className="h-5 w-5 text-primary" />
          </div>
          <div>
            <h2 className="text-xl font-semibold theme-text-primary">Upgrade Storage Tier</h2>
            <p className="text-sm theme-text-secondary">
              Choose the perfect storage plan for your growing needs
            </p>
          </div>
        </div>

        {/* Current vs Target Comparison */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Card className="theme-surface rounded-lg theme-border">
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <HardDrive className="h-4 w-4 text-blue-500" />
                Current Plan
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="text-lg font-semibold theme-text-primary">
                  {currentTier?.name || 'Unknown'}
                </div>
                <div className="text-sm theme-text-secondary">
                  {currentTier?.storageFormatted || 'N/A'} storage
                </div>
                <div className="text-sm font-medium text-green-600 dark:text-green-400">
                  ₹{currentTier?.priceMonthly || 0}/month
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="theme-surface rounded-lg theme-border border-primary/50">
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <Star className="h-4 w-4 text-amber-500" />
                Recommended Plan
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="text-lg font-semibold theme-text-primary">{targetTier?.name}</div>
                <div className="text-sm theme-text-secondary">
                  {targetTier?.storageFormatted} storage
                </div>
                <div className="text-sm font-medium text-primary">
                  ₹
                  {billingCycle === 'yearly'
                    ? Math.round((targetTier?.priceYearly || 0) / 12)
                    : targetTier?.priceMonthly || 0}
                  /month
                </div>
                <Badge variant="secondary" className="text-xs">
                  {getRecommendationReason(targetTier!)}
                </Badge>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Billing Cycle Selection */}
        <div className="space-y-3">
          <h3 className="font-medium theme-text-primary">Billing Cycle</h3>
          <div className="grid grid-cols-2 gap-3">
            <Button
              variant={billingCycle === 'monthly' ? 'default' : 'outline'}
              onClick={() => setBillingCycle('monthly')}
              className="h-auto p-4 flex flex-col items-start space-y-1"
            >
              <div className="font-medium">Monthly</div>
              <div className="text-sm opacity-75">₹{targetTier?.priceMonthly || 0}/month</div>
            </Button>
            <Button
              variant={billingCycle === 'yearly' ? 'default' : 'outline'}
              onClick={() => setBillingCycle('yearly')}
              className="h-auto p-4 flex flex-col items-start space-y-1 relative"
            >
              <div className="font-medium">Yearly</div>
              <div className="text-sm opacity-75">
                ₹{Math.round((targetTier?.priceYearly || 0) / 12)}/month
              </div>
              <Badge
                variant="secondary"
                className="absolute -top-2 -right-2 bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300"
              >
                Save ₹{calculateYearlySavings(targetTier!)}
              </Badge>
            </Button>
          </div>
        </div>

        {/* Available Tiers */}
        <div className="space-y-3">
          <h3 className="font-medium theme-text-primary">Available Storage Tiers</h3>
          <div className="grid gap-3">
            {availableTiers.map(tier => {
              const isSelected = selectedTier === tier.id;
              const isRecommended = tier.id === targetTierId;

              return (
                <motion.div
                  key={tier.id}
                  whileHover={{ scale: 1.01 }}
                  className={cn(
                    'p-4 rounded-lg border cursor-pointer theme-transition',
                    isSelected
                      ? 'border-primary bg-primary/5'
                      : 'border-border/50 hover:border-border theme-surface'
                  )}
                  onClick={() => setSelectedTier(tier.id)}
                >
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        <div className="font-medium theme-text-primary">{tier.name}</div>
                        {isRecommended && (
                          <Badge
                            variant="secondary"
                            className="text-xs bg-amber-100 text-amber-700 dark:bg-amber-900 dark:text-amber-300"
                          >
                            Recommended
                          </Badge>
                        )}
                      </div>
                      <div className="text-sm theme-text-secondary">
                        {tier.storageFormatted} storage capacity
                      </div>
                      <div className="flex items-center gap-4 text-xs theme-text-secondary">
                        <span>Grace Period: {tier.gracePeriod} days</span>
                        <span>Monthly: ₹{tier.priceMonthly}</span>
                        <span>Yearly: ₹{Math.round(tier.priceYearly / 12)}/month</span>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      {isSelected && <CheckCircle className="h-5 w-5 text-primary" />}
                      <ArrowRight
                        className={cn(
                          'h-4 w-4 transition-opacity',
                          isSelected ? 'opacity-100' : 'opacity-50'
                        )}
                      />
                    </div>
                  </div>
                </motion.div>
              );
            })}
          </div>
        </div>

        {/* Usage Forecast Chart */}
        {!isLoadingForecast && forecastData.length > 0 && (
          <div className="space-y-3">
            <h3 className="font-medium theme-text-primary flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              Storage Usage Forecast
            </h3>
            <Card className="p-4">
              <div className="h-48 w-full">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={forecastData}>
                    <CartesianGrid strokeDasharray="3 3" strokeOpacity={0.3} />
                    <XAxis dataKey="month" className="text-xs" />
                    <YAxis
                      unit="GB"
                      tickFormatter={value => `${value.toFixed(1)}`}
                      className="text-xs"
                    />
                    <Tooltip
                      formatter={(value: number, name: string) => [
                        `${value.toFixed(2)} GB`,
                        name === 'projectedUsage'
                          ? 'Projected Usage'
                          : name === 'currentTierLimit'
                            ? 'Current Limit'
                            : 'New Tier Limit',
                      ]}
                    />
                    <Line
                      type="monotone"
                      dataKey="projectedUsage"
                      stroke="#8884d8"
                      strokeWidth={2}
                      dot={{ fill: '#8884d8', r: 4 }}
                    />
                    <Line
                      type="monotone"
                      dataKey="currentTierLimit"
                      stroke="#ff7300"
                      strokeDasharray="5 5"
                      dot={false}
                    />
                    <Line
                      type="monotone"
                      dataKey="recommendedTierLimit"
                      stroke="#00C49F"
                      strokeDasharray="5 5"
                      dot={false}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
              <div className="mt-2 text-xs theme-text-secondary">
                <span className="inline-block w-3 h-3 bg-blue-500 rounded-full mr-2"></span>
                Projected Usage
                <span className="inline-block w-3 h-3 bg-orange-500 rounded-full mr-2 ml-4"></span>
                Current Limit
                <span className="inline-block w-3 h-3 bg-green-500 rounded-full mr-2 ml-4"></span>
                New Tier Limit
              </div>
            </Card>
          </div>
        )}

        {/* Prorated Billing Information */}
        <AnimatePresence>
          {proratedBilling && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-3"
            >
              <h3 className="font-medium theme-text-primary flex items-center gap-2">
                <Calculator className="h-4 w-4" />
                Billing Breakdown
              </h3>
              <Card className="p-4 bg-gradient-to-r from-primary/5 to-purple-500/5 border-primary/20">
                <div className="space-y-3">
                  <div className="flex justify-between text-sm">
                    <span className="theme-text-secondary">Days remaining in current cycle:</span>
                    <span className="font-medium theme-text-primary">
                      {proratedBilling.daysRemaining} days
                    </span>
                  </div>

                  {proratedBilling.currentPlanRefundAmount > 0 && (
                    <div className="flex justify-between text-sm">
                      <span className="theme-text-secondary">Current plan refund:</span>
                      <span className="font-medium text-green-600 dark:text-green-400">
                        -₹{proratedBilling.currentPlanRefundAmount.toFixed(2)}
                      </span>
                    </div>
                  )}

                  <div className="flex justify-between text-sm">
                    <span className="theme-text-secondary">New plan prorated amount:</span>
                    <span className="font-medium theme-text-primary">
                      ₹{proratedBilling.newPlanProRatedAmount.toFixed(2)}
                    </span>
                  </div>

                  {proratedBilling.savingsAmount && (
                    <div className="flex justify-between text-sm">
                      <span className="theme-text-secondary">Yearly savings:</span>
                      <span className="font-medium text-green-600 dark:text-green-400">
                        ₹{proratedBilling.savingsAmount.toFixed(2)}
                      </span>
                    </div>
                  )}

                  <div className="border-t border-border/50 pt-3">
                    <div className="flex justify-between font-semibold">
                      <span className="theme-text-primary">Total due today:</span>
                      <span className="text-lg text-primary">
                        ₹{proratedBilling.totalDue.toFixed(2)}
                      </span>
                    </div>
                  </div>
                </div>
              </Card>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Feature Comparison */}
        <div className="space-y-3">
          <h3 className="font-medium theme-text-primary">What you'll get</h3>
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <span className="theme-text-secondary">
                  {targetTier?.storageFormatted} storage capacity
                </span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <span className="theme-text-secondary">
                  {targetTier?.gracePeriod} days grace period
                </span>
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <span className="theme-text-secondary">Priority support</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <span className="theme-text-secondary">Advanced analytics</span>
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-3 pt-4 border-t border-border/50">
          <Button
            variant="outline"
            onClick={onClose}
            className="flex-1"
            disabled={isPaymentLoading}
          >
            Cancel
          </Button>
          <Button
            onClick={handleUpgrade}
            className="flex-1 bg-primary hover:bg-primary/90"
            disabled={!targetTier || !proratedBilling || isCalculatingProrated || isPaymentLoading}
          >
            {isPaymentLoading ? (
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                Processing...
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <CreditCard className="h-4 w-4" />
                <span>Upgrade for ₹{proratedBilling?.totalDue.toFixed(2) || 0}</span>
              </div>
            )}
          </Button>
        </div>

        {/* Security Notice */}
        <div className="flex items-center justify-center gap-4 text-xs theme-text-secondary pt-2 border-t border-border/50">
          <div className="flex items-center gap-1">
            <Shield className="h-3 w-3 text-green-600 dark:text-green-400" />
            <span>Secure Payment</span>
          </div>
          <div className="flex items-center gap-1">
            <Clock className="h-3 w-3 text-blue-600 dark:text-blue-400" />
            <span>Instant Upgrade</span>
          </div>
          <div className="flex items-center gap-1">
            <Zap className="h-3 w-3 text-purple-600 dark:text-purple-400" />
            <span>No Downtime</span>
          </div>
        </div>
      </div>
    </Modal>
  );
};
