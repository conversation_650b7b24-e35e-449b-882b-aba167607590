'use client';

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { X, Download, Smartphone, Monitor, Info, ChevronDown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface BeforeInstallPromptEvent extends Event {
  readonly platforms: string[];
  readonly userChoice: Promise<{
    outcome: 'accepted' | 'dismissed';
    platform: string;
  }>;
  prompt(): Promise<void>;
}

interface PWAInstallPromptProps {
  variant?: 'default' | 'compact' | 'banner' | 'modal';
  position?: 'top' | 'bottom' | 'center';
  showOnMobile?: boolean;
  showOnDesktop?: boolean;
  autoShow?: boolean;
  dismissible?: true;
  customTitle?: string;
  customDescription?: string;
  onInstall?: (outcome: 'accepted' | 'dismissed') => void;
  onDismiss?: () => void;
  className?: string;
}

interface InstallInstructions {
  platform: string;
  icon: React.ReactNode;
  steps: string[];
}

const PWAInstallPrompt: React.FC<PWAInstallPromptProps> = ({
  variant = 'default',
  position = 'bottom',
  showOnMobile = true,
  showOnDesktop = true,
  autoShow = true,
  dismissible = true,
  customTitle,
  customDescription,
  onInstall,
  onDismiss,
  className = '',
}) => {
  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null);
  const [isVisible, setIsVisible] = useState(false);
  const [isInstalled, setIsInstalled] = useState(false);
  const [isInstalling, setIsInstalling] = useState(false);
  const [installStatus, setInstallStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [showInstructions, setShowInstructions] = useState(false);
  const [userAgent, setUserAgent] = useState('');
  const [abTestVariant, setAbTestVariant] = useState<'A' | 'B'>('A');
  const [isDismissed, setIsDismissed] = useState(false);
  const [focusedButton, setFocusedButton] = useState<string | null>(null);

  const installButtonRef = useRef<HTMLButtonElement>(null);
  const dismissButtonRef = useRef<HTMLButtonElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // A/B Test initialization
  useEffect(() => {
    const savedVariant = localStorage.getItem('pwa_install_ab_variant');
    if (savedVariant === 'A' || savedVariant === 'B') {
      setAbTestVariant(savedVariant);
    } else {
      const newVariant = Math.random() < 0.5 ? 'A' : 'B';
      setAbTestVariant(newVariant);
      localStorage.setItem('pwa_install_ab_variant', newVariant);
    }
  }, []);

  // Check if user has dismissed the prompt before
  useEffect(() => {
    const dismissed = localStorage.getItem('pwa_install_dismissed');
    const dismissedTime = localStorage.getItem('pwa_install_dismissed_time');

    if (dismissed === 'true' && dismissedTime) {
      const daysSinceDismissed = (Date.now() - parseInt(dismissedTime)) / (1000 * 60 * 60 * 24);
      if (daysSinceDismissed < 7) {
        // Show again after 7 days
        setIsDismissed(true);
      }
    }
  }, []);

  // Detect user agent and device
  useEffect(() => {
    setUserAgent(navigator.userAgent);

    // Check if app is already installed
    if (window.matchMedia && window.matchMedia('(display-mode: standalone)').matches) {
      setIsInstalled(true);
    }
  }, []);

  // Handle beforeinstallprompt event
  useEffect(() => {
    const handleBeforeInstallPrompt = (e: Event) => {
      e.preventDefault();
      const promptEvent = e as BeforeInstallPromptEvent;
      setDeferredPrompt(promptEvent);

      if (autoShow && !isDismissed && !isInstalled) {
        const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
          userAgent
        );
        if ((isMobile && showOnMobile) || (!isMobile && showOnDesktop)) {
          setIsVisible(true);
        }
      }

      // Track event
      trackAnalytics('pwa_install_prompt_shown', {
        variant: abTestVariant,
        userAgent: navigator.userAgent,
        platform: promptEvent.platforms?.join(',') || 'unknown',
      });
    };

    const handleAppInstalled = () => {
      setIsInstalled(true);
      setIsVisible(false);
      setInstallStatus('success');

      trackAnalytics('pwa_installed', {
        variant: abTestVariant,
        source: 'prompt',
      });

      // Clear dismissal preference since user installed
      localStorage.removeItem('pwa_install_dismissed');
      localStorage.removeItem('pwa_install_dismissed_time');
    };

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    window.addEventListener('appinstalled', handleAppInstalled);

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
      window.removeEventListener('appinstalled', handleAppInstalled);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [autoShow, isDismissed, isInstalled, showOnMobile, showOnDesktop, userAgent, abTestVariant]);

  // Analytics tracking
  const trackAnalytics = useCallback((event: string, data: Record<string, any>) => {
    // Store analytics events in localStorage for offline support
    const events = JSON.parse(localStorage.getItem('pwa_analytics') || '[]');
    events.push({
      event,
      data: {
        ...data,
        timestamp: new Date().toISOString(),
        url: window.location.href,
      },
    });
    localStorage.setItem('pwa_analytics', JSON.stringify(events));

    // Send to analytics service if online
    if (navigator.onLine && typeof window !== 'undefined') {
      fetch('/api/analytics/pwa', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ event, data }),
      }).catch(() => {
        // Silently fail, data is stored locally for retry
      });
    }
  }, []);

  // Handle install button click
  const handleInstall = useCallback(async () => {
    if (!deferredPrompt) {
      setShowInstructions(true);
      trackAnalytics('pwa_manual_instructions_shown', { variant: abTestVariant });
      return;
    }

    setIsInstalling(true);

    try {
      await deferredPrompt.prompt();
      const { outcome } = await deferredPrompt.userChoice;

      trackAnalytics('pwa_install_choice', {
        outcome,
        variant: abTestVariant,
      });

      if (outcome === 'accepted') {
        setInstallStatus('success');
        onInstall?.(outcome);
      } else {
        setInstallStatus('idle');
        onInstall?.(outcome);
      }

      setDeferredPrompt(null);
      setIsVisible(false);
    } catch (error) {
      setInstallStatus('error');
      trackAnalytics('pwa_install_error', {
        error: error instanceof Error ? error.message : 'Unknown error',
        variant: abTestVariant,
      });
    } finally {
      setIsInstalling(false);
    }
  }, [deferredPrompt, onInstall, abTestVariant, trackAnalytics]);

  // Handle dismiss
  const handleDismiss = useCallback(() => {
    setIsVisible(false);
    setIsDismissed(true);

    // Store dismissal preference
    localStorage.setItem('pwa_install_dismissed', 'true');
    localStorage.setItem('pwa_install_dismissed_time', Date.now().toString());

    trackAnalytics('pwa_install_dismissed', { variant: abTestVariant });
    onDismiss?.();
  }, [onDismiss, abTestVariant, trackAnalytics]);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isVisible) return;

      switch (e.key) {
        case 'Escape':
          if (dismissible) {
            handleDismiss();
          }
          break;
        case 'Tab':
          e.preventDefault();
          if (e.shiftKey) {
            // Shift+Tab - previous element
            if (focusedButton === 'install') {
              dismissButtonRef.current?.focus();
              setFocusedButton('dismiss');
            } else {
              installButtonRef.current?.focus();
              setFocusedButton('install');
            }
          } else {
            // Tab - next element
            if (focusedButton === 'dismiss') {
              installButtonRef.current?.focus();
              setFocusedButton('install');
            } else {
              dismissButtonRef.current?.focus();
              setFocusedButton('dismiss');
            }
          }
          break;
        case 'Enter':
        case ' ':
          e.preventDefault();
          if (focusedButton === 'install') {
            handleInstall();
          } else if (focusedButton === 'dismiss') {
            handleDismiss();
          }
          break;
      }
    };

    if (isVisible) {
      document.addEventListener('keydown', handleKeyDown);
      // Auto-focus first button when prompt appears
      setTimeout(() => {
        installButtonRef.current?.focus();
        setFocusedButton('install');
      }, 100);
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isVisible, focusedButton, handleInstall, handleDismiss, dismissible]);

  // Get platform-specific install instructions
  const getInstallInstructions = (): InstallInstructions[] => {
    const instructions: InstallInstructions[] = [];

    if (/iPhone|iPad|iPod/i.test(userAgent)) {
      instructions.push({
        platform: 'iOS Safari',
        icon: <Smartphone className="w-5 h-5" />,
        steps: [
          'Tap the Share button in Safari',
          'Scroll down and tap "Add to Home Screen"',
          'Tap "Add" in the top-right corner',
        ],
      });
    }

    if (/Android/i.test(userAgent) && /Chrome/i.test(userAgent)) {
      instructions.push({
        platform: 'Android Chrome',
        icon: <Smartphone className="w-5 h-5" />,
        steps: [
          'Tap the menu button (three dots)',
          'Tap "Add to Home screen"',
          'Tap "Add" to confirm',
        ],
      });
    }

    if (/Chrome/i.test(userAgent) && !/Mobile/i.test(userAgent)) {
      instructions.push({
        platform: 'Desktop Chrome',
        icon: <Monitor className="w-5 h-5" />,
        steps: [
          'Click the install icon in the address bar',
          'Or click the menu (three dots) → "Install TaskMantra"',
          'Click "Install" to confirm',
        ],
      });
    }

    return instructions;
  };

  // Don't render if conditions not met
  if (isInstalled || !isVisible || isDismissed) {
    return null;
  }

  const title =
    customTitle || (abTestVariant === 'A' ? 'Install TaskMantra' : 'Get TaskMantra App');
  const description =
    customDescription ||
    (abTestVariant === 'A'
      ? 'Install TaskMantra for quick access and offline functionality'
      : 'Access TaskMantra instantly with our fast, reliable app experience');

  const positionClasses = {
    top: 'top-4',
    bottom: 'bottom-4',
    center: 'top-1/2 transform -translate-y-1/2',
  };

  const variantClasses = {
    default: 'w-full max-w-md mx-auto',
    compact: 'w-auto',
    banner: 'w-full',
    modal: 'w-full max-w-lg mx-auto',
  };

  if (variant === 'banner') {
    return (
      <div
        ref={containerRef}
        className={`fixed ${positionClasses[position]} left-0 right-0 z-50 px-4 ${className}`}
        role="dialog"
        aria-labelledby="pwa-install-title"
        aria-describedby="pwa-install-description"
      >
        <Alert className="bg-gradient-to-r from-blue-500 to-purple-600 text-white border-0 shadow-lg">
          <Download className="h-4 w-4" />
          <AlertDescription className="flex items-center justify-between w-full">
            <div className="flex-1">
              <span className="font-semibold">{title}</span>
              <span className="ml-2 text-blue-100">{description}</span>
            </div>
            <div className="flex items-center gap-2 ml-4">
              <Button
                ref={installButtonRef}
                onClick={handleInstall}
                disabled={isInstalling}
                size="sm"
                variant="secondary"
                className="bg-white text-blue-600 hover:bg-blue-50"
              >
                {isInstalling ? 'Installing...' : 'Install'}
              </Button>
              {dismissible && (
                <Button
                  ref={dismissButtonRef}
                  onClick={handleDismiss}
                  size="sm"
                  variant="ghost"
                  className="text-white hover:bg-white/20 p-1"
                  aria-label="Dismiss install prompt"
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div
      className={`fixed ${positionClasses[position]} left-4 right-4 z-50 ${className}`}
      role="dialog"
      aria-labelledby="pwa-install-title"
      aria-describedby="pwa-install-description"
    >
      <Card className={`${variantClasses[variant]} shadow-xl border-0 bg-white dark:bg-gray-900`}>
        <CardHeader className="pb-4">
          <div className="flex items-start justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                <Download className="w-6 h-6 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <CardTitle id="pwa-install-title" className="text-lg font-semibold">
                  {title}
                  {abTestVariant === 'B' && (
                    <Badge variant="secondary" className="ml-2 text-xs">
                      Fast & Reliable
                    </Badge>
                  )}
                </CardTitle>
                <CardDescription id="pwa-install-description" className="mt-1">
                  {description}
                </CardDescription>
              </div>
            </div>
            {dismissible && (
              <Button
                ref={dismissButtonRef}
                onClick={handleDismiss}
                variant="ghost"
                size="sm"
                className="p-1 h-8 w-8"
                aria-label="Dismiss install prompt"
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
        </CardHeader>

        <CardContent className="pt-0">
          {installStatus === 'success' && (
            <Alert className="mb-4 bg-green-50 border-green-200 text-green-800">
              <Info className="h-4 w-4" />
              <AlertDescription>TaskMantra has been installed successfully!</AlertDescription>
            </Alert>
          )}

          {installStatus === 'error' && (
            <Alert className="mb-4 bg-red-50 border-red-200 text-red-800">
              <Info className="h-4 w-4" />
              <AlertDescription>
                Installation failed. Please try again or use manual installation.
              </AlertDescription>
            </Alert>
          )}

          <div className="flex flex-col gap-3">
            <Button
              ref={installButtonRef}
              onClick={handleInstall}
              disabled={isInstalling}
              className="w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-medium"
              size="lg"
            >
              {isInstalling ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                  Installing...
                </>
              ) : (
                <>
                  <Download className="w-4 h-4 mr-2" />
                  {abTestVariant === 'A' ? 'Install App' : 'Get App Now'}
                </>
              )}
            </Button>

            {!deferredPrompt && (
              <Button
                onClick={() => setShowInstructions(!showInstructions)}
                variant="outline"
                className="w-full"
              >
                <ChevronDown
                  className={`w-4 h-4 mr-2 transition-transform ${showInstructions ? 'rotate-180' : ''}`}
                />
                Manual Installation
              </Button>
            )}
          </div>

          {showInstructions && (
            <div className="mt-4 space-y-4">
              {getInstallInstructions().map((instruction, index) => (
                <div key={index} className="border rounded-lg p-3 bg-gray-50 dark:bg-gray-800">
                  <div className="flex items-center gap-2 mb-2 font-medium text-sm text-gray-900 dark:text-gray-100">
                    {instruction.icon}
                    {instruction.platform}
                  </div>
                  <ol className="list-decimal pl-6 space-y-1 text-sm text-gray-600 dark:text-gray-400">
                    {instruction.steps.map((step, stepIndex) => (
                      <li key={stepIndex}>{step}</li>
                    ))}
                  </ol>
                </div>
              ))}
            </div>
          )}

          {abTestVariant === 'B' && (
            <div className="mt-4 text-center">
              <div className="flex items-center justify-center gap-4 text-sm text-gray-500">
                <div className="flex items-center gap-1">
                  <div className="w-2 h-2 bg-green-500 rounded-full" />
                  Works Offline
                </div>
                <div className="flex items-center gap-1">
                  <div className="w-2 h-2 bg-blue-500 rounded-full" />
                  Lightning Fast
                </div>
                <div className="flex items-center gap-1">
                  <div className="w-2 h-2 bg-purple-500 rounded-full" />
                  Native Feel
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default PWAInstallPrompt;
