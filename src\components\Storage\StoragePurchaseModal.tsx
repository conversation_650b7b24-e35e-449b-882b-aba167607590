'use client';

import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Modal from '../Global/Modal';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useStorage, PurchaseInfo } from '@/hooks/useStorage';
import { CreditCard, Calculator, Zap, Shield, InfinityIcon } from 'lucide-react';
import { toast } from 'sonner';
import { useRazorpay, RazorpayResponse } from '@/hooks/useRazorpay';
import { cn } from '@/lib/utils';

interface StoragePurchaseModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export const StoragePurchaseModal: React.FC<StoragePurchaseModalProps> = ({ isOpen, onClose }) => {
  const [storageMB, setStorageMB] = useState<number>(50);
  const [priceInfo, setPriceInfo] = useState<PurchaseInfo | null>(null);
  const [isProcessingPayment] = useState(false);

  const { calculatePrice, purchaseStorage, isCalculatingPrice, isPurchasing } = useStorage();

  const { initiateStoragePayment, isLoading: isPaymentLoading } = useRazorpay();

  // Use ref to store the latest calculatePrice function to avoid dependency issues
  const calculatePriceRef = useRef(calculatePrice);
  calculatePriceRef.current = calculatePrice;

  // Calculate price when storage amount changes (only when modal is open)
  useEffect(() => {
    if (isOpen && storageMB > 0) {
      const debounceTimer = setTimeout(async () => {
        const price = await calculatePriceRef.current(storageMB);
        setPriceInfo(price);
      }, 500);

      return () => clearTimeout(debounceTimer);
    }
  }, [storageMB, isOpen]); // Only depend on storageMB and isOpen

  // Reset price info when modal closes
  useEffect(() => {
    if (!isOpen) {
      setPriceInfo(null);
    }
  }, [isOpen]);

  const handleStorageChange = (value: string) => {
    const numValue = parseInt(value) || 0;
    if (numValue >= 0 && numValue <= 10000) {
      // Max 10GB purchase at once
      setStorageMB(numValue);
    }
  };

  const handlePurchase = async () => {
    if (!priceInfo || storageMB <= 0) {
      toast.error('Please enter a valid storage amount');
      return;
    }

    try {
      await initiateStoragePayment({
        storageMB,
        onSuccess: async (response: RazorpayResponse, _orderData) => {
          try {
            await purchaseStorage({
              storageMB,
              paymentId: response.razorpay_payment_id,
              orderId: response.razorpay_order_id,
              paymentMethod: 'razorpay',
            });

            toast.success('Storage purchased successfully!');
            onClose();
            setStorageMB(50);
            setPriceInfo(null);
          } catch (error) {
            toast.error('Payment successful but failed to update storage. Please contact support.');
          }
        },
        onFailure: (error: Error) => {
          if (error.message === 'Payment cancelled by user') {
            toast.info('Payment cancelled');
          } else {
            toast.error(error.message || 'Payment failed. Please try again.');
          }
        },
      });
    } catch (error: any) {
      toast.error(error.message || 'Failed to initiate payment');
    }
  };

  const quickSelectOptions = [25, 50, 100, 250, 500, 1000];

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="lg">
      <div className="space-y-4">
        {/* Header */}
        <div className="flex items-center gap-3 pb-2 border-b border-border/50">
          <div className="bg-primary/10 p-2 rounded-lg">
            <Calculator className="h-5 w-5 text-primary" />
          </div>
          <div>
            <h2 className="text-lg font-semibold theme-text-primary">Purchase Storage</h2>
            <p className="text-sm theme-text-secondary">Expand your storage capacity</p>
          </div>
        </div>

        {/* Storage Selection */}
        <div className="space-y-3">
          <Label htmlFor="storage-amount" className="text-sm font-medium theme-text-primary">
            Storage Amount
          </Label>

          <div className="relative">
            <Input
              id="storage-amount"
              type="number"
              value={storageMB}
              onChange={e => handleStorageChange(e.target.value)}
              placeholder="Enter amount"
              min="1"
              max="10000"
              className="theme-input pr-12 text-center font-medium"
            />
            <div className="absolute right-3 top-1/2 -translate-y-1/2 text-sm theme-text-secondary">
              MB
            </div>
          </div>

          {/* Quick Select */}
          <div className="grid grid-cols-6 gap-2">
            {quickSelectOptions.map(amount => {
              const isSelected = storageMB === amount;
              const displayText = amount >= 1000 ? `${amount / 1000}GB` : `${amount}MB`;

              return (
                <Button
                  key={amount}
                  variant={isSelected ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setStorageMB(amount)}
                  className={cn(
                    'h-10 text-xs font-medium theme-transition',
                    isSelected && 'bg-primary text-primary-foreground'
                  )}
                >
                  {displayText}
                </Button>
              );
            })}
          </div>
        </div>

        {/* Price Display */}
        <AnimatePresence>
          {priceInfo && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.2 }}
              className="bg-gradient-to-r from-primary/5 to-purple-500/5 p-4 rounded-lg border border-primary/20"
            >
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <div className="text-sm theme-text-secondary">
                    {priceInfo.storageMB >= 1000
                      ? `${(priceInfo.storageMB / 1000).toFixed(1)}GB`
                      : `${priceInfo.storageMB}MB`}{' '}
                    @ ₹{priceInfo.pricePerMB}/MB
                  </div>
                  <div className="flex items-center gap-2">
                    <InfinityIcon className="h-4 w-4 text-amber-600 dark:text-amber-400" />
                    <span className="text-xs text-amber-600 dark:text-amber-400">
                      Lifetime Access
                    </span>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold text-primary">
                    ₹{priceInfo.totalPrice.toLocaleString()}
                  </div>
                  <div className="text-xs theme-text-secondary">Total Price</div>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Action Buttons */}
        <div className="flex gap-3 pt-2">
          <Button
            variant="outline"
            onClick={onClose}
            className="flex-1"
            disabled={isPurchasing || isProcessingPayment}
          >
            Cancel
          </Button>
          <Button
            onClick={handlePurchase}
            className="flex-1 bg-primary hover:bg-primary/90"
            disabled={
              !priceInfo || storageMB <= 0 || isPurchasing || isPaymentLoading || isCalculatingPrice
            }
          >
            {isPaymentLoading || isPurchasing ? (
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                {isPaymentLoading ? 'Opening...' : 'Processing...'}
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <CreditCard className="h-4 w-4" />
                <span>Purchase ₹{priceInfo?.totalPrice.toLocaleString() || 0}</span>
              </div>
            )}
          </Button>
        </div>

        {/* Payment Info */}
        <div className="flex items-center justify-center gap-4 text-xs theme-text-secondary pt-2 border-t border-border/50">
          <div className="flex items-center gap-1">
            <Shield className="h-3 w-3 text-green-600 dark:text-green-400" />
            <span>Secure</span>
          </div>
          <div className="flex items-center gap-1">
            <CreditCard className="h-3 w-3 text-blue-600 dark:text-blue-400" />
            <span>Razorpay</span>
          </div>
          <div className="flex items-center gap-1">
            <Zap className="h-3 w-3 text-purple-600 dark:text-purple-400" />
            <span>Instant</span>
          </div>
        </div>
      </div>
    </Modal>
  );
};
