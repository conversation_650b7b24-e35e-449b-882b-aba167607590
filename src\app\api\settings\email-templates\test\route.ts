import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/options';
import { connectDB } from '@/Utility/db';
import { User } from '@/models/User';
import { resend } from '@/lib/resend';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await connectDB();
    const user = await User.findOne({ email: session.user.email });
    if (!user || !user.organizationId) {
      return NextResponse.json({ error: 'User or organization not found' }, { status: 404 });
    }

    const { to, templateId } = await request.json();

    if (!to || !templateId) {
      return NextResponse.json(
        { error: 'Email address and template ID are required' },
        { status: 400 }
      );
    }

    // Mock email templates data (in production, this would come from database)
    const emailTemplates = [
      {
        id: 'task-assignment',
        name: 'Task Assignment',
        subject: 'New Task Assigned: {{task.title}}',
        content: `Hi {{user.name}},

You have been assigned a new task: {{task.title}}

Project: {{project.name}}
Due Date: {{due_date}}

Description:
{{task.description}}

You can view and manage this task in your TaskMantra dashboard.

Best regards,
The TaskMantra Team`,
        fromName: 'TaskMantra',
        fromEmail: '<EMAIL>',
      },
      {
        id: 'task-completed',
        name: 'Task Completed',
        subject: 'Task Completed: {{task.title}}',
        content: `Hi {{user.name}},

Great news! The task "{{task.title}}" has been completed by {{completed_by}}.

Project: {{project.name}}
Completed on: {{completion_date}}

You can view the completed task and any deliverables in your TaskMantra dashboard.

Best regards,
The TaskMantra Team`,
        fromName: 'TaskMantra',
        fromEmail: '<EMAIL>',
      },
      {
        id: 'project-invitation',
        name: 'Project Invitation',
        subject: "You've been invited to {{project.name}}",
        content: `Hi {{user.name}},

{{invited_by}} has invited you to join the project "{{project.name}}" in {{organization.name}}.

Project Description:
{{project.description}}

Click the link below to view the project:
{{project.link}}

Best regards,
The TaskMantra Team`,
        fromName: 'TaskMantra',
        fromEmail: '<EMAIL>',
      },
      {
        id: 'due-date-reminder',
        name: 'Due Date Reminder',
        subject: 'Reminder: {{task.title}} is due tomorrow',
        content: `Hi {{user.name}},

This is a friendly reminder that your task "{{task.title}}" is due tomorrow.

Project: {{project.name}}
Due Date: {{due_date}}
Priority: {{task.priority}}

Please make sure to complete the task on time or update its status if needed.

Best regards,
The TaskMantra Team`,
        fromName: 'TaskMantra',
        fromEmail: '<EMAIL>',
      },
      {
        id: 'team-invitation',
        name: 'Team Invitation',
        subject: 'Welcome to {{organization.name}}',
        content: `Hi {{user.name}},

Welcome to {{organization.name}}! {{invited_by}} has invited you to join the team as a {{role}}.

You now have access to:
- Create and manage projects
- Collaborate with team members
- Track tasks and deadlines
- Access team resources

Get started by logging into your TaskMantra dashboard:
{{dashboard.link}}

If you have any questions, feel free to reach out to your team or our support team.

Best regards,
The TaskMantra Team`,
        fromName: 'TaskMantra',
        fromEmail: '<EMAIL>',
      },
    ];

    const template = emailTemplates.find(t => t.id === templateId);
    if (!template) {
      return NextResponse.json({ error: 'Template not found' }, { status: 404 });
    }

    // Replace variables with sample data for testing
    const sampleData = {
      'user.name': user.name || 'Test User',
      'task.title': 'Sample Task Title',
      'task.description': 'This is a sample task description for testing purposes.',
      'task.priority': 'High',
      'project.name': 'Sample Project',
      'project.description': 'This is a sample project description.',
      'project.link': 'https://taskmantra.com/projects/sample',
      due_date: new Date(Date.now() + 24 * 60 * 60 * 1000).toLocaleDateString(),
      completion_date: new Date().toLocaleDateString(),
      completed_by: 'John Doe',
      invited_by: 'Jane Smith',
      'organization.name': 'Sample Organization',
      role: 'Team Member',
      'dashboard.link': 'https://taskmantra.com/home',
    };

    let subject = template.subject;
    let content = template.content;

    // Replace variables in subject and content
    Object.entries(sampleData).forEach(([key, value]) => {
      const variable = `{{${key}}}`;
      subject = subject.replace(new RegExp(variable, 'g'), value);
      content = content.replace(new RegExp(variable, 'g'), value);
    });

    // Send test email using Resend
    try {
      const emailResult = await resend.emails.send({
        from: `${template.fromName} <${template.fromEmail}>`,
        to: [to],
        subject: `[TEST] ${subject}`,
        text: content,
        html: content.replace(/\n/g, '<br>'),
      });

      return NextResponse.json({
        success: true,
        message: 'Test email sent successfully',
        emailId: emailResult.data?.id,
      });
    } catch (emailError: any) {
      console.error('Email sending error:', emailError);
      return NextResponse.json(
        { error: 'Failed to send email', details: emailError.message },
        { status: 500 }
      );
    }
  } catch (error: any) {
    console.error('Test email error:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
