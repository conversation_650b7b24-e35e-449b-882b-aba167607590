import axios from 'axios';
import { MindMapFilters, CreateMindMapRequest, UpdateMindMapRequest } from '@/types/MindMapsTypes';

const API_BASE = '/api/mind-maps';

export const MindMapService = {
  // Get all mind maps
  getMindMaps: async (filters?: MindMapFilters) => {
    const params = new URLSearchParams();

    if (filters?.projectId && filters.projectId !== 'all') {
      params.append('projectId', filters.projectId);
    }
    if (filters?.status && filters.status !== 'all') {
      params.append('status', filters.status);
    }
    if (filters?.search) {
      params.append('search', filters.search);
    }
    if (filters?.tags && filters.tags.length > 0) {
      filters.tags.forEach(tag => params.append('tags', tag));
    }

    const response = await axios.get(`${API_BASE}?${params}`);
    return response.data;
  },

  // Get single mind map
  getMindMap: async (id: string) => {
    const response = await axios.get(`${API_BASE}/${id}`);
    return response.data;
  },

  // Legacy method for backward compatibility
  fetchMindMapById: async (id: string) => {
    const response = await axios.get(`${API_BASE}/${id}`);
    return response.data;
  },

  // Create mind map
  createMindMap: async (data: CreateMindMapRequest) => {
    const response = await axios.post(API_BASE, data);
    return response.data;
  },

  // Update mind map
  updateMindMap: async (id: string, data: UpdateMindMapRequest) => {
    const response = await axios.put(`${API_BASE}/${id}`, data);
    return response.data;
  },

  // Delete mind map
  deleteMindMap: async (id: string) => {
    const response = await axios.delete(`${API_BASE}/${id}`);
    return response.data;
  },

  // Duplicate mind map
  duplicateMindMap: async (id: string, title: string) => {
    const response = await axios.post(`${API_BASE}/${id}/duplicate`, { title });
    return response.data;
  },

  // Export mind map
  exportMindMap: async (id: string, format: string) => {
    const response = await axios.post(`${API_BASE}/${id}/export`, { format });
    return response.data;
  },

  // Search mind maps
  searchMindMaps: async (query: string, filters?: MindMapFilters) => {
    const params = new URLSearchParams();
    params.append('q', query);

    if (filters?.projectId && filters.projectId !== 'all') {
      params.append('projectId', filters.projectId);
    }
    if (filters?.status && filters.status !== 'all') {
      params.append('status', filters.status);
    }
    if (filters?.tags && filters.tags.length > 0) {
      filters.tags.forEach(tag => params.append('tags', tag));
    }

    const response = await axios.get(`${API_BASE}/search?${params}`);
    return response.data;
  },

  // Get mind map templates
  getTemplates: async (category?: string) => {
    const params = new URLSearchParams();
    if (category) {
      params.append('category', category);
    }

    const response = await axios.get(`${API_BASE}/templates?${params}`);
    return response.data;
  },

  // Create template from mind map
  createTemplate: async (
    id: string,
    templateData: { name: string; description: string; category: string; isPublic?: boolean }
  ) => {
    const response = await axios.post(`${API_BASE}/${id}/template`, templateData);
    return response.data;
  },

  // Create mind map from template
  createFromTemplate: async (
    templateId: string,
    data: { title: string; description?: string; projectId?: string }
  ) => {
    const response = await axios.post(`${API_BASE}/templates/${templateId}/create`, data);
    return response.data;
  },

  // Get mind map analytics
  getAnalytics: async (id: string) => {
    const response = await axios.get(`${API_BASE}/${id}/analytics`);
    return response.data;
  },

  // Bulk operations
  bulkDelete: async (ids: string[]) => {
    const response = await axios.post(`${API_BASE}/bulk/delete`, { ids });
    return response.data;
  },

  bulkUpdateStatus: async (ids: string[], status: 'draft' | 'active' | 'archived' | 'template') => {
    const response = await axios.post(`${API_BASE}/bulk/status`, { ids, status });
    return response.data;
  },

  bulkAddTags: async (ids: string[], tags: string[]) => {
    const response = await axios.post(`${API_BASE}/bulk/tags/add`, { ids, tags });
    return response.data;
  },

  bulkRemoveTags: async (ids: string[], tags: string[]) => {
    const response = await axios.post(`${API_BASE}/bulk/tags/remove`, { ids, tags });
    return response.data;
  },

  // Node operations
  addNode: async (id: string, node: any) => {
    const response = await axios.post(`${API_BASE}/${id}/nodes`, node);
    return response.data;
  },

  updateNode: async (id: string, nodeId: string, updates: any) => {
    const response = await axios.put(`${API_BASE}/${id}/nodes/${nodeId}`, updates);
    return response.data;
  },

  deleteNode: async (id: string, nodeId: string) => {
    const response = await axios.delete(`${API_BASE}/${id}/nodes/${nodeId}`);
    return response.data;
  },

  // Connection operations
  addConnection: async (id: string, connection: any) => {
    const response = await axios.post(`${API_BASE}/${id}/connections`, connection);
    return response.data;
  },
};
