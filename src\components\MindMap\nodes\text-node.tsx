'use client';

import type React from 'react';

import { memo, useState } from 'react';
import { <PERSON>le, Position, type NodeProps } from 'reactflow';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Type, Edit3 } from 'lucide-react';
import { useNodeEdit } from '../contexts/node-edit-context';
import { useMindMapStore } from '@/stores/mindMapStore';

interface TextNodeData {
  text: string;
  fontSize: number;
  color: string;
  backgroundColor: string;
  dragging?: boolean;
  isConnecting?: boolean;
  isConnectingFrom?: boolean;
}

export const TextNode = memo(({ id, data, selected }: NodeProps<TextNodeData>) => {
  const { isEditing, startEditing, stopEditing } = useNodeEdit();
  const { updateNode } = useMindMapStore();
  const [isHovered, setIsHovered] = useState(false);
  const [text, setText] = useState(data.text || 'Double-click to edit');

  const handleDoubleClick = () => {
    startEditing(id, data);
  };

  const handleSave = () => {
    updateNode(id, {
      content: {
        ...data,
        text: text,
      },
    });
    stopEditing();
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSave();
    }
    if (e.key === 'Escape') {
      setText(data.text || 'Double-click to edit');
      stopEditing();
    }
  };

  // If this node is being edited, show inline editing
  if (isEditing(id)) {
    return (
      <Card
        className="min-w-[150px] transition-all duration-200 border-2 border-blue-500 shadow-xl"
        style={{
          backgroundColor: data.backgroundColor || 'white',
        }}
      >
        <Handle
          type="target"
          position={Position.Top}
          className="w-3 h-3 bg-blue-500 border-2 border-white"
        />

        <div className="p-3">
          <div className="flex items-center space-x-2 mb-2">
            <Type className="h-4 w-4 text-blue-600" />
            <span className="text-xs text-blue-600 font-medium">Editing</span>
          </div>

          <Input
            value={text}
            onChange={e => setText(e.target.value)}
            onBlur={handleSave}
            onKeyDown={handleKeyPress}
            autoFocus
            className="border-none p-0 focus:ring-0 bg-transparent"
            style={{
              fontSize: `${data.fontSize || 14}px`,
              color: data.color || '#000000',
            }}
          />
        </div>

        <Handle
          type="source"
          position={Position.Bottom}
          className="w-3 h-3 bg-blue-500 border-2 border-white"
        />
      </Card>
    );
  }

  return (
    <div
      className="relative"
      onDoubleClick={handleDoubleClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <Handle
        type="target"
        position={Position.Top}
        className="w-3 h-3 bg-blue-500 border-2 border-white"
      />

      <Card
        className={`min-w-[150px] transition-all duration-200 cursor-pointer ${
          selected
            ? 'ring-2 ring-blue-500 shadow-xl scale-105'
            : data.dragging
              ? 'shadow-2xl scale-110 rotate-1'
              : data.isConnectingFrom
                ? 'ring-2 ring-green-500 shadow-xl'
                : data.isConnecting
                  ? 'ring-1 ring-blue-300 hover:ring-2 hover:ring-blue-500'
                  : 'hover:shadow-lg hover:scale-102'
        }`}
        style={{
          backgroundColor: data.backgroundColor || 'white',
          border: '1px solid #e2e8f0',
        }}
      >
        <div className="p-4">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center space-x-2">
              <Type className="h-4 w-4 text-blue-600" />
              <span className="text-xs text-gray-500 font-medium">Text</span>
            </div>
            {isHovered && (
              <Edit3
                className="h-3 w-3 text-gray-400 hover:text-blue-500 cursor-pointer transition-colors duration-200"
                onClick={() => startEditing(id, data)}
              />
            )}
          </div>

          <div
            className="cursor-text min-h-[20px] leading-relaxed"
            style={{
              fontSize: `${data.fontSize || 14}px`,
              color: data.color || '#000000',
            }}
          >
            {text}
          </div>
        </div>
      </Card>

      <Handle
        type="source"
        position={Position.Bottom}
        className="w-3 h-3 bg-blue-500 border-2 border-white"
      />
    </div>
  );
});

TextNode.displayName = 'TextNode';
