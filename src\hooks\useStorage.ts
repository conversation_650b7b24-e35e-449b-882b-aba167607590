import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useCallback } from 'react';
import axios from 'axios';
import { toast } from 'sonner';
import { SubscriptionService, StorageTier } from '@/services/Subscription.service';

export interface StorageInfo {
  organizationId: string;
  totalLimit: number;
  totalUsed: number;
  availableStorage: number;
  usagePercentage: number;
  isOverLimit: boolean;
  freeStorageLimit: number;
  purchasedStorage: number;
}

export interface StorageBreakdown {
  _id: string;
  totalSize: number;
  fileCount: number;
}

export interface PurchaseInfo {
  storageMB: number;
  pricePerMB: number;
  totalPrice: number;
  storageBytes: number;
}

export interface StoragePurchase {
  _id: string;
  organizationId: string;
  purchasedBy: {
    name: string;
    email: string;
  };
  storageAmount: number;
  pricePerMB: number;
  totalPrice: number;
  currency: string;
  paymentStatus: string;
  paymentMethod: string;
  paymentId: string;
  orderId: string;
  purchaseType: string;
  description: string;
  createdAt: string;
}

export interface StorageQuotaInfo {
  basePlanStorage: number;
  additionalPurchasedStorage: number;
  totalAvailableStorage: number;
  usedStorage: number;
  availableStorage: number;
  usagePercentage: number;
  isOverQuota: boolean;
  daysInGracePeriod: number | null;
  gracePeriodEnd: Date | null;
  recommendedTier: StorageTier | null;
  currentTier: StorageTier | null;
  canUpgrade: boolean;
  canDowngrade: boolean;
}

export interface StorageUsageAnalytics {
  organizationId: string;
  period: string;
  startDate: Date;
  endDate: Date;
  totalUsage: number;
  averageDailyUsage: number;
  growthRate: number;
  topFileTypes: { type: string; size: number; count: number }[];
  topUsers: { userId: string; userName: string; usage: number }[];
  usageByResource: { resourceType: string; usage: number }[];
  predictions: {
    nextMonth: number;
    nextQuarter: number;
    nextYear: number;
  };
}

export interface StorageOptimizationRecommendations {
  recommendations: string[];
  potentialSavings: number;
  currentTier: StorageTier;
  recommendedTier: StorageTier;
}

export interface StorageForecast {
  growthRate: number;
  forecastData: {
    date: Date;
    projectedUsage: number;
    projectedUsageFormatted: string;
    recommendedTier: StorageTier;
  }[];
  recommendations: {
    shortTerm: any;
    longTerm: any;
  };
}

export interface CleanupRecommendation {
  type: string;
  files: {
    fileId: string;
    fileName: string;
    fileSize: number;
    lastAccessed: Date;
    resourceType: string;
    potentialSavings: number;
  }[];
  totalPotentialSavings: number;
  priority: 'low' | 'medium' | 'high';
  description: string;
}

export const useStorage = () => {
  const queryClient = useQueryClient();

  // Get storage information
  const {
    data: storageInfo,
    isLoading: isLoadingInfo,
    error: infoError,
    refetch: refetchStorageInfo,
  } = useQuery<StorageInfo>({
    queryKey: ['storage', 'info'],
    queryFn: async () => {
      const response = await axios.get('/api/storage/info');
      return response.data.data;
    },
    refetchInterval: 30000, // Refetch every 30 seconds
  });

  // Get storage breakdown
  const {
    data: storageBreakdown,
    isLoading: isLoadingBreakdown,
    error: breakdownError,
  } = useQuery<StorageBreakdown[]>({
    queryKey: ['storage', 'breakdown'],
    queryFn: async () => {
      const response = await axios.get('/api/storage/breakdown');
      return response.data.data;
    },
  });

  // Get purchase history
  const {
    data: purchaseHistory,
    isLoading: isLoadingHistory,
    error: historyError,
  } = useQuery<StoragePurchase[]>({
    queryKey: ['storage', 'purchases'],
    queryFn: async () => {
      const response = await axios.get('/api/storage/purchases');
      return response.data.data;
    },
  });

  // Calculate storage price
  const calculatePriceMutation = useMutation({
    mutationFn: async (storageMB: number): Promise<PurchaseInfo> => {
      const response = await axios.post('/api/storage/calculate-price', { storageMB });
      return response.data.data;
    },
  });

  // Purchase storage
  const purchaseStorageMutation = useMutation({
    mutationFn: async (purchaseData: {
      storageMB: number;
      paymentId: string;
      orderId: string;
      paymentMethod?: string;
    }) => {
      const response = await axios.post('/api/storage/purchase', purchaseData);
      return response.data;
    },
    onSuccess: () => {
      toast.success('Storage purchased successfully!');
      queryClient.invalidateQueries({ queryKey: ['storage'] });
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to purchase storage');
    },
  });

  // Check if file can be uploaded
  const checkUploadMutation = useMutation({
    mutationFn: async (fileSize: number) => {
      const response = await axios.post('/api/storage/check-upload', { fileSize });
      return response.data.data;
    },
  });

  // Get storage quota information
  const {
    data: storageQuotaInfo,
    isLoading: isLoadingQuotaInfo,
    error: quotaInfoError,
  } = useQuery<StorageQuotaInfo>({
    queryKey: ['storage', 'quota-info'],
    queryFn: async () => {
      if (!storageInfo?.organizationId) throw new Error('Organization ID is required');
      const response = await axios.get(
        `/api/subscription/storage-quota?organizationId=${storageInfo.organizationId}`
      );
      return response.data.data;
    },
    enabled: !!storageInfo?.organizationId,
    staleTime: 1000 * 60 * 5, // 5 minutes
  });

  // Get storage usage analytics
  const {
    data: storageUsageAnalytics,
    isLoading: isLoadingStorageAnalytics,
    error: analyticsError,
  } = useQuery<StorageUsageAnalytics>({
    queryKey: ['storage', 'usage-analytics'],
    queryFn: async () => {
      if (!storageInfo?.organizationId) throw new Error('Organization ID is required');
      const response = await axios.get(
        `/api/storage/analytics?organizationId=${storageInfo.organizationId}&period=month`
      );
      return response.data.data;
    },
    enabled: !!storageInfo?.organizationId,
    staleTime: 1000 * 60 * 15, // 15 minutes
  });

  // Get storage optimization recommendations
  const {
    data: storageOptimizationRecommendations,
    isLoading: isLoadingOptimizationRecommendations,
    error: optimizationError,
  } = useQuery<StorageOptimizationRecommendations>({
    queryKey: ['storage', 'optimization-recommendations'],
    queryFn: async () => {
      if (!storageInfo?.organizationId) throw new Error('Organization ID is required');
      const response = await axios.get(
        `/api/storage/quota-enforcement?organizationId=${storageInfo.organizationId}&recommendations=true`
      );
      return response.data.data.recommendations;
    },
    enabled: !!storageInfo?.organizationId,
    staleTime: 1000 * 60 * 30, // 30 minutes
  });

  // Get storage forecast
  const {
    data: storageForecast,
    isLoading: isLoadingStorageForecast,
    error: forecastError,
  } = useQuery<StorageForecast>({
    queryKey: ['storage', 'forecast'],
    queryFn: async () => {
      if (!storageInfo?.organizationId) throw new Error('Organization ID is required');
      const response = await axios.get(
        `/api/storage/forecast?organizationId=${storageInfo.organizationId}&months=6`
      );
      return response.data.data;
    },
    enabled: !!storageInfo?.organizationId,
    staleTime: 1000 * 60 * 60, // 1 hour
  });

  // Get cleanup recommendations
  const {
    data: cleanupRecommendations,
    isLoading: isLoadingCleanupRecommendations,
    error: cleanupError,
  } = useQuery<CleanupRecommendation[]>({
    queryKey: ['storage', 'cleanup-recommendations'],
    queryFn: async () => {
      if (!storageInfo?.organizationId) throw new Error('Organization ID is required');
      const response = await axios.get(
        `/api/storage/quota-enforcement?organizationId=${storageInfo.organizationId}&cleanup=true`
      );
      return response.data.data.cleanupSuggestions;
    },
    enabled: !!storageInfo?.organizationId,
    staleTime: 1000 * 60 * 30, // 30 minutes
  });

  // Mutations for quota management
  const triggerCleanupMutation = useMutation({
    mutationFn: async ({
      organizationId,
      cleanupMode = 'safe',
    }: {
      organizationId: string;
      cleanupMode?: 'safe' | 'aggressive' | 'preview';
    }) => {
      const response = await axios.post('/api/storage/quota-enforcement', {
        action: 'trigger_cleanup',
        cleanupMode,
        organizationId,
      });
      return response.data.data;
    },
    onSuccess: () => {
      toast.success('Storage cleanup initiated successfully');
      queryClient.invalidateQueries({ queryKey: ['storage'] });
    },
  });

  const requestEmergencyAllocationMutation = useMutation({
    mutationFn: async (organizationId: string) => {
      const response = await axios.post('/api/storage/quota-enforcement', {
        action: 'request_emergency_allocation',
        requestEmergencyAllocation: true,
        organizationId,
      });
      return response.data.data;
    },
    onSuccess: () => {
      toast.success('Emergency storage allocation granted');
      queryClient.invalidateQueries({ queryKey: ['storage'] });
    },
  });

  const setStorageAlertSettingsMutation = useMutation({
    mutationFn: async ({ organizationId, settings }: { organizationId: string; settings: any }) => {
      const response = await axios.put('/api/storage/quota-enforcement', {
        notificationSettings: settings,
        organizationId,
      });
      return response.data;
    },
    onSuccess: () => {
      toast.success('Storage alert settings updated');
    },
  });

  // Utility functions
  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatMB = (bytes: number): number => {
    return Math.round((bytes / (1024 * 1024)) * 100) / 100;
  };

  const getStorageColor = (percentage: number): string => {
    if (percentage >= 90) return 'text-red-500';
    if (percentage >= 75) return 'text-yellow-500';
    return 'text-green-500';
  };

  const getStorageStatus = (percentage: number): string => {
    if (percentage >= 100) return 'Over Limit';
    if (percentage >= 90) return 'Critical';
    if (percentage >= 75) return 'Warning';
    return 'Good';
  };

  // Additional utility functions for tier management
  const recommendStorageTier = useCallback(
    (currentUsage: number, growthRate: number = 0.1): StorageTier => {
      // Project usage for the next 3 months with the given growth rate
      const projectedUsage = currentUsage * (1 + growthRate * 3);
      return SubscriptionService.calculateRecommendedTier(projectedUsage);
    },
    []
  );

  // --- PRODUCTION-READY CALLBACKS ---

  /**
   * Calculate price for storage purchase
   * @param storageMB Amount of storage in MB
   * @returns PurchaseInfo or null
   */
  const calculatePrice = useCallback(
    async (storageMB: number): Promise<PurchaseInfo | null> => {
      if (!storageMB || storageMB <= 0) {
        toast.error('Storage amount must be greater than 0');
        return null;
      }
      try {
        return await calculatePriceMutation.mutateAsync(storageMB);
      } catch (error: any) {
        toast.error(error?.response?.data?.error || 'Failed to calculate price');
        return null;
      }
    },
    [calculatePriceMutation]
  );

  /**
   * Purchase storage
   * @param purchaseData { storageMB, paymentId, orderId, paymentMethod }
   */
  const purchaseStorage = useCallback(
    async (purchaseData: {
      storageMB: number;
      paymentId: string;
      orderId: string;
      paymentMethod?: string;
    }) => {
      if (!purchaseData?.storageMB || !purchaseData?.paymentId || !purchaseData?.orderId) {
        toast.error('Missing required purchase data');
        return null;
      }
      try {
        return await purchaseStorageMutation.mutateAsync(purchaseData);
      } catch (error: any) {
        toast.error(error?.response?.data?.error || 'Failed to purchase storage');
        return null;
      }
    },
    [purchaseStorageMutation]
  );

  /**
   * Check if a file can be uploaded
   * @param fileSize File size in bytes
   */
  const checkCanUpload = useCallback(
    async (fileSize: number) => {
      if (!fileSize || fileSize <= 0) {
        toast.error('File size must be greater than 0');
        return null;
      }
      try {
        return await checkUploadMutation.mutateAsync(fileSize);
      } catch (error: any) {
        toast.error(error?.response?.data?.error || 'Failed to check upload permission');
        return null;
      }
    },
    [checkUploadMutation]
  );

  /**
   * Get storage quota info for a specific organization
   * @param organizationId
   */
  const getStorageQuotaInfo = useCallback(async (organizationId: string) => {
    if (!organizationId) {
      toast.error('Organization ID is required');
      return null;
    }
    try {
      const response = await axios.get(
        `/api/subscription/storage-quota?organizationId=${organizationId}`
      );
      return response.data.data as StorageQuotaInfo;
    } catch (error: any) {
      toast.error(error?.response?.data?.error || 'Failed to fetch storage quota information');
      return null;
    }
  }, []);

  /**
   * Get storage usage analytics for a specific organization and period
   * @param organizationId
   * @param period
   */
  const getStorageUsageAnalytics = useCallback(
    async (organizationId: string, period: 'day' | 'week' | 'month' | 'year' = 'month') => {
      if (!organizationId) {
        toast.error('Organization ID is required');
        return null;
      }
      try {
        const response = await axios.get(
          `/api/storage/analytics?organizationId=${organizationId}&period=${period}`
        );
        return response.data.data as StorageUsageAnalytics;
      } catch (error: any) {
        toast.error(error?.response?.data?.error || 'Failed to fetch storage usage analytics');
        return null;
      }
    },
    []
  );

  /**
   * Get storage optimization recommendations for a specific organization
   * @param organizationId
   */
  const getStorageOptimizationRecommendations = useCallback(async (organizationId: string) => {
    if (!organizationId) {
      toast.error('Organization ID is required');
      return null;
    }
    try {
      const response = await axios.get(
        `/api/storage/quota-enforcement?organizationId=${organizationId}&recommendations=true`
      );
      return response.data.data.recommendations as StorageOptimizationRecommendations;
    } catch (error: any) {
      toast.error(
        error?.response?.data?.error || 'Failed to fetch storage optimization recommendations'
      );
      return null;
    }
  }, []);

  /**
   * Get storage forecast for a specific organization and months
   * @param organizationId
   * @param months
   */
  const getStorageForecast = useCallback(async (organizationId: string, months: number = 6) => {
    if (!organizationId) {
      toast.error('Organization ID is required');
      return null;
    }
    try {
      const response = await axios.get(
        `/api/storage/forecast?organizationId=${organizationId}&months=${months}`
      );
      return response.data.data as StorageForecast;
    } catch (error: any) {
      toast.error(error?.response?.data?.error || 'Failed to fetch storage forecast');
      return null;
    }
  }, []);

  /**
   * Get cleanup recommendations for a specific organization
   * @param organizationId
   */
  const getCleanupRecommendations = useCallback(async (organizationId: string) => {
    if (!organizationId) {
      toast.error('Organization ID is required');
      return null;
    }
    try {
      const response = await axios.get(
        `/api/storage/quota-enforcement?organizationId=${organizationId}&cleanup=true`
      );
      return response.data.data.cleanupSuggestions as CleanupRecommendation[];
    } catch (error: any) {
      toast.error(error?.response?.data?.error || 'Failed to fetch cleanup recommendations');
      return null;
    }
  }, []);

  /**
   * Trigger storage cleanup for a specific organization
   * @param organizationId
   * @param cleanupMode
   */
  const triggerCleanup = useCallback(
    async (organizationId: string, cleanupMode: 'safe' | 'aggressive' | 'preview' = 'safe') => {
      if (!organizationId) {
        toast.error('Organization ID is required');
        return null;
      }
      try {
        return await triggerCleanupMutation.mutateAsync({ organizationId, cleanupMode });
      } catch (error: any) {
        toast.error(error?.response?.data?.error || 'Failed to trigger storage cleanup');
        return null;
      }
    },
    [triggerCleanupMutation]
  );

  /**
   * Request emergency storage allocation for a specific organization
   * @param organizationId
   */
  const requestEmergencyAllocation = useCallback(
    async (organizationId: string) => {
      if (!organizationId) {
        toast.error('Organization ID is required');
        return null;
      }
      try {
        return await requestEmergencyAllocationMutation.mutateAsync(organizationId);
      } catch (error: any) {
        toast.error(
          error?.response?.data?.error || 'Failed to request emergency storage allocation'
        );
        return null;
      }
    },
    [requestEmergencyAllocationMutation]
  );

  /**
   * Set storage alert settings for a specific organization
   * @param organizationId
   * @param settings
   */
  const setStorageAlertSettings = useCallback(
    async (organizationId: string, settings: any) => {
      if (!organizationId) {
        toast.error('Organization ID is required');
        return null;
      }
      if (!settings) {
        toast.error('Settings are required');
        return null;
      }
      try {
        return await setStorageAlertSettingsMutation.mutateAsync({ organizationId, settings });
      } catch (error: any) {
        toast.error(error?.response?.data?.error || 'Failed to update storage alert settings');
        return null;
      }
    },
    [setStorageAlertSettingsMutation]
  );

  return {
    // Data
    storageInfo,
    storageBreakdown,
    purchaseHistory,
    storageQuotaInfo,
    storageUsageAnalytics,
    storageOptimizationRecommendations,
    storageForecast,
    cleanupRecommendations,

    // Loading states
    isLoadingInfo,
    isLoadingBreakdown,
    isLoadingHistory,
    isLoadingQuotaInfo,
    isLoadingStorageAnalytics,
    isLoadingOptimizationRecommendations,
    isLoadingStorageForecast,
    isLoadingCleanupRecommendations,
    isCalculatingPrice: calculatePriceMutation.isPending,
    isPurchasing: purchaseStorageMutation.isPending,
    isCheckingUpload: checkUploadMutation.isPending,
    isTriggering: triggerCleanupMutation.isPending,
    isRequestingAllocation: requestEmergencyAllocationMutation.isPending,
    isSettingAlerts: setStorageAlertSettingsMutation.isPending,

    // Errors
    infoError,
    breakdownError,
    historyError,
    quotaInfoError,
    analyticsError,
    optimizationError,
    forecastError,
    cleanupError,

    // Actions
    calculatePrice,
    purchaseStorage,
    checkCanUpload,
    refetchStorageInfo,
    getStorageQuotaInfo,
    getStorageUsageAnalytics,
    getStorageOptimizationRecommendations,
    getStorageForecast,
    getCleanupRecommendations,
    triggerCleanup,
    requestEmergencyAllocation,
    setStorageAlertSettings,

    // Utilities
    formatBytes,
    formatMB,
    getStorageColor,
    getStorageStatus,
    recommendStorageTier,
  };
};
