import axios from 'axios';

export interface AnalyticsOverview {
  totalTasks: number;
  completedTasks: number;
  inProgressTasks: number;
  overdueTasks: number;
  totalLoggedTime: number;
  totalEstimatedTime: number;
  completionRate: number;
  productivityRate: number;
  totalUsers: number;
  activeProjects: number;
}

export interface TeamMemberPerformance {
  user: {
    id: string;
    name: string;
    email: string;
    image?: string;
  };
  metrics: {
    totalTasks: number;
    completedTasks: number;
    completionRate: number;
    totalLoggedTime: number;
    totalEstimatedTime: number;
    efficiency: number;
    weeklyHours: number;
    averageTasksPerWeek: number;
  };
}

export interface TimeTrackingData {
  dailyTracking: Array<{
    date: string;
    hours: number;
  }>;
  userTracking: Array<{
    name: string;
    hours: number;
  }>;
  totalHours: number;
}

export interface ProjectAnalytics {
  id: string;
  name: string;
  status: string;
  priority: string;
  createdBy: string;
  totalTasks: number;
  completedTasks: number;
  completionRate: number;
  totalLoggedTime: number;
  totalEstimatedTime: number;
  createdAt: string;
}

export interface AdvancedMetrics {
  dateRange: {
    startDate: Date;
    endDate: Date;
  };
  overview: {
    totalTasks: number;
    completedTasks: number;
    completionRate: number;
    totalLoggedTime: number;
    averageTaskDuration: number;
    productivityScore: number;
  };
  trends: {
    taskCompletionTrend: Array<{
      date: string;
      completed: number;
      created: number;
    }>;
    timeTrackingTrend: Array<{
      date: string;
      hours: number;
      efficiency: number;
    }>;
    productivityTrend: Array<{
      date: string;
      score: number;
    }>;
  };
  comparisons: {
    previousPeriod: {
      completionRate: number;
      change: number;
    };
    teamAverage: {
      completionRate: number;
      comparison: number;
    };
  };
}

export interface TeamProductivityTrends {
  overallTrends: {
    productivity: Array<{
      date: string;
      score: number;
      tasks: number;
      hours: number;
    }>;
    collaboration: Array<{
      date: string;
      score: number;
      sharedTasks: number;
      communications: number;
    }>;
    efficiency: Array<{
      date: string;
      score: number;
      averageTaskTime: number;
      onTimeCompletion: number;
    }>;
  };
  individualTrends: Array<{
    userId: string;
    name: string;
    trends: {
      productivity: Array<{
        date: string;
        score: number;
      }>;
      workload: Array<{
        date: string;
        hours: number;
        tasks: number;
      }>;
      quality: Array<{
        date: string;
        score: number;
        defects: number;
      }>;
    };
  }>;
  teamMetrics: {
    averageProductivity: number;
    totalCollaboration: number;
    overallEfficiency: number;
    burnoutRisk: Array<{
      userId: string;
      name: string;
      riskLevel: 'low' | 'medium' | 'high';
      factors: string[];
    }>;
  };
}

export interface ProjectPerformanceMetrics {
  project: {
    id: string;
    name: string;
    status: string;
    priority: string;
    startDate: Date;
    endDate?: Date;
  };
  performance: {
    overallHealth: number;
    scheduleHealth: number;
    budgetHealth: number;
    qualityHealth: number;
    teamHealth: number;
  };
  timeline: {
    plannedDuration: number;
    actualDuration: number;
    remainingDuration: number;
    milestones: Array<{
      id: string;
      name: string;
      plannedDate: Date;
      actualDate?: Date;
      status: 'completed' | 'in_progress' | 'delayed' | 'at_risk';
    }>;
  };
  resources: {
    teamUtilization: number;
    budgetUtilization: number;
    resourceConstraints: string[];
  };
  quality: {
    defectRate: number;
    reworkRate: number;
    customerSatisfaction: number;
    codeQuality?: number;
  };
  risks: Array<{
    id: string;
    description: string;
    probability: number;
    impact: number;
    mitigation: string;
    status: 'open' | 'mitigated' | 'closed';
  }>;
}

export interface TimeTrackingDetails {
  summary: {
    totalTime: number;
    billableTime: number;
    nonBillableTime: number;
    overtimeHours: number;
    utilization: number;
  };
  breakdown: {
    byProject: Array<{
      projectId: string;
      projectName: string;
      totalTime: number;
      billableTime: number;
      tasks: Array<{
        taskId: string;
        taskName: string;
        timeSpent: number;
        efficiency: number;
      }>;
    }>;
    byUser: Array<{
      userId: string;
      userName: string;
      totalTime: number;
      billableTime: number;
      efficiency: number;
      workPattern: {
        averageHoursPerDay: number;
        peakHours: string[];
        workDays: number;
      };
    }>;
    byCategory: Array<{
      category: string;
      totalTime: number;
      percentage: number;
    }>;
  };
  analytics: {
    productivityTrends: Array<{
      date: string;
      totalHours: number;
      productiveHours: number;
      efficiency: number;
    }>;
    timeDistribution: {
      development: number;
      meetings: number;
      documentation: number;
      testing: number;
      other: number;
    };
    insights: {
      mostProductiveHours: string[];
      averageTaskDuration: number;
      timeWasters: string[];
      recommendations: string[];
    };
  };
}

export interface BudgetAnalytics {
  overview: {
    totalBudget: number;
    spentAmount: number;
    remainingBudget: number;
    utilizationRate: number;
    projectedSpend: number;
    variance: number;
  };
  breakdown: {
    byProject: Array<{
      projectId: string;
      projectName: string;
      budget: number;
      spent: number;
      remaining: number;
      utilization: number;
      status: 'on_track' | 'over_budget' | 'under_budget';
    }>;
    byCategory: Array<{
      category: string;
      budget: number;
      spent: number;
      percentage: number;
    }>;
    byTimeframe: Array<{
      period: string;
      budgeted: number;
      actual: number;
      variance: number;
    }>;
  };
  forecasting: {
    projectedCompletion: Date;
    estimatedFinalCost: number;
    costVariance: number;
    riskFactors: string[];
  };
  alerts: Array<{
    type: 'budget_exceeded' | 'spending_rate_high' | 'category_overspend';
    message: string;
    severity: 'low' | 'medium' | 'high';
    projectId?: string;
    category?: string;
  }>;
}

export interface MilestoneAnalytics {
  overview: {
    totalMilestones: number;
    completedMilestones: number;
    delayedMilestones: number;
    upcomingMilestones: number;
    completionRate: number;
    averageDelay: number;
  };
  timeline: {
    milestones: Array<{
      id: string;
      name: string;
      projectId: string;
      projectName: string;
      plannedDate: Date;
      actualDate?: Date;
      status: 'completed' | 'in_progress' | 'delayed' | 'at_risk';
      progress: number;
      delay: number;
      impact: 'low' | 'medium' | 'high';
    }>;
    criticalPath: string[];
  };
  performance: {
    onTimeDelivery: number;
    earlyDelivery: number;
    lateDelivery: number;
    averageLeadTime: number;
    predictability: number;
  };
  trends: {
    completionTrends: Array<{
      date: string;
      planned: number;
      actual: number;
      cumulative: number;
    }>;
    delayTrends: Array<{
      date: string;
      averageDelay: number;
      delayedCount: number;
    }>;
  };
  insights: {
    frequentDelayReasons: Array<{
      reason: string;
      count: number;
    }>;
    riskFactors: string[];
    recommendations: string[];
  };
}

export interface CustomReportData {
  metadata: {
    reportName: string;
    generatedAt: Date;
    dateRange: {
      startDate: Date;
      endDate: Date;
    };
    parameters: Record<string, any>;
  };
  data: Record<string, any>;
  aggregations: {
    totals: Record<string, number>;
    averages: Record<string, number>;
    percentages: Record<string, number>;
  };
  visualizations: Array<{
    type: 'chart' | 'table' | 'metric';
    title: string;
    data: any;
    config: Record<string, any>;
  }>;
}

export class AnalyticsService {
  static async getOverview(): Promise<AnalyticsOverview> {
    try {
      const response = await axios.get('/api/analytics/overview');
      return response.data.overview;
    } catch (error) {
      throw new Error('Failed to fetch analytics overview');
    }
  }

  static async getTeamPerformance(): Promise<TeamMemberPerformance[]> {
    try {
      const response = await axios.get('/api/analytics/team-performance');
      return response.data.teamPerformance;
    } catch (error) {
      throw new Error('Failed to fetch team performance data');
    }
  }

  static async getTimeTracking(): Promise<TimeTrackingData> {
    try {
      const response = await axios.get('/api/analytics/time-tracking');
      return response.data;
    } catch (error) {
      throw new Error('Failed to fetch time tracking data');
    }
  }

  static async getProjectAnalytics(): Promise<ProjectAnalytics[]> {
    try {
      const response = await axios.get('/api/analytics/projects');
      return response.data.projects;
    } catch (error) {
      throw new Error('Failed to fetch project analytics');
    }
  }

  /**
   * Gets advanced metrics with custom date ranges
   * @param dateRange Custom date range parameters
   * @param filters Optional filters for data refinement
   * @returns Advanced metrics data
   */
  static async getAdvancedMetrics(
    dateRange: { startDate: Date; endDate: Date },
    filters?: {
      projectIds?: string[];
      userIds?: string[];
      categories?: string[];
      includeArchived?: boolean;
    }
  ): Promise<AdvancedMetrics> {
    try {
      const response = await axios.post('/api/analytics/advanced-metrics', {
        dateRange,
        filters: filters || {},
      });
      return response.data.metrics;
    } catch (error: any) {
      throw new Error(`Failed to fetch advanced metrics: ${error.message}`);
    }
  }

  /**
   * Generates custom report data based on configuration
   * @param reportConfig Report configuration parameters
   * @returns Custom report data
   */
  static async getReportData(reportConfig: {
    name: string;
    dateRange: { startDate: Date; endDate: Date };
    metrics: string[];
    groupBy?: string[];
    filters?: Record<string, any>;
    aggregations?: string[];
  }): Promise<CustomReportData> {
    try {
      const response = await axios.post('/api/analytics/custom-report', reportConfig);
      return response.data;
    } catch (error: any) {
      throw new Error(`Failed to generate custom report: ${error.message}`);
    }
  }

  /**
   * Gets detailed team productivity trends
   * @param timeframe Timeframe for trend analysis
   * @param filters Optional filters for team analysis
   * @returns Team productivity trends
   */
  static async getTeamProductivityTrends(
    timeframe: 'week' | 'month' | 'quarter' | 'year' = 'month',
    filters?: {
      teamIds?: string[];
      departmentIds?: string[];
      projectIds?: string[];
      includeIndividuals?: boolean;
    }
  ): Promise<TeamProductivityTrends> {
    try {
      const response = await axios.post('/api/analytics/team-productivity-trends', {
        timeframe,
        filters: filters || {},
      });
      return response.data.trends;
    } catch (error: any) {
      throw new Error(`Failed to fetch team productivity trends: ${error.message}`);
    }
  }

  /**
   * Gets comprehensive project performance metrics
   * @param projectId Project ID for analysis
   * @param timeframe Optional timeframe for analysis
   * @returns Project performance metrics
   */
  static async getProjectPerformanceMetrics(
    projectId: string,
    timeframe?: 'week' | 'month' | 'quarter' | 'all'
  ): Promise<ProjectPerformanceMetrics> {
    try {
      const params = timeframe ? `?timeframe=${timeframe}` : '';
      const response = await axios.get(`/api/analytics/project-performance/${projectId}${params}`);
      return response.data.performance;
    } catch (error: any) {
      throw new Error(`Failed to fetch project performance metrics: ${error.message}`);
    }
  }

  /**
   * Gets detailed time tracking data with granular analysis
   * @param filters Filter parameters for time tracking
   * @returns Detailed time tracking data
   */
  static async getTimeTrackingDetails(filters?: {
    dateRange?: { startDate: Date; endDate: Date };
    projectIds?: string[];
    userIds?: string[];
    includeBreakdown?: boolean;
    includeAnalytics?: boolean;
    groupBy?: 'project' | 'user' | 'category' | 'date';
  }): Promise<TimeTrackingDetails> {
    try {
      const response = await axios.post('/api/analytics/time-tracking-details', {
        filters: filters || {},
      });
      return response.data.timeTracking;
    } catch (error: any) {
      throw new Error(`Failed to fetch time tracking details: ${error.message}`);
    }
  }

  /**
   * Gets comprehensive budget analytics
   * @param projectId Optional project ID for project-specific analytics
   * @param filters Optional filters for budget analysis
   * @returns Budget analytics data
   */
  static async getBudgetAnalytics(
    projectId?: string,
    filters?: {
      dateRange?: { startDate: Date; endDate: Date };
      categories?: string[];
      includeForecasting?: boolean;
      includeAlerts?: boolean;
    }
  ): Promise<BudgetAnalytics> {
    try {
      const url = projectId
        ? `/api/analytics/budget-analytics/${projectId}`
        : '/api/analytics/budget-analytics';

      const response = await axios.post(url, {
        filters: filters || {},
      });
      return response.data.budgetAnalytics;
    } catch (error: any) {
      throw new Error(`Failed to fetch budget analytics: ${error.message}`);
    }
  }

  /**
   * Gets milestone analytics and performance data
   * @param projectId Optional project ID for project-specific milestone analytics
   * @param filters Optional filters for milestone analysis
   * @returns Milestone analytics data
   */
  static async getMilestoneAnalytics(
    projectId?: string,
    filters?: {
      dateRange?: { startDate: Date; endDate: Date };
      status?: string[];
      includeTimeline?: boolean;
      includeInsights?: boolean;
    }
  ): Promise<MilestoneAnalytics> {
    try {
      const url = projectId
        ? `/api/analytics/milestone-analytics/${projectId}`
        : '/api/analytics/milestone-analytics';

      const response = await axios.post(url, {
        filters: filters || {},
      });
      return response.data.milestoneAnalytics;
    } catch (error: any) {
      throw new Error(`Failed to fetch milestone analytics: ${error.message}`);
    }
  }

  /**
   * Gets performance benchmarks for comparison
   * @param type Type of benchmark to retrieve
   * @param filters Optional filters for benchmark data
   * @returns Benchmark data
   */
  static async getPerformanceBenchmarks(
    type: 'industry' | 'organization' | 'team' | 'project',
    filters?: {
      category?: string;
      size?: string;
      region?: string;
    }
  ): Promise<any> {
    try {
      const response = await axios.post('/api/analytics/benchmarks', {
        type,
        filters: filters || {},
      });
      return response.data.benchmarks;
    } catch (error: any) {
      throw new Error(`Failed to fetch performance benchmarks: ${error.message}`);
    }
  }

  /**
   * Gets predictive analytics and forecasting data
   * @param type Type of prediction to generate
   * @param parameters Parameters for prediction model
   * @returns Predictive analytics data
   */
  static async getPredictiveAnalytics(
    type: 'project_completion' | 'budget_forecast' | 'resource_demand' | 'risk_assessment',
    parameters: {
      projectId?: string;
      timeHorizon?: number;
      confidence?: number;
      factors?: string[];
    }
  ): Promise<any> {
    try {
      const response = await axios.post('/api/analytics/predictive', {
        type,
        parameters,
      });
      return response.data.predictions;
    } catch (error: any) {
      throw new Error(`Failed to fetch predictive analytics: ${error.message}`);
    }
  }

  /**
   * Exports analytics data in various formats
   * @param dataType Type of data to export
   * @param format Export format
   * @param filters Optional filters for export
   * @returns Export data or download URL
   */
  static async exportAnalyticsData(
    dataType: 'overview' | 'team_performance' | 'project_analytics' | 'time_tracking',
    format: 'json' | 'csv' | 'excel' | 'pdf',
    filters?: Record<string, any>
  ): Promise<{ downloadUrl?: string; data?: any }> {
    try {
      const response = await axios.post(
        '/api/analytics/export',
        {
          dataType,
          format,
          filters: filters || {},
        },
        {
          responseType: format === 'json' ? 'json' : 'blob',
        }
      );

      if (format === 'json') {
        return { data: response.data };
      } else {
        const blob = new Blob([response.data]);
        const downloadUrl = URL.createObjectURL(blob);
        return { downloadUrl };
      }
    } catch (error: any) {
      throw new Error(`Failed to export analytics data: ${error.message}`);
    }
  }

  /**
   * Gets real-time analytics dashboard data
   * @param refreshInterval Optional refresh interval in seconds
   * @returns Real-time dashboard data
   */
  static async getRealTimeAnalytics(refreshInterval?: number): Promise<any> {
    try {
      const params = refreshInterval ? `?refresh=${refreshInterval}` : '';
      const response = await axios.get(`/api/analytics/realtime${params}`);
      return response.data;
    } catch (error: any) {
      throw new Error(`Failed to fetch real-time analytics: ${error.message}`);
    }
  }

  /**
   * Clears analytics cache for fresh data retrieval
   * @param cacheType Optional specific cache type to clear
   * @returns Success status
   */
  static async clearAnalyticsCache(cacheType?: string): Promise<{ success: boolean }> {
    try {
      const response = await axios.post('/api/analytics/clear-cache', {
        cacheType: cacheType || 'all',
      });
      return response.data;
    } catch (error: any) {
      throw new Error(`Failed to clear analytics cache: ${error.message}`);
    }
  }
}
